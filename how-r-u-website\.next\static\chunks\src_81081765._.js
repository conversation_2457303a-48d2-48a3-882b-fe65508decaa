(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/DreamyLogo.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "DreamyLogo": (()=>DreamyLogo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function DreamyLogo({ onLetterClick }) {
    _s();
    const [isHovered, setIsHovered] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [clickSequence, setClickSequence] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [activeLetters, setActiveLetters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        false,
        false,
        false,
        false,
        false
    ]);
    const logoRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const letters = [
        'h',
        'o',
        'w',
        'r',
        'u'
    ];
    const secretSequence = [
        0,
        4,
        1,
        3,
        2
    ]; // h-u-o-r-w
    const handleLetterClick = (letter, index)=>{
        const newSequence = [
            ...clickSequence,
            index
        ];
        setClickSequence(newSequence);
        // Activate letter with dreamy glow
        const newActiveLetters = [
            ...activeLetters
        ];
        newActiveLetters[index] = true;
        setActiveLetters(newActiveLetters);
        // Reset after 3 seconds
        setTimeout(()=>{
            const resetLetters = [
                ...activeLetters
            ];
            resetLetters[index] = false;
            setActiveLetters(resetLetters);
        }, 3000);
        // Check if sequence matches secret pattern
        if (newSequence.length === secretSequence.length) {
            const isCorrect = newSequence.every((val, i)=>val === secretSequence[i]);
            if (isCorrect) {
                onLetterClick?.('secret', -1);
            }
            setTimeout(()=>setClickSequence([]), 1000);
        }
        onLetterClick?.(letter, index);
    };
    const letterVariants = {
        initial: {
            opacity: 0.7,
            scale: 1,
            rotateX: 0,
            rotateY: 0,
            z: 0
        },
        hover: {
            opacity: 1,
            scale: 1.1,
            rotateX: 10,
            rotateY: 5,
            z: 20,
            transition: {
                duration: 0.6,
                ease: "easeOut"
            }
        },
        click: {
            opacity: [
                1,
                0.3,
                1
            ],
            scale: [
                1,
                0.8,
                1.2,
                1
            ],
            rotateX: [
                0,
                15,
                -10,
                0
            ],
            rotateY: [
                0,
                -15,
                10,
                0
            ],
            transition: {
                duration: 0.8,
                ease: "easeInOut"
            }
        },
        active: {
            opacity: 1,
            scale: 1.05,
            filter: 'brightness(1.5) blur(0.5px)',
            textShadow: '0 0 20px rgba(255, 255, 255, 0.8)'
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative perspective-1000",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "absolute inset-0 -m-20",
                animate: {
                    background: [
                        'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',
                        'radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%)',
                        'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)'
                    ]
                },
                transition: {
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                }
            }, void 0, false, {
                fileName: "[project]/src/components/DreamyLogo.tsx",
                lineNumber: 81,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "relative transform-gpu",
                onMouseEnter: ()=>setIsHovered(true),
                onMouseLeave: ()=>setIsHovered(false),
                animate: {
                    rotateX: isHovered ? 5 : 0,
                    rotateY: isHovered ? 5 : 0,
                    scale: isHovered ? 1.05 : 1
                },
                transition: {
                    duration: 0.6,
                    ease: "easeOut"
                },
                style: {
                    transformStyle: 'preserve-3d',
                    perspective: '1000px'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                        ref: logoRef,
                        width: "266",
                        height: "275",
                        viewBox: "0 0 266 275",
                        fill: "none",
                        xmlns: "http://www.w3.org/2000/svg",
                        className: "w-80 h-auto cursor-pointer filter drop-shadow-2xl",
                        style: {
                            filter: isHovered ? 'brightness(1.3) drop-shadow(0 0 30px rgba(255, 255, 255, 0.5))' : 'brightness(1) drop-shadow(0 0 15px rgba(255, 255, 255, 0.2))',
                            transition: 'filter 0.6s ease'
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                            id: "howru",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].path, {
                                    id: "h",
                                    d: "M3.55881 1.54838C6.09211 -0.984916 9.69161 -0.318516 11.425 2.88138C12.4912 4.88138 12.7582 11.8147 11.9582 27.4148C10.6249 58.7478 11.1585 60.2148 23.1582 54.3478C34.3582 48.8818 45.6912 51.5478 51.9582 61.1478C56.4912 67.8148 59.5592 83.0148 59.8252 99.4148C59.9592 111.148 59.5582 113.415 57.4252 115.815C55.9582 117.415 53.5582 118.615 52.0922 118.348C49.5582 117.948 49.2912 117.015 49.4252 109.948C49.5582 105.548 49.6922 97.1478 49.8252 91.2818C50.2252 77.6818 47.9582 71.0148 42.0922 67.0148C37.0252 63.5488 27.2912 62.8808 22.4912 65.6808C14.2252 70.3478 11.4255 78.4818 9.29221 103.282C8.89221 108.348 8.09211 113.681 7.82541 114.882C7.02541 117.815 1.29151 118.081 0.224813 115.415C-0.175087 114.346 -0.0413892 99.2798 0.625211 81.8148C1.29191 64.2148 1.82491 39.4148 1.95821 26.4808C1.95821 10.2147 2.49211 2.61508 3.55881 1.54838Z",
                                    fill: activeLetters[0] ? "rgba(255, 255, 255, 0.9)" : "rgba(255, 255, 255, 0.7)",
                                    variants: letterVariants,
                                    initial: "initial",
                                    whileHover: "hover",
                                    whileTap: "click",
                                    animate: activeLetters[0] ? "active" : "initial",
                                    onClick: ()=>handleLetterClick('h', 0),
                                    className: "cursor-pointer animate-letter-dance",
                                    style: {
                                        animationDelay: '0s',
                                        transformOrigin: 'center',
                                        filter: activeLetters[0] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/DreamyLogo.tsx",
                                    lineNumber: 126,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].path, {
                                    id: "o",
                                    d: "M137.425 21.4154C139.025 21.2821 142.225 22.6156 144.759 24.3486C147.159 25.9496 151.025 29.9486 153.292 33.1486C156.759 38.2156 157.292 39.8156 157.158 47.4156C156.358 81.1486 126.091 101.816 107.691 80.8826C100.492 72.6156 98.8922 56.6156 104.092 44.0826C106.758 37.6826 114.891 28.0826 119.158 26.3486C124.491 24.0826 134.358 21.4155 137.425 21.4154ZM139.825 32.7496C131.959 30.0826 129.025 30.0826 123.958 32.7496C114.092 37.8166 108.625 47.2826 108.625 59.0156C108.625 67.9496 111.025 74.7496 115.425 77.8156C119.158 80.3496 129.292 80.6156 133.559 78.2156C139.292 75.0146 145.825 65.6826 147.958 57.8156C150.491 48.2156 150.492 45.0156 148.092 39.4156C146.625 35.9486 144.892 34.4826 139.825 32.7496Z",
                                    fill: activeLetters[1] ? "rgba(255, 255, 255, 0.9)" : "rgba(255, 255, 255, 0.7)",
                                    variants: letterVariants,
                                    initial: "initial",
                                    whileHover: "hover",
                                    whileTap: "click",
                                    animate: activeLetters[1] ? "active" : "initial",
                                    onClick: ()=>handleLetterClick('o', 1),
                                    className: "cursor-pointer animate-dreamy-float",
                                    style: {
                                        animationDelay: '1s',
                                        transformOrigin: 'center',
                                        filter: activeLetters[1] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/DreamyLogo.tsx",
                                    lineNumber: 145,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].path, {
                                    id: "w",
                                    d: "M219.959 33.2824C222.892 24.3494 225.426 21.9491 228.226 25.5494C229.026 26.4824 229.959 31.8154 230.359 37.2824C231.026 47.6824 236.093 64.7494 238.76 65.8154C241.159 66.7484 242.36 65.1484 248.359 52.7494C256.626 35.9494 260.226 30.6154 263.293 30.6154C264.759 30.6154 265.959 31.4154 265.959 32.4814C265.959 35.6814 260.76 46.8824 255.293 55.2824C252.493 59.6824 248.626 67.2814 246.626 72.2154C243.026 81.1484 239.159 86.2154 236.893 84.7494C236.226 84.3494 234.626 81.1484 233.426 77.6814C232.092 74.2154 230.226 69.1484 229.293 66.6154C228.226 64.0824 226.759 59.4154 225.959 56.2154C225.159 52.8824 223.693 50.6154 222.626 50.6154C221.426 50.6154 217.426 57.2824 212.76 67.0154C202.76 87.5494 199.293 90.8814 197.56 81.6814C196.226 75.1484 187.293 51.5484 182.626 42.2154C180.093 37.1484 177.959 31.5494 177.959 29.8154C177.959 26.8824 178.493 26.4824 181.56 26.8824C185.693 27.4154 187.959 31.2824 194.492 49.6814C200.092 65.1474 201.693 68.2154 203.426 67.2824C205.292 65.9494 217.292 41.6824 219.959 33.2824Z",
                                    fill: activeLetters[2] ? "rgba(255, 255, 255, 0.9)" : "rgba(255, 255, 255, 0.7)",
                                    variants: letterVariants,
                                    initial: "initial",
                                    whileHover: "hover",
                                    whileTap: "click",
                                    animate: activeLetters[2] ? "active" : "initial",
                                    onClick: ()=>handleLetterClick('w', 2),
                                    className: "cursor-pointer animate-gentle-sway",
                                    style: {
                                        animationDelay: '2s',
                                        transformOrigin: 'center',
                                        filter: activeLetters[2] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/DreamyLogo.tsx",
                                    lineNumber: 164,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].path, {
                                    id: "r",
                                    d: "M34.7586 144.216C36.8916 140.883 40.8916 140.35 43.1576 143.149C43.9576 144.216 45.2906 148.483 45.9576 152.616C47.9576 165.283 52.4916 168.483 61.0246 163.55C64.0916 161.683 67.6916 159.016 69.1576 157.416C74.4916 151.549 87.9576 151.016 92.6246 156.616C96.6246 161.416 91.2906 165.949 76.2246 170.616C66.0906 173.816 56.8916 180.216 55.8246 185.016C55.4246 187.017 53.8246 191.549 52.3576 195.149C49.5576 201.949 49.5576 211.683 52.0916 231.416C53.1576 239.949 50.0916 247.016 45.5586 246.349C43.5586 246.083 41.9586 243.949 39.8246 238.216C36.7586 230.482 36.3576 223.416 36.6246 186.349C36.6246 180.083 36.3576 174.084 35.9576 173.016C35.5576 172.083 34.7576 165.816 34.0916 159.283C33.0246 149.55 33.1586 146.749 34.7586 144.216Z",
                                    fill: activeLetters[3] ? "rgba(255, 255, 255, 0.9)" : "rgba(255, 255, 255, 0.7)",
                                    variants: letterVariants,
                                    initial: "initial",
                                    whileHover: "hover",
                                    whileTap: "click",
                                    animate: activeLetters[3] ? "active" : "initial",
                                    onClick: ()=>handleLetterClick('r', 3),
                                    className: "cursor-pointer animate-depth-shift",
                                    style: {
                                        animationDelay: '3s',
                                        transformOrigin: 'center',
                                        filter: activeLetters[3] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/DreamyLogo.tsx",
                                    lineNumber: 183,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].path, {
                                    id: "u",
                                    d: "M148.892 228.881C148.092 209.814 152.092 200.481 159.691 204.214C161.825 205.28 162.092 207.281 161.825 221.414C161.692 236.214 161.958 237.947 165.158 244.614C172.758 260.081 184.759 263.414 190.759 251.548C191.959 249.148 194.358 245.814 195.958 243.947C200.091 239.414 201.958 231.947 201.958 219.814C201.958 211.681 202.491 209.28 204.491 207.414C207.558 204.614 210.092 205.548 213.158 210.748C215.158 214.214 215.425 216.481 214.625 224.748C213.825 233.281 212.625 237.014 205.825 250.347C198.892 263.947 197.158 266.481 191.425 270.347C185.158 274.614 184.891 274.614 177.958 273.147C172.092 271.947 169.558 270.348 163.958 264.748C153.558 254.348 149.558 244.881 148.892 228.881Z",
                                    fill: activeLetters[4] ? "rgba(255, 255, 255, 0.9)" : "rgba(255, 255, 255, 0.7)",
                                    variants: letterVariants,
                                    initial: "initial",
                                    whileHover: "hover",
                                    whileTap: "click",
                                    animate: activeLetters[4] ? "active" : "initial",
                                    onClick: ()=>handleLetterClick('u', 4),
                                    className: "cursor-pointer animate-whisper-glow",
                                    style: {
                                        animationDelay: '4s',
                                        transformOrigin: 'center',
                                        filter: activeLetters[4] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/DreamyLogo.tsx",
                                    lineNumber: 202,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/DreamyLogo.tsx",
                            lineNumber: 124,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/DreamyLogo.tsx",
                        lineNumber: 109,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute inset-0 -z-10",
                        animate: {
                            rotateX: isHovered ? 3 : 0,
                            rotateY: isHovered ? 3 : 0,
                            scale: isHovered ? 1.02 : 1,
                            opacity: isHovered ? 0.3 : 0.1
                        },
                        transition: {
                            duration: 0.6,
                            ease: "easeOut"
                        },
                        style: {
                            transformStyle: 'preserve-3d',
                            transform: 'translateZ(-20px)',
                            filter: 'blur(2px)',
                            background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)'
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/DreamyLogo.tsx",
                        lineNumber: 223,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/DreamyLogo.tsx",
                lineNumber: 94,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "absolute inset-0 pointer-events-none",
                animate: {
                    rotate: 360
                },
                transition: {
                    duration: 60,
                    repeat: Infinity,
                    ease: "linear"
                },
                children: [
                    ...Array(8)
                ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute w-1 h-1 bg-white rounded-full opacity-30",
                        style: {
                            left: `${20 + Math.cos(i * 45 * Math.PI / 180) * 150}px`,
                            top: `${20 + Math.sin(i * 45 * Math.PI / 180) * 150}px`
                        },
                        animate: {
                            opacity: [
                                0.1,
                                0.5,
                                0.1
                            ],
                            scale: [
                                0.5,
                                1.5,
                                0.5
                            ]
                        },
                        transition: {
                            duration: 3 + i * 0.5,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: i * 0.3
                        }
                    }, i, false, {
                        fileName: "[project]/src/components/DreamyLogo.tsx",
                        lineNumber: 250,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/DreamyLogo.tsx",
                lineNumber: 242,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/DreamyLogo.tsx",
        lineNumber: 79,
        columnNumber: 5
    }, this);
}
_s(DreamyLogo, "FeJTH5dhVyPGgtheXM/TtrATv9k=");
_c = DreamyLogo;
var _c;
__turbopack_context__.k.register(_c, "DreamyLogo");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/AnimatedSVGLogo.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AnimatedSVGLogo": (()=>AnimatedSVGLogo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$value$2f$use$2d$motion$2d$value$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/value/use-motion-value.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$value$2f$use$2d$transform$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/value/use-transform.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/gsap/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function AnimatedSVGLogo({ onLetterClick }) {
    _s();
    const svgRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [activeLetters, setActiveLetters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        false,
        false,
        false,
        false,
        false
    ]);
    const [isHovered, setIsHovered] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [clickSequence, setClickSequence] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const mouseX = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$value$2f$use$2d$motion$2d$value$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMotionValue"])(0);
    const mouseY = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$value$2f$use$2d$motion$2d$value$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMotionValue"])(0);
    const rotateX = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$value$2f$use$2d$transform$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTransform"])(mouseY, [
        -300,
        300
    ], [
        15,
        -15
    ]);
    const rotateY = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$value$2f$use$2d$transform$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTransform"])(mouseX, [
        -300,
        300
    ], [
        -15,
        15
    ]);
    const letters = [
        'h',
        'o',
        'w',
        'r',
        'u'
    ];
    const secretSequence = [
        0,
        4,
        1,
        3,
        2
    ];
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AnimatedSVGLogo.useEffect": ()=>{
            // Animation d'entrée épique avec GSAP
            if (svgRef.current) {
                const paths = svgRef.current.querySelectorAll('path');
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].set(paths, {
                    scale: 0,
                    rotation: 360,
                    transformOrigin: 'center',
                    opacity: 0
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].to(paths, {
                    scale: 1,
                    rotation: 0,
                    opacity: 1,
                    duration: 2,
                    ease: "elastic.out(1, 0.5)",
                    stagger: 0.3,
                    delay: 0.5
                });
                // Animation continue de respiration
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].to(svgRef.current, {
                    scale: 1.05,
                    duration: 3,
                    ease: "power2.inOut",
                    yoyo: true,
                    repeat: -1
                });
            }
        }
    }["AnimatedSVGLogo.useEffect"], []);
    const handleMouseMove = (e)=>{
        const rect = e.currentTarget.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        mouseX.set(e.clientX - centerX);
        mouseY.set(e.clientY - centerY);
    };
    const handleLetterClick = (letter, index)=>{
        const newSequence = [
            ...clickSequence,
            index
        ];
        setClickSequence(newSequence);
        // Animation de clic avec GSAP
        if (svgRef.current) {
            const path = svgRef.current.querySelector(`#${letter}`);
            if (path) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].to(path, {
                    scale: 1.5,
                    rotation: 360,
                    duration: 0.8,
                    ease: "back.out(1.7)",
                    yoyo: true,
                    repeat: 1,
                    transformOrigin: 'center'
                });
                // Effet de particules
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].to(path, {
                    filter: 'drop-shadow(0 0 20px #ffffff) drop-shadow(0 0 40px #ffffff)',
                    duration: 0.5,
                    yoyo: true,
                    repeat: 3
                });
            }
        }
        // Activer la lettre
        const newActiveLetters = [
            ...activeLetters
        ];
        newActiveLetters[index] = true;
        setActiveLetters(newActiveLetters);
        setTimeout(()=>{
            const resetLetters = [
                ...activeLetters
            ];
            resetLetters[index] = false;
            setActiveLetters(resetLetters);
        }, 3000);
        // Vérifier séquence secrète
        if (newSequence.length === secretSequence.length) {
            const isCorrect = newSequence.every((val, i)=>val === secretSequence[i]);
            if (isCorrect) {
                // Animation secrète épique !
                if (svgRef.current) {
                    const paths = svgRef.current.querySelectorAll('path');
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].to(paths, {
                        scale: 2,
                        rotation: 720,
                        duration: 3,
                        ease: "power4.out",
                        stagger: 0.1,
                        yoyo: true,
                        repeat: 1,
                        transformOrigin: 'center'
                    });
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].to(svgRef.current, {
                        filter: 'hue-rotate(360deg) saturate(2) brightness(1.5)',
                        duration: 3,
                        ease: "power2.inOut"
                    });
                }
                onLetterClick?.('secret', -1);
            }
            setTimeout(()=>setClickSequence([]), 1000);
        }
        onLetterClick?.(letter, index);
    };
    const handleHover = ()=>{
        setIsHovered(true);
        if (svgRef.current) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].to(svgRef.current.querySelectorAll('path'), {
                scale: 1.1,
                duration: 0.3,
                ease: "power2.out",
                stagger: 0.05,
                transformOrigin: 'center'
            });
        }
    };
    const handleLeave = ()=>{
        setIsHovered(false);
        if (svgRef.current) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$gsap$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["gsap"].to(svgRef.current.querySelectorAll('path'), {
                scale: 1,
                duration: 0.3,
                ease: "power2.out",
                stagger: 0.05,
                transformOrigin: 'center'
            });
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative w-full h-[400px] flex items-center justify-center perspective-1000",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "absolute inset-0 opacity-20",
                animate: {
                    background: [
                        'radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%)',
                        'radial-gradient(circle at 80% 70%, rgba(255,255,255,0.2) 0%, transparent 50%)',
                        'radial-gradient(circle at 50% 50%, rgba(255,255,255,0.15) 0%, transparent 50%)',
                        'radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%)'
                    ]
                },
                transition: {
                    duration: 8,
                    repeat: Infinity,
                    ease: "easeInOut"
                }
            }, void 0, false, {
                fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                lineNumber: 168,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "relative",
                style: {
                    rotateX,
                    rotateY,
                    transformStyle: 'preserve-3d'
                },
                onMouseMove: handleMouseMove,
                onMouseEnter: handleHover,
                onMouseLeave: handleLeave,
                whileHover: {
                    scale: 1.05
                },
                transition: {
                    type: "spring",
                    stiffness: 300,
                    damping: 30
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].svg, {
                    ref: svgRef,
                    width: "320",
                    height: "330",
                    viewBox: "0 0 266 275",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg",
                    className: "cursor-pointer filter drop-shadow-2xl",
                    initial: {
                        opacity: 0,
                        scale: 0.5
                    },
                    animate: {
                        opacity: 1,
                        scale: 1
                    },
                    transition: {
                        duration: 1.5,
                        ease: "easeOut"
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("filter", {
                                    id: "glow",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("feGaussianBlur", {
                                            stdDeviation: "3",
                                            result: "coloredBlur"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                                            lineNumber: 209,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("feMerge", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("feMergeNode", {
                                                    in: "coloredBlur"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                                                    lineNumber: 211,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("feMergeNode", {
                                                    in: "SourceGraphic"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                                                    lineNumber: 212,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                                            lineNumber: 210,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                                    lineNumber: 208,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("filter", {
                                    id: "turbulence",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("feTurbulence", {
                                            baseFrequency: "0.02",
                                            numOctaves: "3",
                                            result: "noise"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                                            lineNumber: 216,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("feDisplacementMap", {
                                            in: "SourceGraphic",
                                            in2: "noise",
                                            scale: "2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                                            lineNumber: 217,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                                    lineNumber: 215,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                            lineNumber: 207,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                            id: "howru",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].path, {
                                    id: "h",
                                    d: "M3.55881 1.54838C6.09211 -0.984916 9.69161 -0.318516 11.425 2.88138C12.4912 4.88138 12.7582 11.8147 11.9582 27.4148C10.6249 58.7478 11.1585 60.2148 23.1582 54.3478C34.3582 48.8818 45.6912 51.5478 51.9582 61.1478C56.4912 67.8148 59.5592 83.0148 59.8252 99.4148C59.9592 111.148 59.5582 113.415 57.4252 115.815C55.9582 117.415 53.5582 118.615 52.0922 118.348C49.5582 117.948 49.2912 117.015 49.4252 109.948C49.5582 105.548 49.6922 97.1478 49.8252 91.2818C50.2252 77.6818 47.9582 71.0148 42.0922 67.0148C37.0252 63.5488 27.2912 62.8808 22.4912 65.6808C14.2252 70.3478 11.4255 78.4818 9.29221 103.282C8.89221 108.348 8.09211 113.681 7.82541 114.882C7.02541 117.815 1.29151 118.081 0.224813 115.415C-0.175087 114.346 -0.0413892 99.2798 0.625211 81.8148C1.29191 64.2148 1.82491 39.4148 1.95821 26.4808C1.95821 10.2147 2.49211 2.61508 3.55881 1.54838Z",
                                    fill: activeLetters[0] ? "#ffffff" : "#c8c8c8",
                                    onClick: ()=>handleLetterClick('h', 0),
                                    className: "cursor-pointer transition-all duration-300",
                                    style: {
                                        filter: activeLetters[0] ? 'url(#glow)' : 'none',
                                        transformOrigin: 'center'
                                    },
                                    whileHover: {
                                        scale: 1.1
                                    },
                                    whileTap: {
                                        scale: 0.95
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                                    lineNumber: 223,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].path, {
                                    id: "o",
                                    d: "M137.425 21.4154C139.025 21.2821 142.225 22.6156 144.759 24.3486C147.159 25.9496 151.025 29.9486 153.292 33.1486C156.759 38.2156 157.292 39.8156 157.158 47.4156C156.358 81.1486 126.091 101.816 107.691 80.8826C100.492 72.6156 98.8922 56.6156 104.092 44.0826C106.758 37.6826 114.891 28.0826 119.158 26.3486C124.491 24.0826 134.358 21.4155 137.425 21.4154ZM139.825 32.7496C131.959 30.0826 129.025 30.0826 123.958 32.7496C114.092 37.8166 108.625 47.2826 108.625 59.0156C108.625 67.9496 111.025 74.7496 115.425 77.8156C119.158 80.3496 129.292 80.6156 133.559 78.2156C139.292 75.0146 145.825 65.6826 147.958 57.8156C150.491 48.2156 150.492 45.0156 148.092 39.4156C146.625 35.9486 144.892 34.4826 139.825 32.7496Z",
                                    fill: activeLetters[1] ? "#ffffff" : "#c8c8c8",
                                    onClick: ()=>handleLetterClick('o', 1),
                                    className: "cursor-pointer transition-all duration-300",
                                    style: {
                                        filter: activeLetters[1] ? 'url(#glow)' : 'none',
                                        transformOrigin: 'center'
                                    },
                                    whileHover: {
                                        scale: 1.1
                                    },
                                    whileTap: {
                                        scale: 0.95
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                                    lineNumber: 238,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].path, {
                                    id: "w",
                                    d: "M219.959 33.2824C222.892 24.3494 225.426 21.9491 228.226 25.5494C229.026 26.4824 229.959 31.8154 230.359 37.2824C231.026 47.6824 236.093 64.7494 238.76 65.8154C241.159 66.7484 242.36 65.1484 248.359 52.7494C256.626 35.9494 260.226 30.6154 263.293 30.6154C264.759 30.6154 265.959 31.4154 265.959 32.4814C265.959 35.6814 260.76 46.8824 255.293 55.2824C252.493 59.6824 248.626 67.2814 246.626 72.2154C243.026 81.1484 239.159 86.2154 236.893 84.7494C236.226 84.3494 234.626 81.1484 233.426 77.6814C232.092 74.2154 230.226 69.1484 229.293 66.6154C228.226 64.0824 226.759 59.4154 225.959 56.2154C225.159 52.8824 223.693 50.6154 222.626 50.6154C221.426 50.6154 217.426 57.2824 212.76 67.0154C202.76 87.5494 199.293 90.8814 197.56 81.6814C196.226 75.1484 187.293 51.5484 182.626 42.2154C180.093 37.1484 177.959 31.5494 177.959 29.8154C177.959 26.8824 178.493 26.4824 181.56 26.8824C185.693 27.4154 187.959 31.2824 194.492 49.6814C200.092 65.1474 201.693 68.2154 203.426 67.2824C205.292 65.9494 217.292 41.6824 219.959 33.2824Z",
                                    fill: activeLetters[2] ? "#ffffff" : "#c8c8c8",
                                    onClick: ()=>handleLetterClick('w', 2),
                                    className: "cursor-pointer transition-all duration-300",
                                    style: {
                                        filter: activeLetters[2] ? 'url(#glow)' : 'none',
                                        transformOrigin: 'center'
                                    },
                                    whileHover: {
                                        scale: 1.1
                                    },
                                    whileTap: {
                                        scale: 0.95
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                                    lineNumber: 253,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].path, {
                                    id: "r",
                                    d: "M34.7586 144.216C36.8916 140.883 40.8916 140.35 43.1576 143.149C43.9576 144.216 45.2906 148.483 45.9576 152.616C47.9576 165.283 52.4916 168.483 61.0246 163.55C64.0916 161.683 67.6916 159.016 69.1576 157.416C74.4916 151.549 87.9576 151.016 92.6246 156.616C96.6246 161.416 91.2906 165.949 76.2246 170.616C66.0906 173.816 56.8916 180.216 55.8246 185.016C55.4246 187.017 53.8246 191.549 52.3576 195.149C49.5576 201.949 49.5576 211.683 52.0916 231.416C53.1576 239.949 50.0916 247.016 45.5586 246.349C43.5586 246.083 41.9586 243.949 39.8246 238.216C36.7586 230.482 36.3576 223.416 36.6246 186.349C36.6246 180.083 36.3576 174.084 35.9576 173.016C35.5576 172.083 34.7576 165.816 34.0916 159.283C33.0246 149.55 33.1586 146.749 34.7586 144.216Z",
                                    fill: activeLetters[3] ? "#ffffff" : "#c8c8c8",
                                    onClick: ()=>handleLetterClick('r', 3),
                                    className: "cursor-pointer transition-all duration-300",
                                    style: {
                                        filter: activeLetters[3] ? 'url(#glow)' : 'none',
                                        transformOrigin: 'center'
                                    },
                                    whileHover: {
                                        scale: 1.1
                                    },
                                    whileTap: {
                                        scale: 0.95
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                                    lineNumber: 268,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].path, {
                                    id: "u",
                                    d: "M148.892 228.881C148.092 209.814 152.092 200.481 159.691 204.214C161.825 205.28 162.092 207.281 161.825 221.414C161.692 236.214 161.958 237.947 165.158 244.614C172.758 260.081 184.759 263.414 190.759 251.548C191.959 249.148 194.358 245.814 195.958 243.947C200.091 239.414 201.958 231.947 201.958 219.814C201.958 211.681 202.491 209.28 204.491 207.414C207.558 204.614 210.092 205.548 213.158 210.748C215.158 214.214 215.425 216.481 214.625 224.748C213.825 233.281 212.625 237.014 205.825 250.347C198.892 263.947 197.158 266.481 191.425 270.347C185.158 274.614 184.891 274.614 177.958 273.147C172.092 271.947 169.558 270.348 163.958 264.748C153.558 254.348 149.558 244.881 148.892 228.881Z",
                                    fill: activeLetters[4] ? "#ffffff" : "#c8c8c8",
                                    onClick: ()=>handleLetterClick('u', 4),
                                    className: "cursor-pointer transition-all duration-300",
                                    style: {
                                        filter: activeLetters[4] ? 'url(#glow)' : 'none',
                                        transformOrigin: 'center'
                                    },
                                    whileHover: {
                                        scale: 1.1
                                    },
                                    whileTap: {
                                        scale: 0.95
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                                    lineNumber: 283,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                            lineNumber: 221,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                    lineNumber: 194,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                lineNumber: 181,
                columnNumber: 7
            }, this),
            [
                ...Array(12)
            ].map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    className: "absolute w-2 h-2 bg-white rounded-full opacity-20",
                    style: {
                        left: `${50 + Math.cos(i * 30 * Math.PI / 180) * 200}px`,
                        top: `${50 + Math.sin(i * 30 * Math.PI / 180) * 200}px`
                    },
                    animate: {
                        scale: [
                            0.5,
                            1.5,
                            0.5
                        ],
                        opacity: [
                            0.1,
                            0.6,
                            0.1
                        ],
                        rotate: 360
                    },
                    transition: {
                        duration: 4 + i * 0.5,
                        repeat: Infinity,
                        ease: "easeInOut",
                        delay: i * 0.2
                    }
                }, i, false, {
                    fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
                    lineNumber: 302,
                    columnNumber: 9
                }, this))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/AnimatedSVGLogo.tsx",
        lineNumber: 166,
        columnNumber: 5
    }, this);
}
_s(AnimatedSVGLogo, "o6vQEzJdJg2ronQl/11oNRd71+k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$value$2f$use$2d$motion$2d$value$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMotionValue"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$value$2f$use$2d$motion$2d$value$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMotionValue"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$value$2f$use$2d$transform$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTransform"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$value$2f$use$2d$transform$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTransform"]
    ];
});
_c = AnimatedSVGLogo;
var _c;
__turbopack_context__.k.register(_c, "AnimatedSVGLogo");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/Logo3D.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Logo3D": (()=>Logo3D)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$react$2d$three$2d$fiber$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js [app-client] (ecmascript) <export C as useFrame>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__A__as__useThree$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js [app-client] (ecmascript) <export A as useThree>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Text3D$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Text3D.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Center$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Center.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Float$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Float.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$MeshDistortMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/MeshDistortMaterial.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/shapes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Environment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Environment.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function Letter3D({ letter, position, index, isActive, onClick }) {
    _s();
    const meshRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [hovered, setHovered] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"])({
        "Letter3D.useFrame": (state)=>{
            if (meshRef.current) {
                // Animation de base flottante
                meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + index) * 0.3;
                meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.5 + index) * 0.1;
                meshRef.current.rotation.z = Math.cos(state.clock.elapsedTime * 0.3 + index) * 0.05;
                // Animation quand actif
                if (isActive) {
                    meshRef.current.rotation.y += 0.02;
                    meshRef.current.scale.setScalar(1.2 + Math.sin(state.clock.elapsedTime * 3) * 0.1);
                } else {
                    meshRef.current.scale.setScalar(hovered ? 1.1 : 1);
                }
            }
        }
    }["Letter3D.useFrame"]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Float$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Float"], {
        speed: 2,
        rotationIntensity: 0.5,
        floatIntensity: 0.5,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Text3D$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text3D"], {
            ref: meshRef,
            font: "/fonts/helvetiker_regular.typeface.json",
            size: 1.5,
            height: 0.3,
            position: position,
            onPointerOver: ()=>setHovered(true),
            onPointerOut: ()=>setHovered(false),
            onClick: onClick,
            curveSegments: 12,
            children: [
                letter,
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$MeshDistortMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MeshDistortMaterial"], {
                    color: isActive ? "#ffffff" : hovered ? "#e0e0e0" : "#a0a0a0",
                    distort: isActive ? 0.4 : hovered ? 0.2 : 0.1,
                    speed: isActive ? 3 : 1,
                    roughness: 0.1,
                    metalness: 0.8
                }, void 0, false, {
                    fileName: "[project]/src/components/Logo3D.tsx",
                    lineNumber: 56,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/Logo3D.tsx",
            lineNumber: 44,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/Logo3D.tsx",
        lineNumber: 39,
        columnNumber: 5
    }, this);
}
_s(Letter3D, "rCuBNwiPTBvDhn0zdAl8mX8eeeM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"]
    ];
});
_c = Letter3D;
function ParticleField() {
    _s1();
    const pointsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const particleCount = 200;
    const positions = new Float32Array(particleCount * 3);
    for(let i = 0; i < particleCount; i++){
        positions[i * 3] = (Math.random() - 0.5) * 20;
        positions[i * 3 + 1] = (Math.random() - 0.5) * 20;
        positions[i * 3 + 2] = (Math.random() - 0.5) * 20;
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"])({
        "ParticleField.useFrame": (state)=>{
            if (pointsRef.current) {
                pointsRef.current.rotation.y = state.clock.elapsedTime * 0.05;
                pointsRef.current.rotation.x = state.clock.elapsedTime * 0.02;
            }
        }
    }["ParticleField.useFrame"]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("points", {
        ref: pointsRef,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("bufferGeometry", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("bufferAttribute", {
                    attach: "attributes-position",
                    count: particleCount,
                    array: positions,
                    itemSize: 3
                }, void 0, false, {
                    fileName: "[project]/src/components/Logo3D.tsx",
                    lineNumber: 89,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/Logo3D.tsx",
                lineNumber: 88,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pointsMaterial", {
                size: 0.02,
                color: "#ffffff",
                transparent: true,
                opacity: 0.6,
                sizeAttenuation: true
            }, void 0, false, {
                fileName: "[project]/src/components/Logo3D.tsx",
                lineNumber: 96,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/Logo3D.tsx",
        lineNumber: 87,
        columnNumber: 5
    }, this);
}
_s1(ParticleField, "7C7C5y4MprfU2123a3F/mkNQo3Y=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"]
    ];
});
_c1 = ParticleField;
function BackgroundSphere() {
    _s2();
    const sphereRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"])({
        "BackgroundSphere.useFrame": (state)=>{
            if (sphereRef.current) {
                sphereRef.current.rotation.y = state.clock.elapsedTime * 0.01;
                sphereRef.current.rotation.x = state.clock.elapsedTime * 0.005;
            }
        }
    }["BackgroundSphere.useFrame"]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
        ref: sphereRef,
        args: [
            15,
            32,
            32
        ],
        position: [
            0,
            0,
            -10
        ],
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$MeshDistortMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MeshDistortMaterial"], {
            color: "#111111",
            distort: 0.3,
            speed: 1,
            roughness: 0.8,
            metalness: 0.2,
            transparent: true,
            opacity: 0.3
        }, void 0, false, {
            fileName: "[project]/src/components/Logo3D.tsx",
            lineNumber: 119,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/Logo3D.tsx",
        lineNumber: 118,
        columnNumber: 5
    }, this);
}
_s2(BackgroundSphere, "Av1D7j8eGQw6d7NAO2YjuaUNuIc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"]
    ];
});
_c2 = BackgroundSphere;
function CameraController() {
    _s3();
    const { camera } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__A__as__useThree$3e$__["useThree"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"])({
        "CameraController.useFrame": (state)=>{
            camera.position.x = Math.sin(state.clock.elapsedTime * 0.1) * 2;
            camera.position.y = Math.cos(state.clock.elapsedTime * 0.15) * 1;
            camera.lookAt(0, 0, 0);
        }
    }["CameraController.useFrame"]);
    return null;
}
_s3(CameraController, "K6LkdZnP8OJ6UK0tVtTtxiafG3Y=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__A__as__useThree$3e$__["useThree"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"]
    ];
});
_c3 = CameraController;
function Logo3D({ onLetterClick }) {
    _s4();
    const [activeLetters, setActiveLetters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        false,
        false,
        false,
        false,
        false
    ]);
    const [clickSequence, setClickSequence] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const letters = [
        'h',
        'o',
        'w',
        'r',
        'u'
    ];
    const positions = [
        [
            -3,
            0,
            0
        ],
        [
            -1.5,
            0,
            0
        ],
        [
            0,
            0,
            0
        ],
        [
            1.5,
            0,
            0
        ],
        [
            3,
            0,
            0
        ]
    ];
    const secretSequence = [
        0,
        4,
        1,
        3,
        2
    ]; // h-u-o-r-w
    const handleLetterClick = (letter, index)=>{
        const newSequence = [
            ...clickSequence,
            index
        ];
        setClickSequence(newSequence);
        // Activer la lettre avec animation
        const newActiveLetters = [
            ...activeLetters
        ];
        newActiveLetters[index] = true;
        setActiveLetters(newActiveLetters);
        // Reset après 3 secondes
        setTimeout(()=>{
            const resetLetters = [
                ...activeLetters
            ];
            resetLetters[index] = false;
            setActiveLetters(resetLetters);
        }, 3000);
        // Vérifier la séquence secrète
        if (newSequence.length === secretSequence.length) {
            const isCorrect = newSequence.every((val, i)=>val === secretSequence[i]);
            if (isCorrect) {
                onLetterClick?.('secret', -1);
                // Animation spéciale pour toutes les lettres
                setActiveLetters([
                    true,
                    true,
                    true,
                    true,
                    true
                ]);
                setTimeout(()=>setActiveLetters([
                        false,
                        false,
                        false,
                        false,
                        false
                    ]), 5000);
            }
            setTimeout(()=>setClickSequence([]), 1000);
        }
        onLetterClick?.(letter, index);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full h-[600px] relative",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0,
                    scale: 0.8
                },
                animate: {
                    opacity: 1,
                    scale: 1
                },
                transition: {
                    duration: 2,
                    ease: "easeOut"
                },
                className: "w-full h-full",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$react$2d$three$2d$fiber$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Canvas"], {
                    camera: {
                        position: [
                            0,
                            0,
                            8
                        ],
                        fov: 50
                    },
                    gl: {
                        antialias: true,
                        alpha: true
                    },
                    style: {
                        background: 'transparent'
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ambientLight", {
                            intensity: 0.3
                        }, void 0, false, {
                            fileName: "[project]/src/components/Logo3D.tsx",
                            lineNumber: 207,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pointLight", {
                            position: [
                                10,
                                10,
                                10
                            ],
                            intensity: 1
                        }, void 0, false, {
                            fileName: "[project]/src/components/Logo3D.tsx",
                            lineNumber: 208,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pointLight", {
                            position: [
                                -10,
                                -10,
                                -10
                            ],
                            intensity: 0.5,
                            color: "#4a5568"
                        }, void 0, false, {
                            fileName: "[project]/src/components/Logo3D.tsx",
                            lineNumber: 209,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Environment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Environment"], {
                            preset: "night"
                        }, void 0, false, {
                            fileName: "[project]/src/components/Logo3D.tsx",
                            lineNumber: 211,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(BackgroundSphere, {}, void 0, false, {
                            fileName: "[project]/src/components/Logo3D.tsx",
                            lineNumber: 213,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ParticleField, {}, void 0, false, {
                            fileName: "[project]/src/components/Logo3D.tsx",
                            lineNumber: 214,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Center$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Center"], {
                            children: letters.map((letter, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Letter3D, {
                                    letter: letter,
                                    position: positions[index],
                                    index: index,
                                    isActive: activeLetters[index],
                                    onClick: ()=>handleLetterClick(letter, index)
                                }, letter + index, false, {
                                    fileName: "[project]/src/components/Logo3D.tsx",
                                    lineNumber: 218,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/Logo3D.tsx",
                            lineNumber: 216,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CameraController, {}, void 0, false, {
                            fileName: "[project]/src/components/Logo3D.tsx",
                            lineNumber: 229,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/Logo3D.tsx",
                    lineNumber: 202,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/Logo3D.tsx",
                lineNumber: 196,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0,
                    y: 20
                },
                animate: {
                    opacity: 1,
                    y: 0
                },
                transition: {
                    delay: 2,
                    duration: 1
                },
                className: "absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-whisper-400 text-sm font-light mb-2",
                        children: "cliquez sur les lettres pour les animer"
                    }, void 0, false, {
                        fileName: "[project]/src/components/Logo3D.tsx",
                        lineNumber: 240,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-whisper-600 text-xs font-light",
                        children: "trouvez la séquence secrète..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/Logo3D.tsx",
                        lineNumber: 243,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/Logo3D.tsx",
                lineNumber: 234,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/Logo3D.tsx",
        lineNumber: 195,
        columnNumber: 5
    }, this);
}
_s4(Logo3D, "+wmYe5Bu86UVK3Ap0u/AoSEuRWU=");
_c4 = Logo3D;
var _c, _c1, _c2, _c3, _c4;
__turbopack_context__.k.register(_c, "Letter3D");
__turbopack_context__.k.register(_c1, "ParticleField");
__turbopack_context__.k.register(_c2, "BackgroundSphere");
__turbopack_context__.k.register(_c3, "CameraController");
__turbopack_context__.k.register(_c4, "Logo3D");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/Scene3D.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Scene3D": (()=>Scene3D)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$react$2d$three$2d$fiber$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js [app-client] (ecmascript) <export C as useFrame>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__A__as__useThree$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js [app-client] (ecmascript) <export A as useThree>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Text3D$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Text3D.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Center$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Center.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Float$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Float.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$MeshDistortMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/MeshDistortMaterial.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/shapes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Environment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Environment.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$OrbitControls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/OrbitControls.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Sparkles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Sparkles.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Stars$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Stars.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Cloud$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Cloud.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$postprocessing$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/postprocessing/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postprocessing$2f$build$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postprocessing/build/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
function FloatingText({ text, position, color = "#ffffff", isActive = false }) {
    _s();
    const meshRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"])({
        "FloatingText.useFrame": (state)=>{
            if (meshRef.current) {
                meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.5;
                meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime) * 0.3;
                if (isActive) {
                    meshRef.current.scale.setScalar(1.5 + Math.sin(state.clock.elapsedTime * 5) * 0.2);
                }
            }
        }
    }["FloatingText.useFrame"]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Float$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Float"], {
        speed: 3,
        rotationIntensity: 0.5,
        floatIntensity: 2,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Text3D$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text3D"], {
            ref: meshRef,
            font: "/fonts/helvetiker_regular.typeface.json",
            size: 3,
            height: 1.5,
            position: position,
            curveSegments: 24,
            bevelEnabled: true,
            bevelSize: 0.1,
            bevelThickness: 0.08,
            bevelSegments: 16,
            children: [
                text,
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$MeshDistortMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MeshDistortMaterial"], {
                    color: color,
                    distort: isActive ? 0.3 : 0.1,
                    speed: isActive ? 5 : 2,
                    roughness: isActive ? 0.05 : 0.1,
                    metalness: isActive ? 0.95 : 0.85,
                    clearcoat: 1.0,
                    clearcoatRoughness: 0.05
                }, void 0, false, {
                    fileName: "[project]/src/components/Scene3D.tsx",
                    lineNumber: 68,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/Scene3D.tsx",
            lineNumber: 55,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/Scene3D.tsx",
        lineNumber: 54,
        columnNumber: 5
    }, this);
}
_s(FloatingText, "/vg1AmA8+P3+Fj0/y210JTVKtL0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"]
    ];
});
_c = FloatingText;
function DreamSphere() {
    _s1();
    const sphereRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"])({
        "DreamSphere.useFrame": (state)=>{
            if (sphereRef.current) {
                sphereRef.current.rotation.x = state.clock.elapsedTime * 0.1;
                sphereRef.current.rotation.y = state.clock.elapsedTime * 0.15;
                sphereRef.current.position.y = Math.sin(state.clock.elapsedTime) * 2;
            }
        }
    }["DreamSphere.useFrame"]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$shapes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sphere"], {
        ref: sphereRef,
        args: [
            3,
            64,
            64
        ],
        position: [
            0,
            0,
            -8
        ],
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$MeshDistortMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MeshDistortMaterial"], {
            color: "#4a5568",
            distort: 0.4,
            speed: 2,
            roughness: 0.2,
            metalness: 0.9,
            transparent: true,
            opacity: 0.6
        }, void 0, false, {
            fileName: "[project]/src/components/Scene3D.tsx",
            lineNumber: 95,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/Scene3D.tsx",
        lineNumber: 94,
        columnNumber: 5
    }, this);
}
_s1(DreamSphere, "Av1D7j8eGQw6d7NAO2YjuaUNuIc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"]
    ];
});
_c1 = DreamSphere;
function ParticleSystem() {
    _s2();
    const pointsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const particleCount = 500;
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    for(let i = 0; i < particleCount; i++){
        positions[i * 3] = (Math.random() - 0.5) * 50;
        positions[i * 3 + 1] = (Math.random() - 0.5) * 50;
        positions[i * 3 + 2] = (Math.random() - 0.5) * 50;
        colors[i * 3] = Math.random();
        colors[i * 3 + 1] = Math.random();
        colors[i * 3 + 2] = Math.random();
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"])({
        "ParticleSystem.useFrame": (state)=>{
            if (pointsRef.current) {
                pointsRef.current.rotation.y = state.clock.elapsedTime * 0.05;
                pointsRef.current.rotation.x = state.clock.elapsedTime * 0.02;
            }
        }
    }["ParticleSystem.useFrame"]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("points", {
        ref: pointsRef,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("bufferGeometry", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("bufferAttribute", {
                        attach: "attributes-position",
                        count: particleCount,
                        array: positions,
                        itemSize: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 135,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("bufferAttribute", {
                        attach: "attributes-color",
                        count: particleCount,
                        array: colors,
                        itemSize: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 141,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/Scene3D.tsx",
                lineNumber: 134,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pointsMaterial", {
                size: 0.05,
                vertexColors: true,
                transparent: true,
                opacity: 0.8,
                sizeAttenuation: true
            }, void 0, false, {
                fileName: "[project]/src/components/Scene3D.tsx",
                lineNumber: 148,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/Scene3D.tsx",
        lineNumber: 133,
        columnNumber: 5
    }, this);
}
_s2(ParticleSystem, "7C7C5y4MprfU2123a3F/mkNQo3Y=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"]
    ];
});
_c2 = ParticleSystem;
function CameraAnimation() {
    _s3();
    const { camera } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__A__as__useThree$3e$__["useThree"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"])({
        "CameraAnimation.useFrame": (state)=>{
            camera.position.x = Math.sin(state.clock.elapsedTime * 0.2) * 5;
            camera.position.y = Math.cos(state.clock.elapsedTime * 0.15) * 3;
            camera.position.z = 15 + Math.sin(state.clock.elapsedTime * 0.1) * 5;
            camera.lookAt(0, 0, 0);
        }
    }["CameraAnimation.useFrame"]);
    return null;
}
_s3(CameraAnimation, "K6LkdZnP8OJ6UK0tVtTtxiafG3Y=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__A__as__useThree$3e$__["useThree"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"]
    ];
});
_c3 = CameraAnimation;
function Scene3D({ activeLetters = [
    false,
    false,
    false,
    false,
    false
] }) {
    _s4();
    const [glitchActive, setGlitchActive] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const letters = [
        "h",
        "o",
        "w",
        "r",
        "u"
    ];
    const positions = [
        [
            -6,
            0,
            0
        ],
        [
            -3,
            0,
            1
        ],
        [
            0,
            0,
            0
        ],
        [
            3,
            0,
            1
        ],
        [
            6,
            0,
            0
        ]
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full h-screen",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$react$2d$three$2d$fiber$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Canvas"], {
                camera: {
                    position: [
                        0,
                        0,
                        15
                    ],
                    fov: 75
                },
                gl: {
                    antialias: true,
                    alpha: true
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ambientLight", {
                        intensity: 0.2
                    }, void 0, false, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 197,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pointLight", {
                        position: [
                            10,
                            10,
                            10
                        ],
                        intensity: 1,
                        color: "#ffffff"
                    }, void 0, false, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 198,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pointLight", {
                        position: [
                            -10,
                            -10,
                            -10
                        ],
                        intensity: 0.5,
                        color: "#4a5568"
                    }, void 0, false, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 199,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("spotLight", {
                        position: [
                            0,
                            20,
                            0
                        ],
                        angle: 0.3,
                        penumbra: 1,
                        intensity: 1,
                        castShadow: true
                    }, void 0, false, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 204,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Environment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Environment"], {
                        preset: "night"
                    }, void 0, false, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 213,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Stars$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Stars"], {
                        radius: 100,
                        depth: 50,
                        count: 5000,
                        factor: 4,
                        saturation: 0,
                        fade: true
                    }, void 0, false, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 214,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Cloud$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Cloud"], {
                        position: [
                            -20,
                            10,
                            -20
                        ],
                        speed: 0.2,
                        opacity: 0.1,
                        width: 10,
                        depth: 1.5,
                        segments: 20
                    }, void 0, false, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 224,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Cloud$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Cloud"], {
                        position: [
                            20,
                            -10,
                            -20
                        ],
                        speed: 0.3,
                        opacity: 0.1,
                        width: 15,
                        depth: 2,
                        segments: 25
                    }, void 0, false, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 232,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ParticleSystem, {}, void 0, false, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 242,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Sparkles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sparkles"], {
                        count: 100,
                        scale: [
                            20,
                            20,
                            20
                        ],
                        size: 2,
                        speed: 0.5
                    }, void 0, false, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 243,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DreamSphere, {}, void 0, false, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 246,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Center$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Center"], {
                        children: letters.map((letter, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FloatingText, {
                                text: letter,
                                position: positions[index],
                                color: activeLetters[index] ? "#ffffff" : "#a0a0a0",
                                isActive: activeLetters[index]
                            }, letter, false, {
                                fileName: "[project]/src/components/Scene3D.tsx",
                                lineNumber: 251,
                                columnNumber: 13
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 249,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CameraAnimation, {}, void 0, false, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 262,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$OrbitControls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OrbitControls"], {
                        enablePan: false,
                        enableZoom: false,
                        enableRotate: true,
                        autoRotate: true,
                        autoRotateSpeed: 0.5
                    }, void 0, false, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 265,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$postprocessing$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EffectComposer"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$postprocessing$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Bloom"], {
                                intensity: 1.5,
                                luminanceThreshold: 0.2,
                                luminanceSmoothing: 0.9,
                                height: 300
                            }, void 0, false, {
                                fileName: "[project]/src/components/Scene3D.tsx",
                                lineNumber: 275,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$postprocessing$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChromaticAberration"], {
                                blendFunction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postprocessing$2f$build$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BlendFunction"].NORMAL,
                                offset: [
                                    0.002,
                                    0.002
                                ]
                            }, void 0, false, {
                                fileName: "[project]/src/components/Scene3D.tsx",
                                lineNumber: 281,
                                columnNumber: 11
                            }, this),
                            glitchActive && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$postprocessing$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Glitch"], {
                                delay: [
                                    1.5,
                                    3.5
                                ],
                                duration: [
                                    0.6,
                                    1.0
                                ],
                                strength: [
                                    0.3,
                                    1.0
                                ],
                                mode: 0
                            }, void 0, false, {
                                fileName: "[project]/src/components/Scene3D.tsx",
                                lineNumber: 286,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/Scene3D.tsx",
                        lineNumber: 274,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/Scene3D.tsx",
                lineNumber: 192,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute bottom-8 left-8 space-y-2",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: ()=>setGlitchActive(!glitchActive),
                    className: "px-4 py-2 bg-ghost-100 border border-whisper-200 text-whisper-300 text-sm font-light hover:bg-ghost-200 transition-colors",
                    children: glitchActive ? "Désactiver Glitch" : "Activer Glitch"
                }, void 0, false, {
                    fileName: "[project]/src/components/Scene3D.tsx",
                    lineNumber: 298,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/Scene3D.tsx",
                lineNumber: 297,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/Scene3D.tsx",
        lineNumber: 191,
        columnNumber: 5
    }, this);
}
_s4(Scene3D, "sxoabxMtQCxHAxD4C59gG69/Ylw=");
_c4 = Scene3D;
var _c, _c1, _c2, _c3, _c4;
__turbopack_context__.k.register(_c, "FloatingText");
__turbopack_context__.k.register(_c1, "DreamSphere");
__turbopack_context__.k.register(_c2, "ParticleSystem");
__turbopack_context__.k.register(_c3, "CameraAnimation");
__turbopack_context__.k.register(_c4, "Scene3D");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/SVG3DLogo.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "SVG3DLogo": (()=>SVG3DLogo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$react$2d$three$2d$fiber$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js [app-client] (ecmascript) <export C as useFrame>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__e__as__extend$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js [app-client] (ecmascript) <export e as extend>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Center$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Center.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Float$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Float.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$MeshDistortMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/MeshDistortMaterial.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Environment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Environment.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$OrbitControls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/OrbitControls.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2d$stdlib$2f$loaders$2f$SVGLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/three-stdlib/loaders/SVGLoader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/three/build/three.core.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
// Extend pour utiliser SVGLoader
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__e__as__extend$3e$__["extend"])({
    SVGLoader: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2d$stdlib$2f$loaders$2f$SVGLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SVGLoader"]
});
function Letter3D({ svgPath, position, index, isActive, isHovered, onClick, onHover }) {
    _s();
    const meshRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [geometry, setGeometry] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Letter3D.useEffect": ()=>{
            // Créer la géométrie 3D à partir du path SVG
            const loader = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2d$stdlib$2f$loaders$2f$SVGLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SVGLoader"]();
            const svgData = `<svg viewBox="0 0 266 275"><path d="${svgPath}"/></svg>`;
            const svgResult = loader.parse(svgData);
            if (svgResult.paths.length > 0) {
                const path = svgResult.paths[0];
                const shapes = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2d$stdlib$2f$loaders$2f$SVGLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SVGLoader"].createShapes(path);
                if (shapes.length > 0) {
                    const extrudeSettings = {
                        depth: 4.5,
                        bevelEnabled: true,
                        bevelSegments: 48,
                        steps: 10,
                        bevelSize: 0.4,
                        bevelThickness: 0.35,
                        curveSegments: 64
                    };
                    const extrudeGeometry = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExtrudeGeometry"](shapes, extrudeSettings);
                    extrudeGeometry.center();
                    extrudeGeometry.scale(0.02, -0.02, 0.02); // Encore plus grand pour un effet massif
                    // Calculer les normales pour un rendu plus lisse
                    extrudeGeometry.computeVertexNormals();
                    setGeometry(extrudeGeometry);
                }
            }
        }
    }["Letter3D.useEffect"], [
        svgPath
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"])({
        "Letter3D.useFrame": (state)=>{
            if (meshRef.current) {
                // Animation de base flottante
                meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + index) * 0.2;
                meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.5 + index) * 0.1;
                meshRef.current.rotation.z = Math.cos(state.clock.elapsedTime * 0.3 + index) * 0.05;
                // Animation quand actif
                if (isActive) {
                    meshRef.current.rotation.y += 0.02;
                    meshRef.current.scale.setScalar(1.3 + Math.sin(state.clock.elapsedTime * 4) * 0.1);
                } else if (isHovered) {
                    meshRef.current.scale.setScalar(1.1);
                } else {
                    meshRef.current.scale.setScalar(1);
                }
            }
        }
    }["Letter3D.useFrame"]);
    if (!geometry) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Float$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Float"], {
        speed: 2,
        rotationIntensity: 0.3,
        floatIntensity: 0.4,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("mesh", {
            ref: meshRef,
            geometry: geometry,
            position: position,
            onClick: onClick,
            onPointerOver: ()=>onHover(true),
            onPointerOut: ()=>onHover(false),
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$MeshDistortMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MeshDistortMaterial"], {
                color: isActive ? "#ffffff" : isHovered ? "#f0f0f0" : "#d8d8d8",
                distort: isActive ? 0.2 : isHovered ? 0.08 : 0.03,
                speed: isActive ? 4 : isHovered ? 2 : 1,
                roughness: isActive ? 0.05 : 0.15,
                metalness: isActive ? 0.95 : 0.8,
                clearcoat: 1.0,
                clearcoatRoughness: 0.05
            }, void 0, false, {
                fileName: "[project]/src/components/SVG3DLogo.tsx",
                lineNumber: 113,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/SVG3DLogo.tsx",
            lineNumber: 105,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/SVG3DLogo.tsx",
        lineNumber: 104,
        columnNumber: 5
    }, this);
}
_s(Letter3D, "c2d59MYmcaJFigT7NOQS+3A1z9Y=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"]
    ];
});
_c = Letter3D;
function SVG3DLogo({ onLetterClick, activeLetters = [
    false,
    false,
    false,
    false,
    false
], className = "" }) {
    _s1();
    const [hoveredLetters, setHoveredLetters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        false,
        false,
        false,
        false,
        false
    ]);
    const [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SVG3DLogo.useEffect": ()=>{
            const checkMobile = {
                "SVG3DLogo.useEffect.checkMobile": ()=>{
                    setIsMobile(window.innerWidth < 768);
                }
            }["SVG3DLogo.useEffect.checkMobile"];
            checkMobile();
            window.addEventListener("resize", checkMobile);
            return ({
                "SVG3DLogo.useEffect": ()=>window.removeEventListener("resize", checkMobile)
            })["SVG3DLogo.useEffect"];
        }
    }["SVG3DLogo.useEffect"], []);
    const letters = [
        {
            letter: "h",
            path: "M3.55881 1.54838C6.09211 -0.984916 9.69161 -0.318516 11.425 2.88138C12.4912 4.88138 12.7582 11.8147 11.9582 27.4148C10.6249 58.7478 11.1585 60.2148 23.1582 54.3478C34.3582 48.8818 45.6912 51.5478 51.9582 61.1478C56.4912 67.8148 59.5592 83.0148 59.8252 99.4148C59.9592 111.148 59.5582 113.415 57.4252 115.815C55.9582 117.415 53.5582 118.615 52.0922 118.348C49.5582 117.948 49.2912 117.015 49.4252 109.948C49.5582 105.548 49.6922 97.1478 49.8252 91.2818C50.2252 77.6818 47.9582 71.0148 42.0922 67.0148C37.0252 63.5488 27.2912 62.8808 22.4912 65.6808C14.2252 70.3478 11.4255 78.4818 9.29221 103.282C8.89221 108.348 8.09211 113.681 7.82541 114.882C7.02541 117.815 1.29151 118.081 0.224813 115.415C-0.175087 114.346 -0.0413892 99.2798 0.625211 81.8148C1.29191 64.2148 1.82491 39.4148 1.95821 26.4808C1.95821 10.2147 2.49211 2.61508 3.55881 1.54838Z"
        },
        {
            letter: "o",
            path: "M137.425 21.4154C139.025 21.2821 142.225 22.6156 144.759 24.3486C147.159 25.9496 151.025 29.9486 153.292 33.1486C156.759 38.2156 157.292 39.8156 157.158 47.4156C156.358 81.1486 126.091 101.816 107.691 80.8826C100.492 72.6156 98.8922 56.6156 104.092 44.0826C106.758 37.6826 114.891 28.0826 119.158 26.3486C124.491 24.0826 134.358 21.4155 137.425 21.4154ZM139.825 32.7496C131.959 30.0826 129.025 30.0826 123.958 32.7496C114.092 37.8166 108.625 47.2826 108.625 59.0156C108.625 67.9496 111.025 74.7496 115.425 77.8156C119.158 80.3496 129.292 80.6156 133.559 78.2156C139.292 75.0146 145.825 65.6826 147.958 57.8156C150.491 48.2156 150.492 45.0156 148.092 39.4156C146.625 35.9486 144.892 34.4826 139.825 32.7496Z"
        },
        {
            letter: "w",
            path: "M219.959 33.2824C222.892 24.3494 225.426 21.9491 228.226 25.5494C229.026 26.4824 229.959 31.8154 230.359 37.2824C231.026 47.6824 236.093 64.7494 238.76 65.8154C241.159 66.7484 242.36 65.1484 248.359 52.7494C256.626 35.9494 260.226 30.6154 263.293 30.6154C264.759 30.6154 265.959 31.4154 265.959 32.4814C265.959 35.6814 260.76 46.8824 255.293 55.2824C252.493 59.6824 248.626 67.2814 246.626 72.2154C243.026 81.1484 239.159 86.2154 236.893 84.7494C236.226 84.3494 234.626 81.1484 233.426 77.6814C232.092 74.2154 230.226 69.1484 229.293 66.6154C228.226 64.0824 226.759 59.4154 225.959 56.2154C225.159 52.8824 223.693 50.6154 222.626 50.6154C221.426 50.6154 217.426 57.2824 212.76 67.0154C202.76 87.5494 199.293 90.8814 197.56 81.6814C196.226 75.1484 187.293 51.5484 182.626 42.2154C180.093 37.1484 177.959 31.5494 177.959 29.8154C177.959 26.8824 178.493 26.4824 181.56 26.8824C185.693 27.4154 187.959 31.2824 194.492 49.6814C200.092 65.1474 201.693 68.2154 203.426 67.2824C205.292 65.9494 217.292 41.6824 219.959 33.2824Z"
        },
        {
            letter: "r",
            path: "M34.7586 144.216C36.8916 140.883 40.8916 140.35 43.1576 143.149C43.9576 144.216 45.2906 148.483 45.9576 152.616C47.9576 165.283 52.4916 168.483 61.0246 163.55C64.0916 161.683 67.6916 159.016 69.1576 157.416C74.4916 151.549 87.9576 151.016 92.6246 156.616C96.6246 161.416 91.2906 165.949 76.2246 170.616C66.0906 173.816 56.8916 180.216 55.8246 185.016C55.4246 187.017 53.8246 191.549 52.3576 195.149C49.5576 201.949 49.5576 211.683 52.0916 231.416C53.1576 239.949 50.0916 247.016 45.5586 246.349C43.5586 246.083 41.9586 243.949 39.8246 238.216C36.7586 230.482 36.3576 223.416 36.6246 186.349C36.6246 180.083 36.3576 174.084 35.9576 173.016C35.5576 172.083 34.7576 165.816 34.0916 159.283C33.0246 149.55 33.1586 146.749 34.7586 144.216Z"
        },
        {
            letter: "u",
            path: "M148.892 228.881C148.092 209.814 152.092 200.481 159.691 204.214C161.825 205.28 162.092 207.281 161.825 221.414C161.692 236.214 161.958 237.947 165.158 244.614C172.758 260.081 184.759 263.414 190.759 251.548C191.959 249.148 194.358 245.814 195.958 243.947C200.091 239.414 201.958 231.947 201.958 219.814C201.958 211.681 202.491 209.28 204.491 207.414C207.558 204.614 210.092 205.548 213.158 210.748C215.158 214.214 215.425 216.481 214.625 224.748C213.825 233.281 212.625 237.014 205.825 250.347C198.892 263.947 197.158 266.481 191.425 270.347C185.158 274.614 184.891 274.614 177.958 273.147C172.092 271.947 169.558 270.348 163.958 264.748C153.558 254.348 149.558 244.881 148.892 228.881Z"
        }
    ];
    const positions = isMobile ? [
        [
            -1.2,
            1.2,
            0
        ],
        [
            0,
            0.6,
            0
        ],
        [
            1.2,
            0,
            0
        ],
        [
            -0.6,
            -0.6,
            0
        ],
        [
            0.6,
            -1.2,
            0
        ]
    ] // Mobile: disposition verticale compacte
     : [
        [
            -4.5,
            0,
            0
        ],
        [
            -2.2,
            0,
            0
        ],
        [
            0,
            0,
            0
        ],
        [
            2.2,
            0,
            0
        ],
        [
            4.5,
            0,
            0
        ]
    ]; // Desktop: disposition horizontale plus espacée
    const handleLetterClick = (letter, index)=>{
        onLetterClick?.(letter, index);
    };
    const handleLetterHover = (index, hovered)=>{
        const newHovered = [
            ...hoveredLetters
        ];
        newHovered[index] = hovered;
        setHoveredLetters(newHovered);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `w-full h-screen ${className}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$react$2d$three$2d$fiber$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Canvas"], {
            camera: {
                position: isMobile ? [
                    0,
                    0,
                    8
                ] : [
                    0,
                    0,
                    15
                ],
                fov: isMobile ? 70 : 65
            },
            gl: {
                antialias: true,
                alpha: true
            },
            style: {
                width: "100%",
                height: "100%"
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ambientLight", {
                    intensity: 0.3
                }, void 0, false, {
                    fileName: "[project]/src/components/SVG3DLogo.tsx",
                    lineNumber: 216,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pointLight", {
                    position: [
                        10,
                        10,
                        10
                    ],
                    intensity: 1.2,
                    color: "#ffffff"
                }, void 0, false, {
                    fileName: "[project]/src/components/SVG3DLogo.tsx",
                    lineNumber: 217,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pointLight", {
                    position: [
                        -10,
                        -10,
                        -10
                    ],
                    intensity: 0.6,
                    color: "#8892b0"
                }, void 0, false, {
                    fileName: "[project]/src/components/SVG3DLogo.tsx",
                    lineNumber: 218,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pointLight", {
                    position: [
                        0,
                        15,
                        5
                    ],
                    intensity: 0.8,
                    color: "#64ffda"
                }, void 0, false, {
                    fileName: "[project]/src/components/SVG3DLogo.tsx",
                    lineNumber: 223,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("spotLight", {
                    position: [
                        0,
                        10,
                        10
                    ],
                    angle: 0.3,
                    penumbra: 1,
                    intensity: 0.5,
                    castShadow: true
                }, void 0, false, {
                    fileName: "[project]/src/components/SVG3DLogo.tsx",
                    lineNumber: 224,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Environment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Environment"], {
                    preset: "night"
                }, void 0, false, {
                    fileName: "[project]/src/components/SVG3DLogo.tsx",
                    lineNumber: 232,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Center$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Center"], {
                    children: letters.map((letterData, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Letter3D, {
                            svgPath: letterData.path,
                            position: positions[index],
                            index: index,
                            isActive: activeLetters[index],
                            isHovered: hoveredLetters[index],
                            onClick: ()=>handleLetterClick(letterData.letter, index),
                            onHover: (hovered)=>handleLetterHover(index, hovered)
                        }, letterData.letter, false, {
                            fileName: "[project]/src/components/SVG3DLogo.tsx",
                            lineNumber: 236,
                            columnNumber: 13
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/components/SVG3DLogo.tsx",
                    lineNumber: 234,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$OrbitControls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OrbitControls"], {
                    enablePan: false,
                    enableZoom: isMobile ? false : true,
                    enableRotate: true,
                    autoRotate: !isMobile,
                    autoRotateSpeed: 0.3,
                    maxDistance: isMobile ? 10 : 18,
                    minDistance: isMobile ? 5 : 8,
                    maxPolarAngle: Math.PI / 1.5,
                    minPolarAngle: Math.PI / 3
                }, void 0, false, {
                    fileName: "[project]/src/components/SVG3DLogo.tsx",
                    lineNumber: 249,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/SVG3DLogo.tsx",
            lineNumber: 208,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/SVG3DLogo.tsx",
        lineNumber: 207,
        columnNumber: 5
    }, this);
}
_s1(SVG3DLogo, "qlGOXtEBOmTWh32MdzCuAhTmahQ=");
_c1 = SVG3DLogo;
var _c, _c1;
__turbopack_context__.k.register(_c, "Letter3D");
__turbopack_context__.k.register(_c1, "SVG3DLogo");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/InteractiveGame.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "InteractiveGame": (()=>InteractiveGame)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
function InteractiveGame({ onGameComplete, onLetterActivate }) {
    _s();
    const [gameState, setGameState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        level: 1,
        score: 0,
        lives: 3,
        sequence: [],
        playerSequence: [],
        isPlaying: false,
        isShowingSequence: false,
        gameOver: false,
        victory: false
    });
    const [activeButton, setActiveButton] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InteractiveGame.useEffect": ()=>{
            const checkMobile = {
                "InteractiveGame.useEffect.checkMobile": ()=>{
                    setIsMobile(window.innerWidth < 768);
                }
            }["InteractiveGame.useEffect.checkMobile"];
            checkMobile();
            window.addEventListener("resize", checkMobile);
            return ({
                "InteractiveGame.useEffect": ()=>window.removeEventListener("resize", checkMobile)
            })["InteractiveGame.useEffect"];
        }
    }["InteractiveGame.useEffect"], []);
    const letters = [
        "h",
        "o",
        "w",
        "r",
        "u"
    ];
    const colors = [
        "#ff6b6b",
        "#4ecdc4",
        "#45b7d1",
        "#96ceb4",
        "#feca57"
    ];
    const generateSequence = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "InteractiveGame.useCallback[generateSequence]": (level)=>{
            const sequence = [];
            for(let i = 0; i < level + 2; i++){
                sequence.push(Math.floor(Math.random() * 5));
            }
            return sequence;
        }
    }["InteractiveGame.useCallback[generateSequence]"], []);
    const startGame = ()=>{
        const newSequence = generateSequence(1);
        setGameState({
            level: 1,
            score: 0,
            lives: 3,
            sequence: newSequence,
            playerSequence: [],
            isPlaying: true,
            isShowingSequence: true,
            gameOver: false,
            victory: false
        });
        showSequence(newSequence);
    };
    const showSequence = async (sequence)=>{
        setGameState((prev)=>({
                ...prev,
                isShowingSequence: true
            }));
        for(let i = 0; i < sequence.length; i++){
            await new Promise((resolve)=>setTimeout(resolve, 600));
            setActiveButton(sequence[i]);
            onLetterActivate?.(sequence[i]);
            await new Promise((resolve)=>setTimeout(resolve, 400));
            setActiveButton(null);
        }
        setGameState((prev)=>({
                ...prev,
                isShowingSequence: false
            }));
    };
    const handleButtonClick = (index)=>{
        if (gameState.isShowingSequence || gameState.gameOver || gameState.victory) return;
        const newPlayerSequence = [
            ...gameState.playerSequence,
            index
        ];
        setActiveButton(index);
        onLetterActivate?.(index);
        setTimeout(()=>setActiveButton(null), 200);
        // Vérifier si le joueur a fait une erreur
        if (newPlayerSequence[newPlayerSequence.length - 1] !== gameState.sequence[newPlayerSequence.length - 1]) {
            // Erreur !
            const newLives = gameState.lives - 1;
            if (newLives <= 0) {
                setGameState((prev)=>({
                        ...prev,
                        gameOver: true,
                        isPlaying: false
                    }));
            } else {
                setGameState((prev)=>({
                        ...prev,
                        lives: newLives,
                        playerSequence: [],
                        isShowingSequence: true
                    }));
                setTimeout(()=>showSequence(gameState.sequence), 1000);
            }
            return;
        }
        // Vérifier si la séquence est complète
        if (newPlayerSequence.length === gameState.sequence.length) {
            const newScore = gameState.score + gameState.level * 100;
            const newLevel = gameState.level + 1;
            if (newLevel > 10) {
                // Victoire !
                setGameState((prev)=>({
                        ...prev,
                        victory: true,
                        isPlaying: false,
                        score: newScore
                    }));
                onGameComplete?.(newScore);
            } else {
                // Niveau suivant
                const newSequence = generateSequence(newLevel);
                setGameState((prev)=>({
                        ...prev,
                        level: newLevel,
                        score: newScore,
                        sequence: newSequence,
                        playerSequence: [],
                        isShowingSequence: true
                    }));
                setTimeout(()=>showSequence(newSequence), 1500);
            }
        } else {
            setGameState((prev)=>({
                    ...prev,
                    playerSequence: newPlayerSequence
                }));
        }
    };
    const resetGame = ()=>{
        setGameState({
            level: 1,
            score: 0,
            lives: 3,
            sequence: [],
            playerSequence: [],
            isPlaying: false,
            isShowingSequence: false,
            gameOver: false,
            victory: false
        });
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `w-full max-w-2xl mx-auto p-6 ${isMobile ? "px-4" : "px-6"}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0,
                    y: 20
                },
                animate: {
                    opacity: 1,
                    y: 0
                },
                className: "text-center mb-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-whisper-300 text-2xl font-light mb-4",
                        children: 'Jeu de Mémoire "how r u"'
                    }, void 0, false, {
                        fileName: "[project]/src/components/InteractiveGame.tsx",
                        lineNumber: 180,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-whisper-500 text-sm font-light mb-6",
                        children: "Mémorise et reproduis la séquence de lettres"
                    }, void 0, false, {
                        fileName: "[project]/src/components/InteractiveGame.tsx",
                        lineNumber: 183,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: `flex ${isMobile ? "flex-col space-y-2" : "justify-center space-x-8"} mb-6`,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-whisper-400",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-whisper-300",
                                        children: "Niveau:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/InteractiveGame.tsx",
                                        lineNumber: 194,
                                        columnNumber: 13
                                    }, this),
                                    " ",
                                    gameState.level
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/InteractiveGame.tsx",
                                lineNumber: 193,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-whisper-400",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-whisper-300",
                                        children: "Score:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/InteractiveGame.tsx",
                                        lineNumber: 197,
                                        columnNumber: 13
                                    }, this),
                                    " ",
                                    gameState.score
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/InteractiveGame.tsx",
                                lineNumber: 196,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-whisper-400",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-whisper-300",
                                        children: "Vies:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/InteractiveGame.tsx",
                                        lineNumber: 200,
                                        columnNumber: 13
                                    }, this),
                                    Array.from({
                                        length: gameState.lives
                                    }).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-red-400 ml-1",
                                            children: "♥"
                                        }, i, false, {
                                            fileName: "[project]/src/components/InteractiveGame.tsx",
                                            lineNumber: 202,
                                            columnNumber: 15
                                        }, this))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/InteractiveGame.tsx",
                                lineNumber: 199,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/InteractiveGame.tsx",
                        lineNumber: 188,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/InteractiveGame.tsx",
                lineNumber: 175,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `grid ${isMobile ? "grid-cols-2 gap-4 max-w-xs mx-auto" : "grid-cols-5 gap-6 max-w-2xl mx-auto"} mb-8`,
                children: [
                    letters.map((letter, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                            onClick: ()=>handleButtonClick(index),
                            disabled: gameState.isShowingSequence || gameState.gameOver || gameState.victory,
                            className: `
              ${isMobile ? "h-20 w-20 text-lg" : "h-24 w-24 text-2xl"}
              rounded-xl border-3 font-mono font-bold
              transition-all duration-300 transform backdrop-blur-sm
              ${activeButton === index ? "scale-110 shadow-2xl" : "hover:scale-105"}
              ${gameState.isShowingSequence || gameState.gameOver || gameState.victory ? "opacity-40 cursor-not-allowed" : "cursor-pointer hover:shadow-lg"}
            `,
                            style: {
                                backgroundColor: activeButton === index ? colors[index] : "rgba(255,255,255,0.05)",
                                borderColor: colors[index],
                                color: activeButton === index ? "#000" : colors[index],
                                boxShadow: activeButton === index ? `0 0 30px ${colors[index]}` : "none"
                            },
                            whileTap: {
                                scale: 0.9
                            },
                            whileHover: {
                                boxShadow: `0 0 20px ${colors[index]}40`,
                                borderWidth: "3px"
                            },
                            children: letter
                        }, letter, false, {
                            fileName: "[project]/src/components/InteractiveGame.tsx",
                            lineNumber: 219,
                            columnNumber: 11
                        }, this)),
                    isMobile && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "col-span-2 flex justify-center",
                        initial: {
                            opacity: 0
                        },
                        animate: {
                            opacity: 1
                        },
                        transition: {
                            delay: 0.5
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                            onClick: ()=>handleButtonClick(4),
                            disabled: gameState.isShowingSequence || gameState.gameOver || gameState.victory,
                            className: `
                h-20 w-20 text-lg rounded-xl border-3 font-mono font-bold
                transition-all duration-300 transform backdrop-blur-sm
                ${activeButton === 4 ? "scale-110 shadow-2xl" : "hover:scale-105"}
                ${gameState.isShowingSequence || gameState.gameOver || gameState.victory ? "opacity-40 cursor-not-allowed" : "cursor-pointer hover:shadow-lg"}
              `,
                            style: {
                                backgroundColor: activeButton === 4 ? colors[4] : "rgba(255,255,255,0.05)",
                                borderColor: colors[4],
                                color: activeButton === 4 ? "#000" : colors[4],
                                boxShadow: activeButton === 4 ? `0 0 30px ${colors[4]}` : "none"
                            },
                            whileTap: {
                                scale: 0.9
                            },
                            children: letters[4]
                        }, void 0, false, {
                            fileName: "[project]/src/components/InteractiveGame.tsx",
                            lineNumber: 271,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/InteractiveGame.tsx",
                        lineNumber: 265,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/InteractiveGame.tsx",
                lineNumber: 211,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                children: [
                    gameState.isShowingSequence && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        initial: {
                            opacity: 0
                        },
                        animate: {
                            opacity: 1
                        },
                        exit: {
                            opacity: 0
                        },
                        className: "text-center text-whisper-400 mb-4",
                        children: "Mémorise la séquence..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/InteractiveGame.tsx",
                        lineNumber: 313,
                        columnNumber: 11
                    }, this),
                    gameState.gameOver && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        initial: {
                            opacity: 0,
                            scale: 0.8
                        },
                        animate: {
                            opacity: 1,
                            scale: 1
                        },
                        className: "text-center mb-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-red-400 text-xl mb-2",
                                children: "Game Over"
                            }, void 0, false, {
                                fileName: "[project]/src/components/InteractiveGame.tsx",
                                lineNumber: 329,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-whisper-500 text-sm",
                                children: [
                                    "Score final: ",
                                    gameState.score
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/InteractiveGame.tsx",
                                lineNumber: 330,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/InteractiveGame.tsx",
                        lineNumber: 324,
                        columnNumber: 11
                    }, this),
                    gameState.victory && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        initial: {
                            opacity: 0,
                            scale: 0.8
                        },
                        animate: {
                            opacity: 1,
                            scale: 1
                        },
                        className: "text-center mb-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-green-400 text-xl mb-2",
                                children: "Victoire ! 🎉"
                            }, void 0, false, {
                                fileName: "[project]/src/components/InteractiveGame.tsx",
                                lineNumber: 342,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-whisper-500 text-sm",
                                children: [
                                    "Score final: ",
                                    gameState.score
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/InteractiveGame.tsx",
                                lineNumber: 343,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/InteractiveGame.tsx",
                        lineNumber: 337,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/InteractiveGame.tsx",
                lineNumber: 311,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center space-y-4",
                children: [
                    !gameState.isPlaying && !gameState.gameOver && !gameState.victory && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                        onClick: startGame,
                        className: "px-6 py-3 bg-ghost-100 border border-whisper-200 text-whisper-300 font-light hover:bg-ghost-200 transition-colors",
                        whileHover: {
                            scale: 1.05
                        },
                        whileTap: {
                            scale: 0.95
                        },
                        children: "Commencer le Jeu"
                    }, void 0, false, {
                        fileName: "[project]/src/components/InteractiveGame.tsx",
                        lineNumber: 353,
                        columnNumber: 11
                    }, this),
                    (gameState.gameOver || gameState.victory) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
                        onClick: resetGame,
                        className: "px-6 py-3 bg-ghost-100 border border-whisper-200 text-whisper-300 font-light hover:bg-ghost-200 transition-colors",
                        whileHover: {
                            scale: 1.05
                        },
                        whileTap: {
                            scale: 0.95
                        },
                        children: "Rejouer"
                    }, void 0, false, {
                        fileName: "[project]/src/components/InteractiveGame.tsx",
                        lineNumber: 364,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/InteractiveGame.tsx",
                lineNumber: 351,
                columnNumber: 7
            }, this),
            !gameState.isPlaying && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                initial: {
                    opacity: 0
                },
                animate: {
                    opacity: 1
                },
                className: "text-center mt-8 text-whisper-600 text-xs font-light",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "Regarde la séquence, puis reproduis-la en cliquant sur les lettres"
                    }, void 0, false, {
                        fileName: "[project]/src/components/InteractiveGame.tsx",
                        lineNumber: 382,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mt-1",
                        children: "10 niveaux à compléter pour gagner !"
                    }, void 0, false, {
                        fileName: "[project]/src/components/InteractiveGame.tsx",
                        lineNumber: 385,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/InteractiveGame.tsx",
                lineNumber: 377,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/InteractiveGame.tsx",
        lineNumber: 172,
        columnNumber: 5
    }, this);
}
_s(InteractiveGame, "vgXUPZY/8ZoVtNEpX8kelQlg86M=");
_c = InteractiveGame;
var _c;
__turbopack_context__.k.register(_c, "InteractiveGame");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/MysticSoundscape.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "MysticSoundscape": (()=>MysticSoundscape),
    "useMysticSounds": (()=>useMysticSounds)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
"use client";
;
function MysticSoundscape({ isActive = true, intensity = 1, phase = "awakening" }) {
    _s();
    const audioContextRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const oscillatorsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]);
    const gainNodesRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]);
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MysticSoundscape.useEffect": ()=>{
            if (!isActive) return;
            const initAudio = {
                "MysticSoundscape.useEffect.initAudio": ()=>{
                    try {
                        // Créer le contexte audio
                        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
                        // Créer les oscillateurs pour différentes fréquences mystiques
                        const frequencies = [
                            55,
                            110,
                            220,
                            440,
                            880
                        ];
                        frequencies.forEach({
                            "MysticSoundscape.useEffect.initAudio": (freq, index)=>{
                                const oscillator = audioContextRef.current.createOscillator();
                                const gainNode = audioContextRef.current.createGain();
                                // Configuration de l'oscillateur
                                oscillator.type = index % 2 === 0 ? 'sine' : 'triangle';
                                oscillator.frequency.setValueAtTime(freq, audioContextRef.current.currentTime);
                                // Configuration du gain (volume très bas pour l'ambiance)
                                gainNode.gain.setValueAtTime(0.01 * intensity, audioContextRef.current.currentTime);
                                // Connexion audio
                                oscillator.connect(gainNode);
                                gainNode.connect(audioContextRef.current.destination);
                                // Démarrer l'oscillateur
                                oscillator.start();
                                // Stocker les références
                                oscillatorsRef.current.push(oscillator);
                                gainNodesRef.current.push(gainNode);
                            }
                        }["MysticSoundscape.useEffect.initAudio"]);
                        setIsInitialized(true);
                    } catch (error) {
                        console.log("Audio context not available:", error);
                    }
                }
            }["MysticSoundscape.useEffect.initAudio"];
            // Initialiser l'audio au premier clic de l'utilisateur
            const handleUserInteraction = {
                "MysticSoundscape.useEffect.handleUserInteraction": ()=>{
                    if (!isInitialized) {
                        initAudio();
                        document.removeEventListener('click', handleUserInteraction);
                        document.removeEventListener('keydown', handleUserInteraction);
                    }
                }
            }["MysticSoundscape.useEffect.handleUserInteraction"];
            document.addEventListener('click', handleUserInteraction);
            document.addEventListener('keydown', handleUserInteraction);
            return ({
                "MysticSoundscape.useEffect": ()=>{
                    document.removeEventListener('click', handleUserInteraction);
                    document.removeEventListener('keydown', handleUserInteraction);
                }
            })["MysticSoundscape.useEffect"];
        }
    }["MysticSoundscape.useEffect"], [
        isActive,
        intensity,
        isInitialized
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MysticSoundscape.useEffect": ()=>{
            if (!isInitialized || !audioContextRef.current) return;
            // Ajuster les fréquences et volumes selon la phase
            const phaseSettings = {
                awakening: {
                    baseFreq: 55,
                    volume: 0.005
                },
                exploration: {
                    baseFreq: 110,
                    volume: 0.008
                },
                ritual: {
                    baseFreq: 220,
                    volume: 0.012
                },
                transcendence: {
                    baseFreq: 440,
                    volume: 0.015
                },
                void: {
                    baseFreq: 880,
                    volume: 0.020
                }
            };
            const settings = phaseSettings[phase];
            const currentTime = audioContextRef.current.currentTime;
            oscillatorsRef.current.forEach({
                "MysticSoundscape.useEffect": (oscillator, index)=>{
                    if (oscillator && gainNodesRef.current[index]) {
                        // Modulation de fréquence pour créer un effet mystique
                        const modulation = Math.sin(currentTime * 0.1 + index) * 0.1;
                        const newFreq = settings.baseFreq * (index + 1) * (1 + modulation);
                        try {
                            oscillator.frequency.setTargetAtTime(newFreq, currentTime, 2 // Transition douce de 2 secondes
                            );
                            gainNodesRef.current[index].gain.setTargetAtTime(settings.volume * intensity * (1 + modulation * 0.5), currentTime, 1);
                        } catch (error) {
                        // Oscillateur peut-être déjà arrêté
                        }
                    }
                }
            }["MysticSoundscape.useEffect"]);
        }
    }["MysticSoundscape.useEffect"], [
        phase,
        intensity,
        isInitialized
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MysticSoundscape.useEffect": ()=>{
            return ({
                "MysticSoundscape.useEffect": ()=>{
                    // Nettoyage lors du démontage
                    oscillatorsRef.current.forEach({
                        "MysticSoundscape.useEffect": (oscillator)=>{
                            try {
                                oscillator.stop();
                                oscillator.disconnect();
                            } catch (error) {
                            // Oscillateur déjà arrêté
                            }
                        }
                    }["MysticSoundscape.useEffect"]);
                    gainNodesRef.current.forEach({
                        "MysticSoundscape.useEffect": (gainNode)=>{
                            try {
                                gainNode.disconnect();
                            } catch (error) {
                            // Gain node déjà déconnecté
                            }
                        }
                    }["MysticSoundscape.useEffect"]);
                    if (audioContextRef.current) {
                        audioContextRef.current.close();
                    }
                }
            })["MysticSoundscape.useEffect"];
        }
    }["MysticSoundscape.useEffect"], []);
    // Fonction pour créer des effets sonores ponctuels
    const playMysticChime = (frequency = 440, duration = 1000)=>{
        if (!audioContextRef.current) return;
        const oscillator = audioContextRef.current.createOscillator();
        const gainNode = audioContextRef.current.createGain();
        oscillator.type = 'sine';
        oscillator.frequency.setValueAtTime(frequency, audioContextRef.current.currentTime);
        // Enveloppe ADSR pour un son de cloche mystique
        const currentTime = audioContextRef.current.currentTime;
        gainNode.gain.setValueAtTime(0, currentTime);
        gainNode.gain.linearRampToValueAtTime(0.1 * intensity, currentTime + 0.1);
        gainNode.gain.exponentialRampToValueAtTime(0.001, currentTime + duration / 1000);
        oscillator.connect(gainNode);
        gainNode.connect(audioContextRef.current.destination);
        oscillator.start(currentTime);
        oscillator.stop(currentTime + duration / 1000);
    };
    // Exposer la fonction pour les autres composants
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MysticSoundscape.useEffect": ()=>{
            window.playMysticChime = playMysticChime;
            return ({
                "MysticSoundscape.useEffect": ()=>{
                    delete window.playMysticChime;
                }
            })["MysticSoundscape.useEffect"];
        }
    }["MysticSoundscape.useEffect"], [
        intensity
    ]);
    return null; // Ce composant ne rend rien visuellement
}
_s(MysticSoundscape, "K93ewTrEt4e2xwBCRgLMoxIk3fY=");
_c = MysticSoundscape;
function useMysticSounds() {
    const playChime = (frequency, duration)=>{
        if (window.playMysticChime) {
            window.playMysticChime(frequency, duration);
        }
    };
    const playLetterSound = (letterIndex)=>{
        const frequencies = [
            220,
            277,
            330,
            392,
            440
        ]; // Gamme pentatonique mystique
        playChime(frequencies[letterIndex % frequencies.length], 800);
    };
    const playSecretSound = ()=>{
        // Son spécial pour les secrets révélés
        playChime(880, 1500);
        setTimeout(()=>playChime(660, 1000), 200);
        setTimeout(()=>playChime(440, 1200), 400);
    };
    const playRitualSound = ()=>{
        // Séquence sonore pour les rituels
        const sequence = [
            110,
            165,
            220,
            330,
            440
        ];
        sequence.forEach((freq, index)=>{
            setTimeout(()=>playChime(freq, 600), index * 300);
        });
    };
    return {
        playChime,
        playLetterSound,
        playSecretSound,
        playRitualSound
    };
}
var _c;
__turbopack_context__.k.register(_c, "MysticSoundscape");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/MysticInteractiveGame.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "MysticInteractiveGame": (()=>MysticInteractiveGame)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$react$2d$three$2d$fiber$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js [app-client] (ecmascript) <export C as useFrame>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Float$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Float.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Text3D$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Text3D.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Environment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/drei/core/Environment.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/three/build/three.core.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$MysticSoundscape$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/MysticSoundscape.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
function MysticLetter({ letter, position, index, isActive, isChanneling, mysticalEnergy, onClick }) {
    _s();
    const meshRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [isHovered, setIsHovered] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"])({
        "MysticLetter.useFrame": (state)=>{
            if (meshRef.current) {
                // Animation mystique de base
                const time = state.clock.elapsedTime;
                meshRef.current.position.y = position[1] + Math.sin(time + index * 2) * 0.3;
                // Rotation mystique
                meshRef.current.rotation.x = Math.sin(time * 0.5 + index) * 0.2;
                meshRef.current.rotation.z = Math.cos(time * 0.3 + index) * 0.1;
                if (isChanneling) {
                    // Animation de canalisation d'énergie
                    meshRef.current.rotation.y += 0.05;
                    meshRef.current.scale.setScalar(1.5 + Math.sin(time * 8) * 0.3);
                    // Effet de pulsation mystique
                    const intensity = 1 + Math.sin(time * 10) * 0.5;
                    meshRef.current.material.emissive.setRGB(intensity * 0.3, intensity * 0.1, intensity * 0.8);
                } else if (isActive) {
                    meshRef.current.rotation.y += 0.02;
                    meshRef.current.scale.setScalar(1.2 + Math.sin(time * 4) * 0.1);
                    // Lueur surnaturelle
                    meshRef.current.material.emissive.setRGB(0.2, 0.05, 0.4);
                } else if (isHovered) {
                    meshRef.current.scale.setScalar(1.1);
                    meshRef.current.material.emissive.setRGB(0.1, 0.02, 0.2);
                } else {
                    meshRef.current.scale.setScalar(1);
                    meshRef.current.material.emissive.setRGB(0, 0, 0);
                }
            }
        }
    }["MysticLetter.useFrame"]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Float$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Float"], {
        speed: 3,
        rotationIntensity: 0.8,
        floatIntensity: 0.6,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Text3D$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text3D"], {
            ref: meshRef,
            font: "/fonts/helvetiker_regular.typeface.json",
            size: 1.8,
            height: 0.8,
            position: position,
            onPointerOver: ()=>setIsHovered(true),
            onPointerOut: ()=>setIsHovered(false),
            onClick: onClick,
            curveSegments: 24,
            children: [
                letter,
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meshStandardMaterial", {
                    color: isChanneling ? "#8a2be2" : isActive ? "#6a0dad" : isHovered ? "#4b0082" : "#2e2e2e",
                    metalness: 0.9,
                    roughness: 0.1,
                    emissive: "#000000"
                }, void 0, false, {
                    fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                    lineNumber: 97,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/MysticInteractiveGame.tsx",
            lineNumber: 85,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/MysticInteractiveGame.tsx",
        lineNumber: 84,
        columnNumber: 5
    }, this);
}
_s(MysticLetter, "xXKdNnruWsIULWPWIc8MDuW/ez0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"]
    ];
});
_c = MysticLetter;
function MysticParticles() {
    _s1();
    const pointsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const particleCount = 2000;
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    for(let i = 0; i < particleCount; i++){
        positions[i * 3] = (Math.random() - 0.5) * 50;
        positions[i * 3 + 1] = (Math.random() - 0.5) * 50;
        positions[i * 3 + 2] = (Math.random() - 0.5) * 50;
        // Couleurs mystiques
        colors[i * 3] = Math.random() * 0.5 + 0.3; // Rouge
        colors[i * 3 + 1] = Math.random() * 0.2; // Vert
        colors[i * 3 + 2] = Math.random() * 0.8 + 0.2; // Bleu
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"])({
        "MysticParticles.useFrame": (state)=>{
            if (pointsRef.current) {
                pointsRef.current.rotation.y = state.clock.elapsedTime * 0.02;
                pointsRef.current.rotation.x = state.clock.elapsedTime * 0.01;
            }
        }
    }["MysticParticles.useFrame"]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("points", {
        ref: pointsRef,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("bufferGeometry", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("bufferAttribute", {
                        attach: "attributes-position",
                        count: particleCount,
                        array: positions,
                        itemSize: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                        lineNumber: 144,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("bufferAttribute", {
                        attach: "attributes-color",
                        count: particleCount,
                        array: colors,
                        itemSize: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                        lineNumber: 150,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                lineNumber: 143,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pointsMaterial", {
                size: 0.05,
                vertexColors: true,
                transparent: true,
                opacity: 0.8,
                sizeAttenuation: true,
                blending: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AdditiveBlending"]
            }, void 0, false, {
                fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                lineNumber: 157,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/MysticInteractiveGame.tsx",
        lineNumber: 142,
        columnNumber: 5
    }, this);
}
_s1(MysticParticles, "7C7C5y4MprfU2123a3F/mkNQo3Y=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$dc44c1b8$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__useFrame$3e$__["useFrame"]
    ];
});
_c1 = MysticParticles;
function MysticInteractiveGame({ onGameComplete, onSecretUnlocked }) {
    _s2();
    const [gameState, setGameState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        phase: "awakening",
        level: 1,
        souls: 0,
        whispers: [],
        activeLetters: [
            false,
            false,
            false,
            false,
            false
        ],
        mysticalEnergy: 0,
        darkSecrets: [],
        isChanneling: false,
        voidDepth: 0
    });
    const [currentWhisper, setCurrentWhisper] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { playLetterSound, playSecretSound, playRitualSound } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$MysticSoundscape$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMysticSounds"])();
    const letters = [
        "h",
        "o",
        "w",
        "r",
        "u"
    ];
    const positions = isMobile ? [
        [
            -2,
            2,
            0
        ],
        [
            0,
            1,
            0
        ],
        [
            2,
            0,
            0
        ],
        [
            -1,
            -1,
            0
        ],
        [
            1,
            -2,
            0
        ]
    ] : [
        [
            -6,
            0,
            0
        ],
        [
            -3,
            0,
            0
        ],
        [
            0,
            0,
            0
        ],
        [
            3,
            0,
            0
        ],
        [
            6,
            0,
            0
        ]
    ];
    const whispers = [
        "les âmes murmurent dans l'obscurité...",
        "tu entends les échos du vide...",
        "les lettres révèlent leurs secrets...",
        "l'énergie mystique s'éveille...",
        "les ombres dansent autour de toi...",
        "le rituel commence...",
        "tu touches l'essence de l'inconnu...",
        "les dimensions se plient à ta volonté...",
        "tu transcendes la réalité...",
        "le vide t'appelle..."
    ];
    const darkSecrets = [
        "La première lettre cache l'origine de tout",
        "L'ordre des lettres révèle le chemin vers l'au-delà",
        "Cinq âmes sont nécessaires pour ouvrir le portail",
        "Le vide n'est que le début de l'infini",
        "Chaque clic libère une parcelle d'éternité"
    ];
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MysticInteractiveGame.useEffect": ()=>{
            const checkMobile = {
                "MysticInteractiveGame.useEffect.checkMobile": ()=>{
                    setIsMobile(window.innerWidth < 768);
                }
            }["MysticInteractiveGame.useEffect.checkMobile"];
            checkMobile();
            window.addEventListener("resize", checkMobile);
            return ({
                "MysticInteractiveGame.useEffect": ()=>window.removeEventListener("resize", checkMobile)
            })["MysticInteractiveGame.useEffect"];
        }
    }["MysticInteractiveGame.useEffect"], []);
    const handleLetterClick = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "MysticInteractiveGame.useCallback[handleLetterClick]": (index)=>{
            if (gameState.isChanneling) return;
            // Jouer le son de la lettre
            playLetterSound(index);
            const newActiveLetters = [
                ...gameState.activeLetters
            ];
            newActiveLetters[index] = !newActiveLetters[index];
            const activeCount = newActiveLetters.filter(Boolean).length;
            const newMysticalEnergy = Math.min(gameState.mysticalEnergy + 20, 100);
            const newSouls = gameState.souls + (newActiveLetters[index] ? 1 : 0);
            // Nouveau murmure aléatoire
            const randomWhisper = whispers[Math.floor(Math.random() * whispers.length)];
            setCurrentWhisper(randomWhisper);
            // Progression des phases
            let newPhase = gameState.phase;
            if (activeCount >= 3 && gameState.phase === "awakening") {
                newPhase = "exploration";
            } else if (activeCount >= 5 && gameState.phase === "exploration") {
                newPhase = "ritual";
                playRitualSound(); // Son de rituel
                setGameState({
                    "MysticInteractiveGame.useCallback[handleLetterClick]": (prev)=>({
                            ...prev,
                            isChanneling: true
                        })
                }["MysticInteractiveGame.useCallback[handleLetterClick]"]);
                setTimeout({
                    "MysticInteractiveGame.useCallback[handleLetterClick]": ()=>{
                        setGameState({
                            "MysticInteractiveGame.useCallback[handleLetterClick]": (prev)=>({
                                    ...prev,
                                    isChanneling: false,
                                    phase: "transcendence"
                                })
                        }["MysticInteractiveGame.useCallback[handleLetterClick]"]);
                    }
                }["MysticInteractiveGame.useCallback[handleLetterClick]"], 3000);
            }
            // Révélation de secrets
            if (newSouls > 0 && newSouls % 5 === 0 && gameState.darkSecrets.length < darkSecrets.length) {
                const newSecret = darkSecrets[gameState.darkSecrets.length];
                playSecretSound(); // Son spécial pour les secrets
                setGameState({
                    "MysticInteractiveGame.useCallback[handleLetterClick]": (prev)=>({
                            ...prev,
                            darkSecrets: [
                                ...prev.darkSecrets,
                                newSecret
                            ]
                        })
                }["MysticInteractiveGame.useCallback[handleLetterClick]"]);
                onSecretUnlocked?.(newSecret);
            }
            setGameState({
                "MysticInteractiveGame.useCallback[handleLetterClick]": (prev)=>({
                        ...prev,
                        activeLetters: newActiveLetters,
                        mysticalEnergy: newMysticalEnergy,
                        souls: newSouls,
                        phase: newPhase,
                        whispers: [
                            ...prev.whispers.slice(-4),
                            randomWhisper
                        ]
                    })
            }["MysticInteractiveGame.useCallback[handleLetterClick]"]);
            // Completion du jeu
            if (newSouls >= 25) {
                onGameComplete?.(newSouls);
            }
        }
    }["MysticInteractiveGame.useCallback[handleLetterClick]"], [
        gameState,
        onGameComplete,
        onSecretUnlocked,
        playLetterSound,
        playSecretSound,
        playRitualSound
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full h-screen relative bg-black overflow-hidden",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$MysticSoundscape$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MysticSoundscape"], {
                isActive: true,
                intensity: gameState.mysticalEnergy / 100,
                phase: gameState.phase
            }, void 0, false, {
                fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                lineNumber: 321,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$react$2d$three$2d$fiber$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Canvas"], {
                camera: {
                    position: isMobile ? [
                        0,
                        0,
                        10
                    ] : [
                        0,
                        0,
                        18
                    ],
                    fov: isMobile ? 75 : 70
                },
                gl: {
                    antialias: true,
                    alpha: true
                },
                style: {
                    width: "100%",
                    height: "100%"
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ambientLight", {
                        intensity: 0.1
                    }, void 0, false, {
                        fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                        lineNumber: 336,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pointLight", {
                        position: [
                            10,
                            10,
                            10
                        ],
                        intensity: 0.8,
                        color: "#8a2be2"
                    }, void 0, false, {
                        fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                        lineNumber: 337,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("pointLight", {
                        position: [
                            -10,
                            -10,
                            -10
                        ],
                        intensity: 0.5,
                        color: "#4b0082"
                    }, void 0, false, {
                        fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                        lineNumber: 338,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("spotLight", {
                        position: [
                            0,
                            20,
                            10
                        ],
                        angle: 0.5,
                        penumbra: 1,
                        intensity: 1,
                        color: "#6a0dad",
                        castShadow: true
                    }, void 0, false, {
                        fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                        lineNumber: 343,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$drei$2f$core$2f$Environment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Environment"], {
                        preset: "night"
                    }, void 0, false, {
                        fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                        lineNumber: 352,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(MysticParticles, {}, void 0, false, {
                        fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                        lineNumber: 353,
                        columnNumber: 9
                    }, this),
                    letters.map((letter, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(MysticLetter, {
                            letter: letter,
                            position: positions[index],
                            index: index,
                            isActive: gameState.activeLetters[index],
                            isChanneling: gameState.isChanneling,
                            mysticalEnergy: gameState.mysticalEnergy,
                            onClick: ()=>handleLetterClick(index)
                        }, letter, false, {
                            fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                            lineNumber: 356,
                            columnNumber: 11
                        }, this))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                lineNumber: 328,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0 pointer-events-none z-10",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute top-8 left-8 text-purple-300",
                        initial: {
                            opacity: 0
                        },
                        animate: {
                            opacity: 1
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm font-mono mb-2",
                                children: [
                                    "Phase: ",
                                    gameState.phase
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                                lineNumber: 377,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs",
                                children: [
                                    "Âmes: ",
                                    gameState.souls
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                                lineNumber: 378,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs",
                                children: [
                                    "Énergie: ",
                                    gameState.mysticalEnergy,
                                    "%"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                                lineNumber: 379,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                        lineNumber: 372,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                        children: currentWhisper && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                            className: "absolute bottom-20 left-1/2 transform -translate-x-1/2 text-center",
                            initial: {
                                opacity: 0,
                                y: 20
                            },
                            animate: {
                                opacity: 1,
                                y: 0
                            },
                            exit: {
                                opacity: 0,
                                y: -20
                            },
                            transition: {
                                duration: 2
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-purple-200 text-sm font-light italic",
                                children: currentWhisper
                            }, void 0, false, {
                                fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                                lineNumber: 393,
                                columnNumber: 15
                            }, this)
                        }, currentWhisper, false, {
                            fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                            lineNumber: 385,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                        lineNumber: 383,
                        columnNumber: 9
                    }, this),
                    gameState.darkSecrets.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute top-8 right-8 max-w-xs",
                        initial: {
                            opacity: 0,
                            x: 20
                        },
                        animate: {
                            opacity: 1,
                            x: 0
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-purple-300 text-xs font-mono mb-2",
                                children: "Secrets révélés:"
                            }, void 0, false, {
                                fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                                lineNumber: 407,
                                columnNumber: 13
                            }, this),
                            gameState.darkSecrets.map((secret, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                    className: "text-purple-200 text-xs mb-1 opacity-80",
                                    initial: {
                                        opacity: 0
                                    },
                                    animate: {
                                        opacity: 0.8
                                    },
                                    transition: {
                                        delay: index * 0.5
                                    },
                                    children: [
                                        "• ",
                                        secret
                                    ]
                                }, index, true, {
                                    fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                                    lineNumber: 411,
                                    columnNumber: 15
                                }, this))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                        lineNumber: 402,
                        columnNumber: 11
                    }, this),
                    gameState.isChanneling && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute inset-0 bg-purple-900 bg-opacity-30",
                        initial: {
                            opacity: 0
                        },
                        animate: {
                            opacity: [
                                0,
                                0.5,
                                0
                            ]
                        },
                        transition: {
                            duration: 3,
                            repeat: Infinity
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute inset-0 flex items-center justify-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                className: "text-purple-100 text-2xl font-light",
                                animate: {
                                    scale: [
                                        1,
                                        1.2,
                                        1
                                    ]
                                },
                                transition: {
                                    duration: 1,
                                    repeat: Infinity
                                },
                                children: "Canalisation en cours..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                                lineNumber: 433,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                            lineNumber: 432,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                        lineNumber: 426,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        className: "absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center pointer-events-none",
                        initial: {
                            opacity: 0
                        },
                        animate: {
                            opacity: 1
                        },
                        transition: {
                            delay: 2
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-purple-300 text-xs font-light",
                                children: "Cliquez sur les lettres pour éveiller leur pouvoir mystique"
                            }, void 0, false, {
                                fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                                lineNumber: 451,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-purple-400 text-xs font-light mt-1",
                                children: "Collectez des âmes pour révéler les secrets de l'au-delà"
                            }, void 0, false, {
                                fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                                lineNumber: 454,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                        lineNumber: 445,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/MysticInteractiveGame.tsx",
                lineNumber: 370,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/MysticInteractiveGame.tsx",
        lineNumber: 319,
        columnNumber: 5
    }, this);
}
_s2(MysticInteractiveGame, "J0+OvSIcsdZ+DOPNzqSuDUMdnZI=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$MysticSoundscape$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMysticSounds"]
    ];
});
_c2 = MysticInteractiveGame;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "MysticLetter");
__turbopack_context__.k.register(_c1, "MysticParticles");
__turbopack_context__.k.register(_c2, "MysticInteractiveGame");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/MysticAtmosphere.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "MysticAtmosphere": (()=>MysticAtmosphere)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/styled-jsx/style.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
function MysticAtmosphere({ intensity = 1, showRitualCircle = false, isChanneling = false }) {
    _s();
    const canvasRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [dimensions, setDimensions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        width: 0,
        height: 0
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MysticAtmosphere.useEffect": ()=>{
            const updateDimensions = {
                "MysticAtmosphere.useEffect.updateDimensions": ()=>{
                    setDimensions({
                        width: window.innerWidth,
                        height: window.innerHeight
                    });
                }
            }["MysticAtmosphere.useEffect.updateDimensions"];
            updateDimensions();
            window.addEventListener("resize", updateDimensions);
            return ({
                "MysticAtmosphere.useEffect": ()=>window.removeEventListener("resize", updateDimensions)
            })["MysticAtmosphere.useEffect"];
        }
    }["MysticAtmosphere.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MysticAtmosphere.useEffect": ()=>{
            const canvas = canvasRef.current;
            if (!canvas) return;
            const ctx = canvas.getContext("2d");
            if (!ctx) return;
            canvas.width = dimensions.width;
            canvas.height = dimensions.height;
            // Particules mystiques
            const particles = [];
            const colors = [
                "rgba(138, 43, 226, ",
                "rgba(106, 13, 173, ",
                "rgba(75, 0, 130, ",
                "rgba(148, 0, 211, ",
                "rgba(186, 85, 211, "
            ];
            // Créer des particules
            for(let i = 0; i < 150 * intensity; i++){
                particles.push({
                    x: Math.random() * dimensions.width,
                    y: Math.random() * dimensions.height,
                    vx: (Math.random() - 0.5) * 0.5,
                    vy: (Math.random() - 0.5) * 0.5,
                    size: Math.random() * 3 + 1,
                    opacity: Math.random() * 0.8 + 0.2,
                    color: colors[Math.floor(Math.random() * colors.length)],
                    life: Math.random() * 100 + 50
                });
            }
            let animationId;
            const animate = {
                "MysticAtmosphere.useEffect.animate": ()=>{
                    ctx.clearRect(0, 0, dimensions.width, dimensions.height);
                    // Fond mystique avec gradient
                    const gradient = ctx.createRadialGradient(dimensions.width / 2, dimensions.height / 2, 0, dimensions.width / 2, dimensions.height / 2, Math.max(dimensions.width, dimensions.height) / 2);
                    gradient.addColorStop(0, "rgba(25, 0, 50, 0.1)");
                    gradient.addColorStop(0.5, "rgba(50, 0, 100, 0.05)");
                    gradient.addColorStop(1, "rgba(0, 0, 0, 0.2)");
                    ctx.fillStyle = gradient;
                    ctx.fillRect(0, 0, dimensions.width, dimensions.height);
                    // Animer les particules
                    particles.forEach({
                        "MysticAtmosphere.useEffect.animate": (particle, index)=>{
                            particle.x += particle.vx;
                            particle.y += particle.vy;
                            particle.life--;
                            // Effet de canalisation
                            if (isChanneling) {
                                const centerX = dimensions.width / 2;
                                const centerY = dimensions.height / 2;
                                const dx = centerX - particle.x;
                                const dy = centerY - particle.y;
                                const distance = Math.sqrt(dx * dx + dy * dy);
                                if (distance > 50) {
                                    particle.vx += dx * 0.0001;
                                    particle.vy += dy * 0.0001;
                                }
                                particle.opacity = Math.min(1, particle.opacity + 0.02);
                                particle.size = Math.min(5, particle.size + 0.05);
                            }
                            // Rebond sur les bords
                            if (particle.x < 0 || particle.x > dimensions.width) particle.vx *= -1;
                            if (particle.y < 0 || particle.y > dimensions.height) particle.vy *= -1;
                            // Dessiner la particule
                            ctx.save();
                            ctx.globalAlpha = particle.opacity;
                            ctx.fillStyle = particle.color + particle.opacity + ")";
                            ctx.beginPath();
                            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                            ctx.fill();
                            // Effet de lueur
                            ctx.shadowBlur = 20;
                            ctx.shadowColor = particle.color + "0.8)";
                            ctx.fill();
                            ctx.restore();
                            // Régénérer les particules mortes
                            if (particle.life <= 0) {
                                particles[index] = {
                                    x: Math.random() * dimensions.width,
                                    y: Math.random() * dimensions.height,
                                    vx: (Math.random() - 0.5) * 0.5,
                                    vy: (Math.random() - 0.5) * 0.5,
                                    size: Math.random() * 3 + 1,
                                    opacity: Math.random() * 0.8 + 0.2,
                                    color: colors[Math.floor(Math.random() * colors.length)],
                                    life: Math.random() * 100 + 50
                                };
                            }
                        }
                    }["MysticAtmosphere.useEffect.animate"]);
                    // Cercle rituel
                    if (showRitualCircle) {
                        const centerX = dimensions.width / 2;
                        const centerY = dimensions.height / 2;
                        const radius = Math.min(dimensions.width, dimensions.height) * 0.3;
                        ctx.save();
                        ctx.strokeStyle = "rgba(138, 43, 226, 0.6)";
                        ctx.lineWidth = 2;
                        ctx.setLineDash([
                            10,
                            5
                        ]);
                        ctx.lineDashOffset = Date.now() * 0.01;
                        ctx.beginPath();
                        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
                        ctx.stroke();
                        // Symboles mystiques autour du cercle
                        const symbols = [
                            "✦",
                            "✧",
                            "✩",
                            "✪",
                            "✫",
                            "✬"
                        ];
                        for(let i = 0; i < 6; i++){
                            const angle = i / 6 * Math.PI * 2;
                            const x = centerX + Math.cos(angle) * (radius + 30);
                            const y = centerY + Math.sin(angle) * (radius + 30);
                            ctx.fillStyle = "rgba(186, 85, 211, 0.8)";
                            ctx.font = "20px serif";
                            ctx.textAlign = "center";
                            ctx.fillText(symbols[i], x, y);
                        }
                        ctx.restore();
                    }
                    animationId = requestAnimationFrame(animate);
                }
            }["MysticAtmosphere.useEffect.animate"];
            animate();
            return ({
                "MysticAtmosphere.useEffect": ()=>{
                    if (animationId) {
                        cancelAnimationFrame(animationId);
                    }
                }
            })["MysticAtmosphere.useEffect"];
        }
    }["MysticAtmosphere.useEffect"], [
        dimensions,
        intensity,
        showRitualCircle,
        isChanneling
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("canvas", {
                ref: canvasRef,
                style: {
                    mixBlendMode: "screen"
                },
                className: "jsx-8a8a0ffad6ebadad" + " " + "fixed inset-0 pointer-events-none z-0"
            }, void 0, false, {
                fileName: "[project]/src/components/MysticAtmosphere.tsx",
                lineNumber: 201,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "jsx-8a8a0ffad6ebadad" + " " + "fixed inset-0 pointer-events-none z-0",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        background: `
              radial-gradient(circle at 20% 20%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(106, 13, 173, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 60%, rgba(75, 0, 130, 0.05) 0%, transparent 50%)
            `,
                        animation: "mysticPulse 8s ease-in-out infinite"
                    },
                    className: "jsx-8a8a0ffad6ebadad" + " " + "absolute inset-0 opacity-30"
                }, void 0, false, {
                    fileName: "[project]/src/components/MysticAtmosphere.tsx",
                    lineNumber: 209,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/MysticAtmosphere.tsx",
                lineNumber: 208,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                children: isChanneling && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    className: "fixed inset-0 pointer-events-none z-0",
                    initial: {
                        opacity: 0
                    },
                    animate: {
                        opacity: 1
                    },
                    exit: {
                        opacity: 0
                    },
                    transition: {
                        duration: 2
                    },
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            background: `
                  conic-gradient(from 0deg at 50% 50%, 
                    rgba(138, 43, 226, 0.1) 0deg,
                    rgba(106, 13, 173, 0.05) 60deg,
                    rgba(75, 0, 130, 0.1) 120deg,
                    rgba(148, 0, 211, 0.05) 180deg,
                    rgba(186, 85, 211, 0.1) 240deg,
                    rgba(138, 43, 226, 0.05) 300deg,
                    rgba(138, 43, 226, 0.1) 360deg
                  )
                `,
                            animation: "mysticRotate 10s linear infinite"
                        },
                        className: "jsx-8a8a0ffad6ebadad" + " " + "absolute inset-0"
                    }, void 0, false, {
                        fileName: "[project]/src/components/MysticAtmosphere.tsx",
                        lineNumber: 232,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/MysticAtmosphere.tsx",
                    lineNumber: 225,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/MysticAtmosphere.tsx",
                lineNumber: 223,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "jsx-8a8a0ffad6ebadad" + " " + "fixed inset-0 pointer-events-none z-0 opacity-20",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        background: `
              linear-gradient(45deg, 
                rgba(25, 0, 50, 0.3) 0%,
                transparent 25%,
                rgba(50, 0, 100, 0.2) 50%,
                transparent 75%,
                rgba(25, 0, 50, 0.3) 100%
              )
            `,
                        backgroundSize: "200% 200%",
                        animation: "mysticFog 15s ease-in-out infinite"
                    },
                    className: "jsx-8a8a0ffad6ebadad" + " " + "absolute inset-0"
                }, void 0, false, {
                    fileName: "[project]/src/components/MysticAtmosphere.tsx",
                    lineNumber: 255,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/MysticAtmosphere.tsx",
                lineNumber: 254,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                id: "8a8a0ffad6ebadad",
                children: "@keyframes mysticPulse{0%,to{opacity:.3;transform:scale(1)rotate(0)}50%{opacity:.6;transform:scale(1.1)rotate(180deg)}}@keyframes mysticRotate{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes mysticFog{0%,to{background-position:0 0}25%{background-position:100% 0}50%{background-position:100% 100%}75%{background-position:0 100%}}"
            }, void 0, false, void 0, this)
        ]
    }, void 0, true);
}
_s(MysticAtmosphere, "cXqnHzuOCnWBsFiNbRuo8tcLfC8=");
_c = MysticAtmosphere;
var _c;
__turbopack_context__.k.register(_c, "MysticAtmosphere");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ResponsiveLayout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AdaptiveControls": (()=>AdaptiveControls),
    "ResponsiveLayout": (()=>ResponsiveLayout),
    "useResponsive": (()=>useResponsive)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
'use client';
;
;
function ResponsiveLayout({ children, className = "" }) {
    _s();
    const [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isTablet, setIsTablet] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [orientation, setOrientation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('portrait');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ResponsiveLayout.useEffect": ()=>{
            const checkDevice = {
                "ResponsiveLayout.useEffect.checkDevice": ()=>{
                    const width = window.innerWidth;
                    const height = window.innerHeight;
                    setIsMobile(width < 768);
                    setIsTablet(width >= 768 && width < 1024);
                    setOrientation(height > width ? 'portrait' : 'landscape');
                }
            }["ResponsiveLayout.useEffect.checkDevice"];
            checkDevice();
            window.addEventListener('resize', checkDevice);
            window.addEventListener('orientationchange', {
                "ResponsiveLayout.useEffect": ()=>{
                    setTimeout(checkDevice, 100); // Délai pour laisser le temps à l'orientation de changer
                }
            }["ResponsiveLayout.useEffect"]);
            return ({
                "ResponsiveLayout.useEffect": ()=>{
                    window.removeEventListener('resize', checkDevice);
                    window.removeEventListener('orientationchange', checkDevice);
                }
            })["ResponsiveLayout.useEffect"];
        }
    }["ResponsiveLayout.useEffect"], []);
    const getLayoutClasses = ()=>{
        let classes = "min-h-screen w-full relative overflow-hidden ";
        if (isMobile) {
            classes += orientation === 'portrait' ? "px-4 py-6 " : "px-6 py-4 ";
        } else if (isTablet) {
            classes += "px-8 py-8 ";
        } else {
            classes += "px-12 py-12 ";
        }
        return classes + className;
    };
    const containerVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                duration: 0.6,
                staggerChildren: 0.1
            }
        }
    };
    const itemVariants = {
        hidden: {
            y: 20,
            opacity: 0
        },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                duration: 0.6
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: getLayoutClasses(),
        variants: containerVariants,
        initial: "hidden",
        animate: "visible",
        children: [
            ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed top-2 right-2 z-50 text-xs bg-black/50 text-white px-2 py-1 rounded",
                children: [
                    isMobile ? 'Mobile' : isTablet ? 'Tablet' : 'Desktop',
                    " - ",
                    orientation
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ResponsiveLayout.tsx",
                lineNumber: 83,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                variants: itemVariants,
                children: children
            }, void 0, false, {
                fileName: "[project]/src/components/ResponsiveLayout.tsx",
                lineNumber: 88,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ResponsiveLayout.tsx",
        lineNumber: 75,
        columnNumber: 5
    }, this);
}
_s(ResponsiveLayout, "Wyy+iwVefhj+PLTx7G75RNz4RNE=");
_c = ResponsiveLayout;
function useResponsive() {
    _s1();
    const [deviceInfo, setDeviceInfo] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isMobile: false,
        isTablet: false,
        isDesktop: false,
        orientation: 'portrait',
        width: 0,
        height: 0
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useResponsive.useEffect": ()=>{
            const updateDeviceInfo = {
                "useResponsive.useEffect.updateDeviceInfo": ()=>{
                    const width = window.innerWidth;
                    const height = window.innerHeight;
                    setDeviceInfo({
                        isMobile: width < 768,
                        isTablet: width >= 768 && width < 1024,
                        isDesktop: width >= 1024,
                        orientation: height > width ? 'portrait' : 'landscape',
                        width,
                        height
                    });
                }
            }["useResponsive.useEffect.updateDeviceInfo"];
            updateDeviceInfo();
            window.addEventListener('resize', updateDeviceInfo);
            window.addEventListener('orientationchange', {
                "useResponsive.useEffect": ()=>{
                    setTimeout(updateDeviceInfo, 100);
                }
            }["useResponsive.useEffect"]);
            return ({
                "useResponsive.useEffect": ()=>{
                    window.removeEventListener('resize', updateDeviceInfo);
                    window.removeEventListener('orientationchange', updateDeviceInfo);
                }
            })["useResponsive.useEffect"];
        }
    }["useResponsive.useEffect"], []);
    return deviceInfo;
}
_s1(useResponsive, "wkUvFtHtcUeCTfIs9HvfIVzMh6s=");
function AdaptiveControls({ onModeChange, currentMode, modes }) {
    _s2();
    const { isMobile, isTablet } = useResponsive();
    if (isMobile) {
        // Version mobile : menu déroulant
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
            initial: {
                opacity: 0,
                y: -20
            },
            animate: {
                opacity: 1,
                y: 0
            },
            className: "fixed top-4 left-4 right-4 z-20",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                value: currentMode,
                onChange: (e)=>onModeChange(e.target.value),
                className: "w-full bg-ghost-100 border border-whisper-200 text-whisper-300 text-sm font-light p-2 rounded",
                children: modes.map((mode)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                        value: mode.key,
                        children: mode.label
                    }, mode.key, false, {
                        fileName: "[project]/src/components/ResponsiveLayout.tsx",
                        lineNumber: 160,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/ResponsiveLayout.tsx",
                lineNumber: 154,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ResponsiveLayout.tsx",
            lineNumber: 149,
            columnNumber: 7
        }, this);
    }
    if (isTablet) {
        // Version tablette : boutons horizontaux
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
            initial: {
                opacity: 0,
                y: -20
            },
            animate: {
                opacity: 1,
                y: 0
            },
            className: "fixed top-4 left-4 right-4 z-20",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex space-x-2 justify-center",
                children: modes.map((mode)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>onModeChange(mode.key),
                        className: `px-3 py-1 text-xs font-light transition-colors duration-300 rounded ${currentMode === mode.key ? 'text-whisper-300 bg-ghost-200 border border-whisper-300' : 'text-whisper-500 bg-ghost-100 border border-whisper-200 hover:text-whisper-400'}`,
                        children: mode.label
                    }, mode.key, false, {
                        fileName: "[project]/src/components/ResponsiveLayout.tsx",
                        lineNumber: 179,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/ResponsiveLayout.tsx",
                lineNumber: 177,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ResponsiveLayout.tsx",
            lineNumber: 172,
            columnNumber: 7
        }, this);
    }
    // Version desktop : menu vertical
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            x: -20
        },
        animate: {
            opacity: 1,
            x: 0
        },
        className: "fixed top-8 left-8 space-y-2 z-20",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-whisper-500 text-xs font-light mb-2",
                children: "mode d'affichage:"
            }, void 0, false, {
                fileName: "[project]/src/components/ResponsiveLayout.tsx",
                lineNumber: 203,
                columnNumber: 7
            }, this),
            modes.map((mode)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: ()=>onModeChange(mode.key),
                    className: `block w-full text-left px-3 py-1 text-xs font-light transition-colors duration-300 ${currentMode === mode.key ? 'text-whisper-300 bg-ghost-200 border border-whisper-300' : 'text-whisper-500 bg-ghost-100 border border-whisper-200 hover:text-whisper-400 hover:bg-ghost-150'}`,
                    children: mode.label
                }, mode.key, false, {
                    fileName: "[project]/src/components/ResponsiveLayout.tsx",
                    lineNumber: 207,
                    columnNumber: 9
                }, this))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ResponsiveLayout.tsx",
        lineNumber: 198,
        columnNumber: 5
    }, this);
}
_s2(AdaptiveControls, "ysnFTiS6mj7zmM3dqZaQEGAywoM=", false, function() {
    return [
        useResponsive
    ];
});
_c1 = AdaptiveControls;
var _c, _c1;
__turbopack_context__.k.register(_c, "ResponsiveLayout");
__turbopack_context__.k.register(_c1, "AdaptiveControls");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/EnhancedUI.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "EnhancedButton": (()=>EnhancedButton),
    "EnhancedCard": (()=>EnhancedCard),
    "FloatingElement": (()=>FloatingElement),
    "GlowText": (()=>GlowText),
    "ParticleField": (()=>ParticleField),
    "ProgressBar": (()=>ProgressBar),
    "StatusIndicator": (()=>StatusIndicator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ResponsiveLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ResponsiveLayout.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
function EnhancedButton({ children, onClick, variant = 'primary', size = 'md', className = '', disabled = false }) {
    _s();
    const { isMobile } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ResponsiveLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useResponsive"])();
    const baseClasses = "font-light transition-all duration-300 transform backdrop-blur-sm border";
    const variantClasses = {
        primary: "bg-ghost-100 border-whisper-200 text-whisper-300 hover:bg-ghost-200 hover:border-whisper-300",
        secondary: "bg-whisper-100 border-whisper-300 text-void-700 hover:bg-whisper-200",
        ghost: "bg-transparent border-whisper-200 text-whisper-400 hover:bg-ghost-100 hover:text-whisper-300"
    };
    const sizeClasses = {
        sm: isMobile ? "px-3 py-1 text-xs" : "px-4 py-2 text-sm",
        md: isMobile ? "px-4 py-2 text-sm" : "px-6 py-3 text-base",
        lg: isMobile ? "px-6 py-3 text-base" : "px-8 py-4 text-lg"
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
        onClick: onClick,
        disabled: disabled,
        className: `
        ${baseClasses} 
        ${variantClasses[variant]} 
        ${sizeClasses[size]} 
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:shadow-lg animate-glow-pulse'}
        ${className}
      `,
        whileHover: disabled ? {} : {
            scale: 1.05
        },
        whileTap: disabled ? {} : {
            scale: 0.95
        },
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.3
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/EnhancedUI.tsx",
        lineNumber: 40,
        columnNumber: 5
    }, this);
}
_s(EnhancedButton, "zh1QXCqfW6ylXeAP9/dx0AvnWg0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ResponsiveLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useResponsive"]
    ];
});
_c = EnhancedButton;
function EnhancedCard({ children, title, subtitle, onClick, className = '', glowing = false }) {
    _s1();
    const { isMobile } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ResponsiveLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useResponsive"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        onClick: onClick,
        className: `
        border border-whisper-200 backdrop-blur-sm transition-all duration-700
        ${onClick ? 'cursor-pointer hover:border-whisper-300 hover:bg-ghost-100' : ''}
        ${glowing ? 'animate-glow-pulse' : ''}
        ${isMobile ? 'p-6' : 'p-8'}
        ${className}
      `,
        whileHover: onClick ? {
            scale: 1.02,
            y: -5
        } : {},
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.6
        },
        children: [
            title && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "text-whisper-300 text-xl font-light mb-4 animate-whisper-glow",
                children: title
            }, void 0, false, {
                fileName: "[project]/src/components/EnhancedUI.tsx",
                lineNumber: 96,
                columnNumber: 9
            }, this),
            subtitle && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-whisper-500 text-sm opacity-80 mb-4",
                children: subtitle
            }, void 0, false, {
                fileName: "[project]/src/components/EnhancedUI.tsx",
                lineNumber: 101,
                columnNumber: 9
            }, this),
            children,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-4 w-full h-px bg-gradient-to-r from-whisper-200 to-transparent animate-shimmer"
            }, void 0, false, {
                fileName: "[project]/src/components/EnhancedUI.tsx",
                lineNumber: 106,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/EnhancedUI.tsx",
        lineNumber: 81,
        columnNumber: 5
    }, this);
}
_s1(EnhancedCard, "zh1QXCqfW6ylXeAP9/dx0AvnWg0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ResponsiveLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useResponsive"]
    ];
});
_c1 = EnhancedCard;
function FloatingElement({ children, delay = 0, duration = 6, className = '' }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `animate-float3d ${className}`,
        initial: {
            opacity: 0,
            y: 50
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            delay,
            duration: 1
        },
        style: {
            animationDelay: `${delay}s`,
            animationDuration: `${duration}s`
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/EnhancedUI.tsx",
        lineNumber: 125,
        columnNumber: 5
    }, this);
}
_c2 = FloatingElement;
function GlowText({ children, intensity = 'medium', color = 'var(--whisper-bright)', className = '' }) {
    const intensityMap = {
        low: '0 0 10px',
        medium: '0 0 20px',
        high: '0 0 30px, 0 0 40px'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        className: `animate-whisper-glow ${className}`,
        style: {
            textShadow: `${intensityMap[intensity]} ${color}`
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/EnhancedUI.tsx",
        lineNumber: 160,
        columnNumber: 5
    }, this);
}
_c3 = GlowText;
function ProgressBar({ progress, max, label, color = '#64ffda', className = '' }) {
    const percentage = progress / max * 100;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `w-full ${className}`,
        children: [
            label && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-between text-whisper-400 text-xs mb-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: label
                    }, void 0, false, {
                        fileName: "[project]/src/components/EnhancedUI.tsx",
                        lineNumber: 192,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: [
                            progress,
                            "/",
                            max
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/EnhancedUI.tsx",
                        lineNumber: 193,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/EnhancedUI.tsx",
                lineNumber: 191,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full bg-ghost-100 rounded-full h-2 overflow-hidden",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                    className: "h-full rounded-full",
                    style: {
                        backgroundColor: color
                    },
                    initial: {
                        width: 0
                    },
                    animate: {
                        width: `${percentage}%`
                    },
                    transition: {
                        duration: 0.8,
                        ease: "easeOut"
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/EnhancedUI.tsx",
                    lineNumber: 197,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/EnhancedUI.tsx",
                lineNumber: 196,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/EnhancedUI.tsx",
        lineNumber: 189,
        columnNumber: 5
    }, this);
}
_c4 = ProgressBar;
function ParticleField({ count = 20, className = '' }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `absolute inset-0 pointer-events-none ${className}`,
        children: Array.from({
            length: count
        }).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                className: "absolute w-1 h-1 bg-whisper-300 rounded-full opacity-30",
                style: {
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`
                },
                animate: {
                    y: [
                        0,
                        -20,
                        0
                    ],
                    opacity: [
                        0.3,
                        0.8,
                        0.3
                    ],
                    scale: [
                        0.5,
                        1,
                        0.5
                    ]
                },
                transition: {
                    duration: 3 + Math.random() * 2,
                    repeat: Infinity,
                    delay: Math.random() * 2,
                    ease: "easeInOut"
                }
            }, i, false, {
                fileName: "[project]/src/components/EnhancedUI.tsx",
                lineNumber: 218,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/src/components/EnhancedUI.tsx",
        lineNumber: 216,
        columnNumber: 5
    }, this);
}
_c5 = ParticleField;
function StatusIndicator({ status, message, className = '' }) {
    const statusConfig = {
        idle: {
            color: 'text-whisper-500',
            icon: '○'
        },
        loading: {
            color: 'text-blue-400',
            icon: '◐'
        },
        success: {
            color: 'text-green-400',
            icon: '●'
        },
        error: {
            color: 'text-red-400',
            icon: '✕'
        }
    };
    const config = statusConfig[status];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        className: `flex items-center space-x-2 ${className}`,
        initial: {
            opacity: 0
        },
        animate: {
            opacity: 1
        },
        transition: {
            duration: 0.3
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].span, {
                className: `${config.color} text-sm`,
                animate: status === 'loading' ? {
                    rotate: 360
                } : {},
                transition: status === 'loading' ? {
                    duration: 1,
                    repeat: Infinity,
                    ease: "linear"
                } : {},
                children: config.icon
            }, void 0, false, {
                fileName: "[project]/src/components/EnhancedUI.tsx",
                lineNumber: 265,
                columnNumber: 7
            }, this),
            message && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "text-whisper-400 text-xs font-light",
                children: message
            }, void 0, false, {
                fileName: "[project]/src/components/EnhancedUI.tsx",
                lineNumber: 273,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/EnhancedUI.tsx",
        lineNumber: 259,
        columnNumber: 5
    }, this);
}
_c6 = StatusIndicator;
var _c, _c1, _c2, _c3, _c4, _c5, _c6;
__turbopack_context__.k.register(_c, "EnhancedButton");
__turbopack_context__.k.register(_c1, "EnhancedCard");
__turbopack_context__.k.register(_c2, "FloatingElement");
__turbopack_context__.k.register(_c3, "GlowText");
__turbopack_context__.k.register(_c4, "ProgressBar");
__turbopack_context__.k.register(_c5, "ParticleField");
__turbopack_context__.k.register(_c6, "StatusIndicator");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/EasterEggHunter.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "EasterEggHunter": (()=>EasterEggHunter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
function EasterEggHunter({ onSecretFound }) {
    _s();
    const [konamiSequence, setKonamiSequence] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [clickCount, setClickCount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [lastClickTime, setLastClickTime] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const konamiCode = [
        'ArrowUp',
        'ArrowUp',
        'ArrowDown',
        'ArrowDown',
        'ArrowLeft',
        'ArrowRight',
        'ArrowLeft',
        'ArrowRight',
        'KeyB',
        'KeyA'
    ];
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "EasterEggHunter.useEffect": ()=>{
            const handleKeyDown = {
                "EasterEggHunter.useEffect.handleKeyDown": (e)=>{
                    const newSequence = [
                        ...konamiSequence,
                        e.code
                    ];
                    // Keep only the last 10 keys
                    if (newSequence.length > 10) {
                        newSequence.shift();
                    }
                    setKonamiSequence(newSequence);
                    // Check if konami code is complete
                    if (newSequence.length === 10 && newSequence.every({
                        "EasterEggHunter.useEffect.handleKeyDown": (key, index)=>key === konamiCode[index]
                    }["EasterEggHunter.useEffect.handleKeyDown"])) {
                        onSecretFound('konami_code');
                        setKonamiSequence([]);
                    }
                }
            }["EasterEggHunter.useEffect.handleKeyDown"];
            const handleClick = {
                "EasterEggHunter.useEffect.handleClick": (e)=>{
                    const now = Date.now();
                    // Reset if too much time has passed
                    if (now - lastClickTime > 1000) {
                        setClickCount(1);
                    } else {
                        setClickCount({
                            "EasterEggHunter.useEffect.handleClick": (prev)=>prev + 1
                        }["EasterEggHunter.useEffect.handleClick"]);
                    }
                    setLastClickTime(now);
                    // Secret: 7 rapid clicks
                    if (clickCount >= 7) {
                        onSecretFound('rapid_clicks');
                        setClickCount(0);
                    }
                    // Secret: clicking in corners
                    const { clientX, clientY } = e;
                    const { innerWidth, innerHeight } = window;
                    if (clientX < 50 && clientY < 50 || clientX > innerWidth - 50 && clientY < 50 || clientX < 50 && clientY > innerHeight - 50 || clientX > innerWidth - 50 && clientY > innerHeight - 50) {
                        onSecretFound('corner_click');
                    }
                }
            }["EasterEggHunter.useEffect.handleClick"];
            const handleMouseMove = {
                "EasterEggHunter.useEffect.handleMouseMove": (e)=>{
                    // Secret: mouse idle in center for 5 seconds
                    const { clientX, clientY } = e;
                    const { innerWidth, innerHeight } = window;
                    if (Math.abs(clientX - innerWidth / 2) < 20 && Math.abs(clientY - innerHeight / 2) < 20) {
                        setTimeout({
                            "EasterEggHunter.useEffect.handleMouseMove": ()=>{
                                onSecretFound('center_idle');
                            }
                        }["EasterEggHunter.useEffect.handleMouseMove"], 5000);
                    }
                }
            }["EasterEggHunter.useEffect.handleMouseMove"];
            document.addEventListener('keydown', handleKeyDown);
            document.addEventListener('click', handleClick);
            document.addEventListener('mousemove', handleMouseMove);
            return ({
                "EasterEggHunter.useEffect": ()=>{
                    document.removeEventListener('keydown', handleKeyDown);
                    document.removeEventListener('click', handleClick);
                    document.removeEventListener('mousemove', handleMouseMove);
                }
            })["EasterEggHunter.useEffect"];
        }
    }["EasterEggHunter.useEffect"], [
        konamiSequence,
        clickCount,
        lastClickTime,
        onSecretFound
    ]);
    return null; // This component is invisible
}
_s(EasterEggHunter, "mlkOHQ8o994TlLVNPXSaEiLmgq4=");
_c = EasterEggHunter;
var _c;
__turbopack_context__.k.register(_c, "EasterEggHunter");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/AmbientSoundscape.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AmbientSoundscape": (()=>AmbientSoundscape)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
function AmbientSoundscape() {
    _s();
    const [isPlaying, setIsPlaying] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [volume, setVolume] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0.3);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AmbientSoundscape.useEffect": ()=>{
            // Create ambient sound context
            let audioContext = null;
            let oscillator = null;
            let gainNode = null;
            const startAmbientSound = {
                "AmbientSoundscape.useEffect.startAmbientSound": ()=>{
                    try {
                        audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        oscillator = audioContext.createOscillator();
                        gainNode = audioContext.createGain();
                        // Create a very low frequency ambient tone
                        oscillator.frequency.setValueAtTime(40, audioContext.currentTime);
                        oscillator.type = 'sine';
                        // Very quiet volume
                        gainNode.gain.setValueAtTime(volume * 0.1, audioContext.currentTime);
                        oscillator.connect(gainNode);
                        gainNode.connect(audioContext.destination);
                        oscillator.start();
                        setIsPlaying(true);
                    } catch (error) {
                        console.log('Audio context not available');
                    }
                }
            }["AmbientSoundscape.useEffect.startAmbientSound"];
            const stopAmbientSound = {
                "AmbientSoundscape.useEffect.stopAmbientSound": ()=>{
                    if (oscillator) {
                        oscillator.stop();
                        oscillator = null;
                    }
                    if (audioContext) {
                        audioContext.close();
                        audioContext = null;
                    }
                    setIsPlaying(false);
                }
            }["AmbientSoundscape.useEffect.stopAmbientSound"];
            // Start ambient sound on first user interaction
            const handleFirstInteraction = {
                "AmbientSoundscape.useEffect.handleFirstInteraction": ()=>{
                    startAmbientSound();
                    document.removeEventListener('click', handleFirstInteraction);
                    document.removeEventListener('keydown', handleFirstInteraction);
                }
            }["AmbientSoundscape.useEffect.handleFirstInteraction"];
            document.addEventListener('click', handleFirstInteraction);
            document.addEventListener('keydown', handleFirstInteraction);
            return ({
                "AmbientSoundscape.useEffect": ()=>{
                    stopAmbientSound();
                    document.removeEventListener('click', handleFirstInteraction);
                    document.removeEventListener('keydown', handleFirstInteraction);
                }
            })["AmbientSoundscape.useEffect"];
        }
    }["AmbientSoundscape.useEffect"], [
        volume
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed top-4 left-4 z-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "text-whisper-600 text-xs font-mono",
            children: isPlaying ? '♪ ambient' : '♪ silent'
        }, void 0, false, {
            fileName: "[project]/src/components/AmbientSoundscape.tsx",
            lineNumber: 69,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/AmbientSoundscape.tsx",
        lineNumber: 68,
        columnNumber: 5
    }, this);
}
_s(AmbientSoundscape, "s8fCAKb7VUzMtLXC2JlABpuizgw=");
_c = AmbientSoundscape;
var _c;
__turbopack_context__.k.register(_c, "AmbientSoundscape");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/HiddenNavigation.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "HiddenNavigation": (()=>HiddenNavigation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function HiddenNavigation({ secretsFound }) {
    _s();
    const [isVisible, setIsVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [mousePosition, setMousePosition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        x: 0,
        y: 0
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HiddenNavigation.useEffect": ()=>{
            const handleMouseMove = {
                "HiddenNavigation.useEffect.handleMouseMove": (e)=>{
                    setMousePosition({
                        x: e.clientX,
                        y: e.clientY
                    });
                    // Show navigation when mouse is in top-left corner
                    if (e.clientX < 100 && e.clientY < 100) {
                        setIsVisible(true);
                    } else if (e.clientX > 200 || e.clientY > 200) {
                        setIsVisible(false);
                    }
                }
            }["HiddenNavigation.useEffect.handleMouseMove"];
            document.addEventListener('mousemove', handleMouseMove);
            return ({
                "HiddenNavigation.useEffect": ()=>document.removeEventListener('mousemove', handleMouseMove)
            })["HiddenNavigation.useEffect"];
        }
    }["HiddenNavigation.useEffect"], []);
    const navigationItems = [
        {
            id: 'echoes',
            label: '◦ echoes',
            unlocked: secretsFound.includes('logo_sequence')
        },
        {
            id: 'fragments',
            label: '◦ fragments',
            unlocked: secretsFound.includes('konami_code')
        },
        {
            id: 'void',
            label: '◦ the void',
            unlocked: secretsFound.includes('rapid_clicks')
        },
        {
            id: 'contact',
            label: '◦ contact',
            unlocked: secretsFound.length >= 3
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatePresence"], {
        children: isVisible && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].nav, {
            initial: {
                opacity: 0,
                x: -50
            },
            animate: {
                opacity: 1,
                x: 0
            },
            exit: {
                opacity: 0,
                x: -50
            },
            transition: {
                duration: 0.3
            },
            className: "fixed top-8 left-8 z-40",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-void-600 border border-whisper-100 p-6 backdrop-blur-sm",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-whisper-300 text-sm font-mono mb-4",
                        children: "navigate"
                    }, void 0, false, {
                        fileName: "[project]/src/components/HiddenNavigation.tsx",
                        lineNumber: 48,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                        className: "space-y-2",
                        children: navigationItems.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    className: `text-xs font-mono transition-colors duration-300 ${item.unlocked ? 'text-whisper-400 hover:text-whisper-200 cursor-pointer' : 'text-whisper-700 cursor-not-allowed'}`,
                                    disabled: !item.unlocked,
                                    children: item.label
                                }, void 0, false, {
                                    fileName: "[project]/src/components/HiddenNavigation.tsx",
                                    lineNumber: 52,
                                    columnNumber: 19
                                }, this)
                            }, item.id, false, {
                                fileName: "[project]/src/components/HiddenNavigation.tsx",
                                lineNumber: 51,
                                columnNumber: 17
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/HiddenNavigation.tsx",
                        lineNumber: 49,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-6 pt-4 border-t border-whisper-100",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-whisper-600 text-xs font-mono",
                            children: [
                                "secrets: ",
                                secretsFound.length,
                                "/7"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/HiddenNavigation.tsx",
                            lineNumber: 67,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/HiddenNavigation.tsx",
                        lineNumber: 66,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/HiddenNavigation.tsx",
                lineNumber: 47,
                columnNumber: 11
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/HiddenNavigation.tsx",
            lineNumber: 40,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/HiddenNavigation.tsx",
        lineNumber: 38,
        columnNumber: 5
    }, this);
}
_s(HiddenNavigation, "WCfKfK6oMAoRSKrUcKig1hrP1Jw=");
_c = HiddenNavigation;
var _c;
__turbopack_context__.k.register(_c, "HiddenNavigation");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Home)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$DreamyLogo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/DreamyLogo.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AnimatedSVGLogo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/AnimatedSVGLogo.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Logo3D$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Logo3D.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Scene3D$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/Scene3D.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$SVG3DLogo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/SVG3DLogo.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$InteractiveGame$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/InteractiveGame.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$MysticInteractiveGame$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/MysticInteractiveGame.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$MysticAtmosphere$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/MysticAtmosphere.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ResponsiveLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ResponsiveLayout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$EnhancedUI$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/EnhancedUI.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$EasterEggHunter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/EasterEggHunter.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AmbientSoundscape$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/AmbientSoundscape.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$HiddenNavigation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/HiddenNavigation.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function Home() {
    _s();
    const [currentPhase, setCurrentPhase] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("entry");
    const [secretsFound, setSecretsFound] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [showHiddenText, setShowHiddenText] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [logoMode, setLogoMode] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("svg3d");
    const [activeLetters, setActiveLetters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        false,
        false,
        false,
        false,
        false
    ]);
    const [gameScore, setGameScore] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const { isMobile, isTablet } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ResponsiveLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useResponsive"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Home.useEffect": ()=>{
            // Auto-transition from entry to void after 3 seconds
            const timer = setTimeout({
                "Home.useEffect.timer": ()=>{
                    if (currentPhase === "entry") {
                        setCurrentPhase("void");
                    }
                }
            }["Home.useEffect.timer"], 3000);
            return ({
                "Home.useEffect": ()=>clearTimeout(timer)
            })["Home.useEffect"];
        }
    }["Home.useEffect"], [
        currentPhase
    ]);
    const handleLogoInteraction = (letter, index)=>{
        if (letter === "secret") {
            setSecretsFound((prev)=>[
                    ...prev,
                    "logo_sequence"
                ]);
            setCurrentPhase("exploration");
            // Animation spéciale pour toutes les lettres
            setActiveLetters([
                true,
                true,
                true,
                true,
                true
            ]);
            setTimeout(()=>setActiveLetters([
                    false,
                    false,
                    false,
                    false,
                    false
                ]), 5000);
        } else {
            // Activer la lettre cliquée
            const newActiveLetters = [
                ...activeLetters
            ];
            newActiveLetters[index] = true;
            setActiveLetters(newActiveLetters);
            setTimeout(()=>{
                const resetLetters = [
                    ...activeLetters
                ];
                resetLetters[index] = false;
                setActiveLetters(resetLetters);
            }, 3000);
        }
    };
    const handleSecretFound = (secretId)=>{
        setSecretsFound((prev)=>[
                ...prev,
                secretId
            ]);
    };
    const handleGameComplete = (score)=>{
        setGameScore(score);
        setSecretsFound((prev)=>[
                ...prev,
                "game_master"
            ]);
        setCurrentPhase("exploration");
    };
    const handleGameLetterActivate = (index)=>{
        const newActiveLetters = [
            ...activeLetters
        ];
        newActiveLetters[index] = true;
        setActiveLetters(newActiveLetters);
        setTimeout(()=>{
            const resetLetters = [
                ...activeLetters
            ];
            resetLetters[index] = false;
            setActiveLetters(resetLetters);
        }, 500);
    };
    const logoModes = [
        {
            key: "svg3d",
            label: isMobile ? "SVG 3D" : "SVG 3D Extrudé"
        },
        {
            key: "svg",
            label: isMobile ? "SVG" : "SVG Animé"
        },
        {
            key: "2d",
            label: isMobile ? "2D" : "2D Dreamy"
        },
        {
            key: "3d",
            label: isMobile ? "3D" : "3D Texte"
        },
        {
            key: "scene",
            label: isMobile ? "Scène" : "Scène 3D"
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ResponsiveLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ResponsiveLayout"], {
        className: "bg-void-700",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$MysticAtmosphere$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MysticAtmosphere"], {
                intensity: currentPhase === "mystic" ? 3 : 1,
                showRitualCircle: currentPhase === "mystic",
                isChanneling: false
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 114,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AmbientSoundscape$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AmbientSoundscape"], {}, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 119,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$EasterEggHunter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EasterEggHunter"], {
                onSecretFound: handleSecretFound
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 120,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                mode: "wait",
                children: [
                    currentPhase === "entry" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        initial: {
                            opacity: 0
                        },
                        animate: {
                            opacity: 1
                        },
                        exit: {
                            opacity: 0
                        },
                        transition: {
                            duration: 2
                        },
                        className: "fixed inset-0 flex items-center justify-center z-10",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                            initial: {
                                scale: 0.8,
                                opacity: 0
                            },
                            animate: {
                                scale: 1,
                                opacity: 1
                            },
                            transition: {
                                delay: 0.5,
                                duration: 1.5
                            },
                            className: "text-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                    className: "text-whisper-400 text-sm font-mono mb-8 animate-whisper-glow",
                                    animate: {
                                        opacity: [
                                            0.5,
                                            1,
                                            0.5
                                        ]
                                    },
                                    transition: {
                                        duration: 3,
                                        repeat: Infinity
                                    },
                                    children: "entering the void..."
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 137,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    className: "text-whisper-300 text-6xl font-light mb-4 animate-dreamy-float",
                                    style: {
                                        textShadow: "0 0 30px var(--whisper-bright)"
                                    },
                                    children: "how r u"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 144,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-32 h-px bg-gradient-to-r from-transparent via-whisper-300 to-transparent mx-auto animate-gentle-sway"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 152,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                    className: "text-whisper-500 text-xs font-light mt-4",
                                    animate: {
                                        opacity: [
                                            0.3,
                                            0.8,
                                            0.3
                                        ]
                                    },
                                    transition: {
                                        duration: 4,
                                        repeat: Infinity
                                    },
                                    children: "consciousness loading..."
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 153,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 131,
                            columnNumber: 13
                        }, this)
                    }, "entry", false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 123,
                        columnNumber: 11
                    }, this),
                    currentPhase === "void" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        initial: {
                            opacity: 0
                        },
                        animate: {
                            opacity: 1
                        },
                        exit: {
                            opacity: 0
                        },
                        transition: {
                            duration: 2
                        },
                        className: "fixed inset-0 flex flex-col items-center justify-center z-10",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                initial: {
                                    y: 50,
                                    opacity: 0
                                },
                                animate: {
                                    y: 0,
                                    opacity: 1
                                },
                                transition: {
                                    delay: 1,
                                    duration: 2
                                },
                                className: "text-center mb-16",
                                children: [
                                    logoMode === "svg3d" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$SVG3DLogo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SVG3DLogo"], {
                                        onLetterClick: handleLogoInteraction,
                                        activeLetters: activeLetters,
                                        className: isMobile ? "h-[400px]" : "h-[600px]"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 180,
                                        columnNumber: 17
                                    }, this),
                                    logoMode === "2d" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$DreamyLogo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DreamyLogo"], {
                                        onLetterClick: handleLogoInteraction
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 187,
                                        columnNumber: 17
                                    }, this),
                                    logoMode === "svg" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AnimatedSVGLogo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatedSVGLogo"], {
                                        onLetterClick: handleLogoInteraction
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 190,
                                        columnNumber: 17
                                    }, this),
                                    logoMode === "3d" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Logo3D$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Logo3D"], {
                                        onLetterClick: handleLogoInteraction
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 193,
                                        columnNumber: 17
                                    }, this),
                                    logoMode === "scene" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Scene3D$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Scene3D"], {
                                        activeLetters: activeLetters
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 196,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 173,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                initial: {
                                    opacity: 0
                                },
                                animate: {
                                    opacity: 1
                                },
                                transition: {
                                    delay: 3,
                                    duration: 2
                                },
                                className: "text-center max-w-md",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-whisper-400 text-sm font-light mb-8 animate-whisper-glow",
                                        children: "click the letters... listen... feel..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 206,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                        className: "text-whisper-600 text-xs font-light",
                                        animate: {
                                            opacity: [
                                                0.3,
                                                0.7,
                                                0.3
                                            ]
                                        },
                                        transition: {
                                            duration: 3,
                                            repeat: Infinity
                                        },
                                        children: "there are secrets hidden in the darkness"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 210,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 200,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ResponsiveLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AdaptiveControls"], {
                                onModeChange: (mode)=>setLogoMode(mode),
                                currentMode: logoMode,
                                modes: logoModes
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 220,
                                columnNumber: 13
                            }, this)
                        ]
                    }, "void", true, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 165,
                        columnNumber: 11
                    }, this),
                    currentPhase === "exploration" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        initial: {
                            opacity: 0
                        },
                        animate: {
                            opacity: 1
                        },
                        transition: {
                            duration: 2
                        },
                        className: "min-h-screen relative",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$HiddenNavigation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HiddenNavigation"], {
                                secretsFound: secretsFound
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 236,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "container mx-auto px-8 py-16",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                        initial: {
                                            y: 20,
                                            opacity: 0
                                        },
                                        animate: {
                                            y: 0,
                                            opacity: 1
                                        },
                                        transition: {
                                            delay: 0.5
                                        },
                                        className: "text-center mb-16",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                                className: "text-whisper-500 text-sm font-light mb-4 animate-whisper-glow",
                                                animate: {
                                                    opacity: [
                                                        0.5,
                                                        1,
                                                        0.5
                                                    ]
                                                },
                                                transition: {
                                                    duration: 4,
                                                    repeat: Infinity
                                                },
                                                children: "consciousness fragments detected..."
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 246,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                className: "text-whisper-300 text-4xl font-light mb-8 animate-dreamy-float",
                                                style: {
                                                    textShadow: "0 0 25px var(--whisper-bright)"
                                                },
                                                children: "you found the way"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 253,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-64 h-px bg-gradient-to-r from-transparent via-whisper-300 to-transparent mx-auto mb-8 animate-gentle-sway"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 261,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-whisper-400 text-lg font-light max-w-2xl mx-auto leading-relaxed",
                                                children: "welcome to the digital séance. here, consciousness fragments drift through the void, waiting to be discovered. each interaction reveals another layer of the mystery."
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 262,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 240,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                                initial: {
                                                    opacity: 0,
                                                    y: 20
                                                },
                                                animate: {
                                                    opacity: 1,
                                                    y: 0
                                                },
                                                transition: {
                                                    delay: 1
                                                },
                                                className: "group cursor-pointer",
                                                onClick: ()=>setShowHiddenText(!showHiddenText),
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "border border-whisper-200 p-8 hover:border-whisper-300 hover:bg-ghost-100 transition-all duration-700 bg-ghost-50 backdrop-blur-sm animate-dreamy-float",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "text-whisper-300 text-xl font-light mb-4 animate-whisper-glow",
                                                            children: "echoes"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 279,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-whisper-500 text-sm opacity-80",
                                                            children: "fragments of sound and memory"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 282,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "mt-4 w-full h-px bg-gradient-to-r from-whisper-200 to-transparent animate-gentle-sway"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 285,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-whisper-600 text-xs mt-2 font-light",
                                                            children: "drift through silence..."
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 286,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 278,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 271,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                                initial: {
                                                    opacity: 0,
                                                    y: 20
                                                },
                                                animate: {
                                                    opacity: 1,
                                                    y: 0
                                                },
                                                transition: {
                                                    delay: 1.2
                                                },
                                                className: "group cursor-pointer",
                                                onClick: ()=>setCurrentPhase("game"),
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "border border-whisper-200 p-8 hover:border-whisper-300 hover:bg-ghost-100 transition-all duration-700 bg-ghost-50 backdrop-blur-sm animate-gentle-sway",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "text-whisper-300 text-xl font-light mb-4 animate-whisper-glow",
                                                            children: "jeu de mémoire"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 300,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-whisper-500 text-sm opacity-80",
                                                            children: "teste ta conscience avec les lettres"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 303,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "mt-4 w-full h-px bg-gradient-to-r from-whisper-200 to-transparent animate-dreamy-float"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 306,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-whisper-600 text-xs mt-2 font-light",
                                                            children: gameScore > 0 ? `meilleur score: ${gameScore}` : "clique pour jouer..."
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 307,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 299,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 292,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                                initial: {
                                                    opacity: 0,
                                                    y: 20
                                                },
                                                animate: {
                                                    opacity: 1,
                                                    y: 0
                                                },
                                                transition: {
                                                    delay: 1.4
                                                },
                                                className: "group cursor-pointer",
                                                onClick: ()=>setCurrentPhase("mystic"),
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "border border-purple-400 p-8 hover:border-purple-300 hover:bg-purple-900 hover:bg-opacity-20 transition-all duration-700 bg-purple-950 bg-opacity-10 backdrop-blur-sm animate-gentle-sway",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "text-purple-300 text-xl font-light mb-4 animate-whisper-glow",
                                                            children: "rituel mystique"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 323,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-purple-400 text-sm opacity-80",
                                                            children: "éveillez les pouvoirs cachés des lettres"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 326,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "mt-4 w-full h-px bg-gradient-to-r from-purple-400 to-transparent animate-dreamy-float"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 329,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-purple-500 text-xs mt-2 font-light",
                                                            children: "entrez dans l'au-delà..."
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 330,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 322,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 315,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 270,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatePresence"], {
                                        children: showHiddenText && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                            initial: {
                                                opacity: 0,
                                                y: 20
                                            },
                                            animate: {
                                                opacity: 1,
                                                y: 0
                                            },
                                            exit: {
                                                opacity: 0,
                                                y: -20
                                            },
                                            className: "mt-16 text-center",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-ghost-100 border border-whisper-300 p-6 max-w-2xl mx-auto backdrop-blur-md animate-whisper-glow",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-whisper-400 text-xs font-light mb-2 animate-dreamy-float",
                                                        children: "revealing hidden memory..."
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 347,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-whisper-300 text-sm font-light typewriter",
                                                        children: '"in the space between silence and sound, we exist..."'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 350,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-whisper-500 text-xs font-light mt-4 opacity-60",
                                                        children: "fragment recovered from the void"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 353,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 346,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 340,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 338,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                                        initial: {
                                            opacity: 0
                                        },
                                        animate: {
                                            opacity: 1
                                        },
                                        transition: {
                                            delay: 2
                                        },
                                        className: "fixed bottom-8 right-8 bg-ghost-100 border border-whisper-200 p-3 text-whisper-400 text-xs font-light backdrop-blur-sm animate-whisper-glow",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-whisper-300 mb-1",
                                                children: "secrets found"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 368,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    secretsFound.length,
                                                    "/7"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 369,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-whisper-500 text-xs mt-1",
                                                children: secretsFound.length >= 3 ? "consciousness awakening..." : "searching the void..."
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 370,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 362,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 239,
                                columnNumber: 13
                            }, this)
                        ]
                    }, "exploration", true, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 229,
                        columnNumber: 11
                    }, this),
                    currentPhase === "game" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        initial: {
                            opacity: 0
                        },
                        animate: {
                            opacity: 1
                        },
                        exit: {
                            opacity: 0
                        },
                        transition: {
                            duration: 1
                        },
                        className: "min-h-screen relative flex items-center justify-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "fixed top-4 left-4 z-30",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$EnhancedUI$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EnhancedButton"], {
                                    onClick: ()=>setCurrentPhase("exploration"),
                                    variant: "ghost",
                                    size: "sm",
                                    children: "← Retour"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 390,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 389,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$InteractiveGame$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InteractiveGame"], {
                                onGameComplete: handleGameComplete,
                                onLetterActivate: handleGameLetterActivate
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 399,
                                columnNumber: 13
                            }, this)
                        ]
                    }, "game", true, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 381,
                        columnNumber: 11
                    }, this),
                    currentPhase === "mystic" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                        initial: {
                            opacity: 0
                        },
                        animate: {
                            opacity: 1
                        },
                        exit: {
                            opacity: 0
                        },
                        transition: {
                            duration: 2
                        },
                        className: "min-h-screen relative",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "fixed top-4 left-4 z-30",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$EnhancedUI$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EnhancedButton"], {
                                    onClick: ()=>setCurrentPhase("exploration"),
                                    variant: "ghost",
                                    size: "sm",
                                    className: "text-purple-300 border-purple-400 hover:bg-purple-900 hover:bg-opacity-20",
                                    children: "← Retour au monde"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 416,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 415,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$MysticInteractiveGame$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MysticInteractiveGame"], {
                                onGameComplete: (souls)=>{
                                    console.log(`Rituel terminé avec ${souls} âmes collectées`);
                                    setCurrentPhase("exploration");
                                },
                                onSecretUnlocked: (secret)=>{
                                    console.log(`Secret mystique révélé: ${secret}`);
                                }
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 426,
                                columnNumber: 13
                            }, this)
                        ]
                    }, "mystic", true, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 407,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 121,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 113,
        columnNumber: 5
    }, this);
}
_s(Home, "4CuxEyewvLuktuP9cRqdCnON1d4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ResponsiveLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useResponsive"]
    ];
});
_c = Home;
var _c;
__turbopack_context__.k.register(_c, "Home");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_81081765._.js.map