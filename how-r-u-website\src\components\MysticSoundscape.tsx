"use client";

import { useEffect, useRef, useState } from "react";

interface MysticSoundscapeProps {
  isActive?: boolean;
  intensity?: number;
  phase?: "awakening" | "exploration" | "ritual" | "transcendence" | "void";
}

export function MysticSoundscape({ 
  isActive = true, 
  intensity = 1,
  phase = "awakening" 
}: MysticSoundscapeProps) {
  const audioContextRef = useRef<AudioContext | null>(null);
  const oscillatorsRef = useRef<OscillatorNode[]>([]);
  const gainNodesRef = useRef<GainNode[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (!isActive) return;

    const initAudio = () => {
      try {
        // Créer le contexte audio
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
        
        // Créer les oscillateurs pour différentes fréquences mystiques
        const frequencies = [
          55,    // Fréquence de base profonde
          110,   // Harmonique
          220,   // Harmonique supérieure
          440,   // Note de référence
          880,   // Octave supérieure
        ];

        frequencies.forEach((freq, index) => {
          const oscillator = audioContextRef.current!.createOscillator();
          const gainNode = audioContextRef.current!.createGain();
          
          // Configuration de l'oscillateur
          oscillator.type = index % 2 === 0 ? 'sine' : 'triangle';
          oscillator.frequency.setValueAtTime(freq, audioContextRef.current!.currentTime);
          
          // Configuration du gain (volume très bas pour l'ambiance)
          gainNode.gain.setValueAtTime(0.01 * intensity, audioContextRef.current!.currentTime);
          
          // Connexion audio
          oscillator.connect(gainNode);
          gainNode.connect(audioContextRef.current!.destination);
          
          // Démarrer l'oscillateur
          oscillator.start();
          
          // Stocker les références
          oscillatorsRef.current.push(oscillator);
          gainNodesRef.current.push(gainNode);
        });

        setIsInitialized(true);
      } catch (error) {
        console.log("Audio context not available:", error);
      }
    };

    // Initialiser l'audio au premier clic de l'utilisateur
    const handleUserInteraction = () => {
      if (!isInitialized) {
        initAudio();
        document.removeEventListener('click', handleUserInteraction);
        document.removeEventListener('keydown', handleUserInteraction);
      }
    };

    document.addEventListener('click', handleUserInteraction);
    document.addEventListener('keydown', handleUserInteraction);

    return () => {
      document.removeEventListener('click', handleUserInteraction);
      document.removeEventListener('keydown', handleUserInteraction);
    };
  }, [isActive, intensity, isInitialized]);

  useEffect(() => {
    if (!isInitialized || !audioContextRef.current) return;

    // Ajuster les fréquences et volumes selon la phase
    const phaseSettings = {
      awakening: { baseFreq: 55, volume: 0.005 },
      exploration: { baseFreq: 110, volume: 0.008 },
      ritual: { baseFreq: 220, volume: 0.012 },
      transcendence: { baseFreq: 440, volume: 0.015 },
      void: { baseFreq: 880, volume: 0.020 },
    };

    const settings = phaseSettings[phase];
    const currentTime = audioContextRef.current.currentTime;

    oscillatorsRef.current.forEach((oscillator, index) => {
      if (oscillator && gainNodesRef.current[index]) {
        // Modulation de fréquence pour créer un effet mystique
        const modulation = Math.sin(currentTime * 0.1 + index) * 0.1;
        const newFreq = settings.baseFreq * (index + 1) * (1 + modulation);
        
        try {
          oscillator.frequency.setTargetAtTime(
            newFreq, 
            currentTime, 
            2 // Transition douce de 2 secondes
          );
          
          gainNodesRef.current[index].gain.setTargetAtTime(
            settings.volume * intensity * (1 + modulation * 0.5),
            currentTime,
            1
          );
        } catch (error) {
          // Oscillateur peut-être déjà arrêté
        }
      }
    });
  }, [phase, intensity, isInitialized]);

  useEffect(() => {
    return () => {
      // Nettoyage lors du démontage
      oscillatorsRef.current.forEach(oscillator => {
        try {
          oscillator.stop();
          oscillator.disconnect();
        } catch (error) {
          // Oscillateur déjà arrêté
        }
      });
      
      gainNodesRef.current.forEach(gainNode => {
        try {
          gainNode.disconnect();
        } catch (error) {
          // Gain node déjà déconnecté
        }
      });

      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, []);

  // Fonction pour créer des effets sonores ponctuels
  const playMysticChime = (frequency: number = 440, duration: number = 1000) => {
    if (!audioContextRef.current) return;

    const oscillator = audioContextRef.current.createOscillator();
    const gainNode = audioContextRef.current.createGain();
    
    oscillator.type = 'sine';
    oscillator.frequency.setValueAtTime(frequency, audioContextRef.current.currentTime);
    
    // Enveloppe ADSR pour un son de cloche mystique
    const currentTime = audioContextRef.current.currentTime;
    gainNode.gain.setValueAtTime(0, currentTime);
    gainNode.gain.linearRampToValueAtTime(0.1 * intensity, currentTime + 0.1);
    gainNode.gain.exponentialRampToValueAtTime(0.001, currentTime + duration / 1000);
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContextRef.current.destination);
    
    oscillator.start(currentTime);
    oscillator.stop(currentTime + duration / 1000);
  };

  // Exposer la fonction pour les autres composants
  useEffect(() => {
    (window as any).playMysticChime = playMysticChime;
    return () => {
      delete (window as any).playMysticChime;
    };
  }, [intensity]);

  return null; // Ce composant ne rend rien visuellement
}

// Hook personnalisé pour utiliser les sons mystiques
export function useMysticSounds() {
  const playChime = (frequency?: number, duration?: number) => {
    if ((window as any).playMysticChime) {
      (window as any).playMysticChime(frequency, duration);
    }
  };

  const playLetterSound = (letterIndex: number) => {
    const frequencies = [220, 277, 330, 392, 440]; // Gamme pentatonique mystique
    playChime(frequencies[letterIndex % frequencies.length], 800);
  };

  const playSecretSound = () => {
    // Son spécial pour les secrets révélés
    playChime(880, 1500);
    setTimeout(() => playChime(660, 1000), 200);
    setTimeout(() => playChime(440, 1200), 400);
  };

  const playRitualSound = () => {
    // Séquence sonore pour les rituels
    const sequence = [110, 165, 220, 330, 440];
    sequence.forEach((freq, index) => {
      setTimeout(() => playChime(freq, 600), index * 300);
    });
  };

  return {
    playChime,
    playLetterSound,
    playSecretSound,
    playRitualSound,
  };
}
