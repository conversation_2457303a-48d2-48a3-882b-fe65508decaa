"use client";

import { useRef, useState, useEffect } from "react";
import { Canvas, useFrame, extend } from "@react-three/fiber";
import {
  Center,
  Float,
  MeshDistortMaterial,
  Environment,
  OrbitControls,
  PerspectiveCamera,
  Stars,
} from "@react-three/drei";
import { SVGLoader } from "three-stdlib";
import * as THREE from "three";
import { motion, AnimatePresence } from "framer-motion";
import { ExplorationGame, type GameState } from "./MindExplorer";

// Extend pour utiliser SVGLoader
extend({ SVGLoader });

interface Letter3DProps {
  svgPath: string;
  position: [number, number, number];
  index: number;
  isActive: boolean;
  isHovered: boolean;
  onClick: () => void;
  onHover: (hovered: boolean) => void;
}

function Letter3D({
  svgPath,
  position,
  index,
  isActive,
  isHovered,
  onClick,
  onHover,
}: Letter3DProps) {
  const meshRef = useRef<THREE.Mesh>(null);
  const [geometry, setGeometry] = useState<THREE.ExtrudeGeometry | null>(null);

  useEffect(() => {
    // Créer la géométrie 3D à partir du path SVG
    const loader = new SVGLoader();
    const svgData = `<svg viewBox="0 0 266 275"><path d="${svgPath}"/></svg>`;
    const svgResult = loader.parse(svgData);

    if (svgResult.paths.length > 0) {
      const path = svgResult.paths[0];
      const shapes = SVGLoader.createShapes(path);

      if (shapes.length > 0) {
        const extrudeSettings = {
          depth: 8.0, // EXTRUSION MASSIVE pour un effet ultra-volumineux
          bevelEnabled: true,
          bevelSegments: 64, // Maximum absolu de segments pour des courbes parfaites
          steps: 15, // Énormément d'étapes pour une extrusion ultra-douce
          bevelSize: 0.6, // Bevel énorme pour des bords ultra-arrondis
          bevelThickness: 0.5, // Épaisseur maximale du bevel
          curveSegments: 80, // Segments maximum pour une qualité parfaite
        };

        const extrudeGeometry = new THREE.ExtrudeGeometry(
          shapes,
          extrudeSettings
        );
        extrudeGeometry.center();
        extrudeGeometry.scale(0.012, -0.012, 0.012); // Taille réduite pour éviter la coupure

        // Calculer les normales pour un rendu plus lisse
        extrudeGeometry.computeVertexNormals();

        setGeometry(extrudeGeometry);
      }
    }
  }, [svgPath]);

  useFrame((state) => {
    if (meshRef.current) {
      // Animation de base flottante
      meshRef.current.position.y =
        position[1] + Math.sin(state.clock.elapsedTime + index) * 0.2;
      meshRef.current.rotation.x =
        Math.sin(state.clock.elapsedTime * 0.5 + index) * 0.1;
      meshRef.current.rotation.z =
        Math.cos(state.clock.elapsedTime * 0.3 + index) * 0.05;

      // Animation quand actif
      if (isActive) {
        meshRef.current.rotation.y += 0.02;
        meshRef.current.scale.setScalar(
          1.3 + Math.sin(state.clock.elapsedTime * 4) * 0.1
        );
      } else if (isHovered) {
        meshRef.current.scale.setScalar(1.1);
      } else {
        meshRef.current.scale.setScalar(1);
      }
    }
  });

  if (!geometry) return null;

  return (
    <Float speed={2} rotationIntensity={0.3} floatIntensity={0.4}>
      <mesh
        ref={meshRef}
        geometry={geometry}
        position={position}
        onClick={onClick}
        onPointerOver={() => onHover(true)}
        onPointerOut={() => onHover(false)}
      >
        <MeshDistortMaterial
          color={isActive ? "#ffffff" : isHovered ? "#f0f0f0" : "#d8d8d8"}
          distort={isActive ? 0.2 : isHovered ? 0.08 : 0.03} // Moins de distorsion pour préserver les bords arrondis
          speed={isActive ? 4 : isHovered ? 2 : 1}
          roughness={isActive ? 0.05 : 0.15} // Plus lisse pour accentuer les reflets sur l'épaisseur
          metalness={isActive ? 0.95 : 0.8} // Plus métallique pour des reflets plus nets
          clearcoat={1.0} // Maximum de vernis pour un effet ultra-brillant
          clearcoatRoughness={0.05} // Très lisse pour des reflets parfaits
        />
      </mesh>
    </Float>
  );
}

interface SVG3DLogoProps {
  onLetterClick?: (letter: string, index: number) => void;
  activeLetters?: boolean[];
  className?: string;
  gameMode?: boolean;
  onGameStateChange?: (state: GameState) => void;
}

export function SVG3DLogo({
  onLetterClick,
  activeLetters = [false, false, false, false, false],
  className = "",
  gameMode = false,
  onGameStateChange,
}: SVG3DLogoProps) {
  const [hoveredLetters, setHoveredLetters] = useState<boolean[]>([
    false,
    false,
    false,
    false,
    false,
  ]);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const letters = [
    {
      letter: "h",
      path: "M3.55881 1.54838C6.09211 -0.984916 9.69161 -0.318516 11.425 2.88138C12.4912 4.88138 12.7582 11.8147 11.9582 27.4148C10.6249 58.7478 11.1585 60.2148 23.1582 54.3478C34.3582 48.8818 45.6912 51.5478 51.9582 61.1478C56.4912 67.8148 59.5592 83.0148 59.8252 99.4148C59.9592 111.148 59.5582 113.415 57.4252 115.815C55.9582 117.415 53.5582 118.615 52.0922 118.348C49.5582 117.948 49.2912 117.015 49.4252 109.948C49.5582 105.548 49.6922 97.1478 49.8252 91.2818C50.2252 77.6818 47.9582 71.0148 42.0922 67.0148C37.0252 63.5488 27.2912 62.8808 22.4912 65.6808C14.2252 70.3478 11.4255 78.4818 9.29221 103.282C8.89221 108.348 8.09211 113.681 7.82541 114.882C7.02541 117.815 1.29151 118.081 0.224813 115.415C-0.175087 114.346 -0.0413892 99.2798 0.625211 81.8148C1.29191 64.2148 1.82491 39.4148 1.95821 26.4808C1.95821 10.2147 2.49211 2.61508 3.55881 1.54838Z",
    },
    {
      letter: "o",
      path: "M137.425 21.4154C139.025 21.2821 142.225 22.6156 144.759 24.3486C147.159 25.9496 151.025 29.9486 153.292 33.1486C156.759 38.2156 157.292 39.8156 157.158 47.4156C156.358 81.1486 126.091 101.816 107.691 80.8826C100.492 72.6156 98.8922 56.6156 104.092 44.0826C106.758 37.6826 114.891 28.0826 119.158 26.3486C124.491 24.0826 134.358 21.4155 137.425 21.4154ZM139.825 32.7496C131.959 30.0826 129.025 30.0826 123.958 32.7496C114.092 37.8166 108.625 47.2826 108.625 59.0156C108.625 67.9496 111.025 74.7496 115.425 77.8156C119.158 80.3496 129.292 80.6156 133.559 78.2156C139.292 75.0146 145.825 65.6826 147.958 57.8156C150.491 48.2156 150.492 45.0156 148.092 39.4156C146.625 35.9486 144.892 34.4826 139.825 32.7496Z",
    },
    {
      letter: "w",
      path: "M219.959 33.2824C222.892 24.3494 225.426 21.9491 228.226 25.5494C229.026 26.4824 229.959 31.8154 230.359 37.2824C231.026 47.6824 236.093 64.7494 238.76 65.8154C241.159 66.7484 242.36 65.1484 248.359 52.7494C256.626 35.9494 260.226 30.6154 263.293 30.6154C264.759 30.6154 265.959 31.4154 265.959 32.4814C265.959 35.6814 260.76 46.8824 255.293 55.2824C252.493 59.6824 248.626 67.2814 246.626 72.2154C243.026 81.1484 239.159 86.2154 236.893 84.7494C236.226 84.3494 234.626 81.1484 233.426 77.6814C232.092 74.2154 230.226 69.1484 229.293 66.6154C228.226 64.0824 226.759 59.4154 225.959 56.2154C225.159 52.8824 223.693 50.6154 222.626 50.6154C221.426 50.6154 217.426 57.2824 212.76 67.0154C202.76 87.5494 199.293 90.8814 197.56 81.6814C196.226 75.1484 187.293 51.5484 182.626 42.2154C180.093 37.1484 177.959 31.5494 177.959 29.8154C177.959 26.8824 178.493 26.4824 181.56 26.8824C185.693 27.4154 187.959 31.2824 194.492 49.6814C200.092 65.1474 201.693 68.2154 203.426 67.2824C205.292 65.9494 217.292 41.6824 219.959 33.2824Z",
    },
    {
      letter: "r",
      path: "M34.7586 144.216C36.8916 140.883 40.8916 140.35 43.1576 143.149C43.9576 144.216 45.2906 148.483 45.9576 152.616C47.9576 165.283 52.4916 168.483 61.0246 163.55C64.0916 161.683 67.6916 159.016 69.1576 157.416C74.4916 151.549 87.9576 151.016 92.6246 156.616C96.6246 161.416 91.2906 165.949 76.2246 170.616C66.0906 173.816 56.8916 180.216 55.8246 185.016C55.4246 187.017 53.8246 191.549 52.3576 195.149C49.5576 201.949 49.5576 211.683 52.0916 231.416C53.1576 239.949 50.0916 247.016 45.5586 246.349C43.5586 246.083 41.9586 243.949 39.8246 238.216C36.7586 230.482 36.3576 223.416 36.6246 186.349C36.6246 180.083 36.3576 174.084 35.9576 173.016C35.5576 172.083 34.7576 165.816 34.0916 159.283C33.0246 149.55 33.1586 146.749 34.7586 144.216Z",
    },
    {
      letter: "u",
      path: "M148.892 228.881C148.092 209.814 152.092 200.481 159.691 204.214C161.825 205.28 162.092 207.281 161.825 221.414C161.692 236.214 161.958 237.947 165.158 244.614C172.758 260.081 184.759 263.414 190.759 251.548C191.959 249.148 194.358 245.814 195.958 243.947C200.091 239.414 201.958 231.947 201.958 219.814C201.958 211.681 202.491 209.28 204.491 207.414C207.558 204.614 210.092 205.548 213.158 210.748C215.158 214.214 215.425 216.481 214.625 224.748C213.825 233.281 212.625 237.014 205.825 250.347C198.892 263.947 197.158 266.481 191.425 270.347C185.158 274.614 184.891 274.614 177.958 273.147C172.092 271.947 169.558 270.348 163.958 264.748C153.558 254.348 149.558 244.881 148.892 228.881Z",
    },
  ];

  const positions: [number, number, number][] = gameMode
    ? [
        [-6, 2, -3],
        [-3, -1, 4],
        [0, 3, -2],
        [4, -2, 5],
        [7, 1, -4],
      ] // Mode jeu: disposition 3D immersive
    : isMobile
    ? [
        [-1.2, 1.2, 0],
        [0, 0.6, 0],
        [1.2, 0, 0],
        [-0.6, -0.6, 0],
        [0.6, -1.2, 0],
      ] // Mobile: disposition verticale compacte
    : [
        [-4.5, 0, 0],
        [-2.2, 0, 0],
        [0, 0, 0],
        [2.2, 0, 0],
        [4.5, 0, 0],
      ]; // Desktop: disposition horizontale plus espacée

  const handleLetterClick = (letter: string, index: number) => {
    onLetterClick?.(letter, index);
  };

  const handleLetterHover = (index: number, hovered: boolean) => {
    const newHovered = [...hoveredLetters];
    newHovered[index] = hovered;
    setHoveredLetters(newHovered);
  };

  // Préparer les données des lettres pour le jeu
  const letterData = letters.map((letter, index) => ({
    ...letter,
    position: positions[index],
  }));

  return (
    <div className={`w-full h-screen ${className}`}>
      <Canvas
        camera={{
          position: gameMode ? [0, 5, 20] : isMobile ? [0, 0, 8] : [0, 0, 15],
          fov: gameMode ? 75 : isMobile ? 70 : 65,
        }}
        gl={{ antialias: true, alpha: true }}
        style={{ width: "100%", height: "100%" }}
      >
        <ambientLight intensity={0.3} />
        <pointLight position={[10, 10, 10]} intensity={1.2} color="#ffffff" />
        <pointLight
          position={[-10, -10, -10]}
          intensity={0.6}
          color="#8892b0"
        />
        <pointLight position={[0, 15, 5]} intensity={0.8} color="#64ffda" />
        <spotLight
          position={[0, 10, 10]}
          angle={0.3}
          penumbra={1}
          intensity={0.5}
          castShadow
        />

        <Environment preset="night" />

        <Center>
          {letters.map((letterData, index) => (
            <Letter3D
              key={letterData.letter}
              svgPath={letterData.path}
              position={positions[index]}
              index={index}
              isActive={activeLetters[index]}
              isHovered={hoveredLetters[index]}
              onClick={() => handleLetterClick(letterData.letter, index)}
              onHover={(hovered) => handleLetterHover(index, hovered)}
            />
          ))}
        </Center>

        {/* Jeu d'exploration en mode jeu */}
        {gameMode && onGameStateChange && (
          <ExplorationGame
            letters={letterData}
            onGameStateChange={onGameStateChange}
          />
        )}

        <OrbitControls
          enablePan={gameMode}
          enableZoom={gameMode || !isMobile}
          enableRotate={true}
          autoRotate={!gameMode && !isMobile}
          autoRotateSpeed={gameMode ? 0 : 0.3}
          maxDistance={gameMode ? 50 : isMobile ? 10 : 18}
          minDistance={gameMode ? 5 : isMobile ? 5 : 8}
          maxPolarAngle={gameMode ? Math.PI : Math.PI / 1.5}
          minPolarAngle={gameMode ? 0 : Math.PI / 3}
        />
      </Canvas>
    </div>
  );
}
