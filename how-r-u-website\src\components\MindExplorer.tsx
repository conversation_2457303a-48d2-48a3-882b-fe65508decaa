"use client";

import { useRef, useState, useEffect, useMemo } from "react";
import { Canvas, useFrame, useThree } from "@react-three/fiber";
import {
  Center,
  Float,
  MeshDistortMaterial,
  Environment,
  Stars,
  Text3D,
  OrbitControls,
  Sphere,
  Trail,
} from "@react-three/drei";
import { SVGLoader } from "three-stdlib";
import * as THREE from "three";
import { motion, AnimatePresence } from "framer-motion";

// Types pour le jeu
interface GameState {
  mode: 'exploration' | 'connection' | 'memory';
  currentLetter: number | null;
  discoveredMemories: string[];
  connections: number[][];
  playerPosition: THREE.Vector3;
}

interface MemoryFragment {
  id: string;
  text: string;
  position: [number, number, number];
  color: string;
  discovered: boolean;
}

// Composant pour les fragments de mémoire flottants
function MemoryFragment({ fragment, onDiscover }: { 
  fragment: MemoryFragment; 
  onDiscover: (id: string) => void;
}) {
  const meshRef = useRef<THREE.Mesh>(null);
  const [hovered, setHovered] = useState(false);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.position.y += Math.sin(state.clock.elapsedTime + fragment.position[0]) * 0.001;
      meshRef.current.rotation.y += 0.005;
      
      if (hovered) {
        meshRef.current.scale.setScalar(1.2);
      } else {
        meshRef.current.scale.setScalar(fragment.discovered ? 0.8 : 1.0);
      }
    }
  });

  return (
    <Float speed={1.5} rotationIntensity={0.2} floatIntensity={0.3}>
      <mesh
        ref={meshRef}
        position={fragment.position}
        onClick={() => onDiscover(fragment.id)}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        <sphereGeometry args={[0.1, 16, 16]} />
        <meshStandardMaterial
          color={fragment.discovered ? fragment.color : "#444"}
          emissive={fragment.discovered ? fragment.color : "#000"}
          emissiveIntensity={fragment.discovered ? 0.3 : 0}
          transparent
          opacity={fragment.discovered ? 0.8 : 0.4}
        />
      </mesh>
      
      {fragment.discovered && (
        <Text3D
          font="/fonts/helvetiker_regular.typeface.json"
          size={0.05}
          height={0.01}
          position={[0, 0.2, 0]}
        >
          {fragment.text}
          <meshStandardMaterial color={fragment.color} />
        </Text3D>
      )}
    </Float>
  );
}

// Composant pour les connexions neuronales
function NeuralConnection({ start, end, active }: {
  start: THREE.Vector3;
  end: THREE.Vector3;
  active: boolean;
}) {
  const lineRef = useRef<THREE.Line>(null);
  
  const points = useMemo(() => {
    const curve = new THREE.CatmullRomCurve3([
      start,
      new THREE.Vector3(
        (start.x + end.x) / 2 + Math.random() * 2 - 1,
        (start.y + end.y) / 2 + Math.random() * 2 - 1,
        (start.z + end.z) / 2 + Math.random() * 2 - 1
      ),
      end
    ]);
    return curve.getPoints(50);
  }, [start, end]);

  const geometry = useMemo(() => {
    const geometry = new THREE.BufferGeometry().setFromPoints(points);
    return geometry;
  }, [points]);

  useFrame((state) => {
    if (lineRef.current && active) {
      const material = lineRef.current.material as THREE.LineBasicMaterial;
      material.opacity = 0.5 + Math.sin(state.clock.elapsedTime * 3) * 0.3;
    }
  });

  return (
    <line ref={lineRef} geometry={geometry}>
      <lineBasicMaterial
        color={active ? "#64ffda" : "#333"}
        transparent
        opacity={active ? 0.8 : 0.2}
        linewidth={active ? 3 : 1}
      />
    </line>
  );
}

// Composant principal du jeu d'exploration
function ExplorationGame({ letters, onGameStateChange }: {
  letters: any[];
  onGameStateChange: (state: GameState) => void;
}) {
  const [gameState, setGameState] = useState<GameState>({
    mode: 'exploration',
    currentLetter: null,
    discoveredMemories: [],
    connections: [],
    playerPosition: new THREE.Vector3(0, 0, 0),
  });

  const [memoryFragments] = useState<MemoryFragment[]>([
    {
      id: 'memory1',
      text: 'childhood dreams',
      position: [-8, 3, -5],
      color: '#ff6b9d',
      discovered: false,
    },
    {
      id: 'memory2',
      text: 'forgotten words',
      position: [6, -2, 8],
      color: '#4ecdc4',
      discovered: false,
    },
    {
      id: 'memory3',
      text: 'silent thoughts',
      position: [-3, 5, 12],
      color: '#45b7d1',
      discovered: false,
    },
    {
      id: 'memory4',
      text: 'hidden fears',
      position: [10, 1, -8],
      color: '#f9ca24',
      discovered: false,
    },
    {
      id: 'memory5',
      text: 'lost connections',
      position: [-12, -4, 3],
      color: '#6c5ce7',
      discovered: false,
    },
  ]);

  const [discoveredFragments, setDiscoveredFragments] = useState<Set<string>>(new Set());

  const handleMemoryDiscover = (id: string) => {
    setDiscoveredFragments(prev => new Set([...prev, id]));
    const newState = {
      ...gameState,
      discoveredMemories: [...gameState.discoveredMemories, id],
    };
    setGameState(newState);
    onGameStateChange(newState);
  };

  return (
    <>
      {/* Fragments de mémoire */}
      {memoryFragments.map(fragment => (
        <MemoryFragment
          key={fragment.id}
          fragment={{
            ...fragment,
            discovered: discoveredFragments.has(fragment.id),
          }}
          onDiscover={handleMemoryDiscover}
        />
      ))}

      {/* Connexions neuronales entre les lettres */}
      {letters.map((letter, i) => 
        letters.slice(i + 1).map((_, j) => (
          <NeuralConnection
            key={`connection-${i}-${j + i + 1}`}
            start={new THREE.Vector3(...letter.position)}
            end={new THREE.Vector3(...letters[j + i + 1].position)}
            active={discoveredFragments.size > i + j}
          />
        ))
      )}

      {/* Particules atmosphériques */}
      <Stars
        radius={100}
        depth={50}
        count={5000}
        factor={4}
        saturation={0}
        fade
        speed={1}
      />
    </>
  );
}

export { ExplorationGame, type GameState, type MemoryFragment };
