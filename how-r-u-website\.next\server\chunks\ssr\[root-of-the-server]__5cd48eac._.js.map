{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/DreamyLogo.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface DreamyLogoProps {\n  onLetterClick?: (letter: string, index: number) => void;\n}\n\nexport function DreamyLogo({ onLetterClick }: DreamyLogoProps) {\n  const [isHovered, setIsHovered] = useState(false);\n  const [clickSequence, setClickSequence] = useState<number[]>([]);\n  const [activeLetters, setActiveLetters] = useState<boolean[]>([false, false, false, false, false]);\n  const logoRef = useRef<SVGSVGElement>(null);\n\n  const letters = ['h', 'o', 'w', 'r', 'u'];\n  const secretSequence = [0, 4, 1, 3, 2]; // h-u-o-r-w\n\n  const handleLetterClick = (letter: string, index: number) => {\n    const newSequence = [...clickSequence, index];\n    setClickSequence(newSequence);\n\n    // Activate letter with dreamy glow\n    const newActiveLetters = [...activeLetters];\n    newActiveLetters[index] = true;\n    setActiveLetters(newActiveLetters);\n\n    // Reset after 3 seconds\n    setTimeout(() => {\n      const resetLetters = [...activeLetters];\n      resetLetters[index] = false;\n      setActiveLetters(resetLetters);\n    }, 3000);\n\n    // Check if sequence matches secret pattern\n    if (newSequence.length === secretSequence.length) {\n      const isCorrect = newSequence.every((val, i) => val === secretSequence[i]);\n      if (isCorrect) {\n        onLetterClick?.('secret', -1);\n      }\n      setTimeout(() => setClickSequence([]), 1000);\n    }\n\n    onLetterClick?.(letter, index);\n  };\n\n  const letterVariants = {\n    initial: { \n      opacity: 0.7, \n      scale: 1,\n      rotateX: 0,\n      rotateY: 0,\n      z: 0,\n    },\n    hover: { \n      opacity: 1, \n      scale: 1.1,\n      rotateX: 10,\n      rotateY: 5,\n      z: 20,\n      transition: { duration: 0.6, ease: \"easeOut\" }\n    },\n    click: { \n      opacity: [1, 0.3, 1], \n      scale: [1, 0.8, 1.2, 1],\n      rotateX: [0, 15, -10, 0],\n      rotateY: [0, -15, 10, 0],\n      transition: { duration: 0.8, ease: \"easeInOut\" }\n    },\n    active: {\n      opacity: 1,\n      scale: 1.05,\n      filter: 'brightness(1.5) blur(0.5px)',\n      textShadow: '0 0 20px rgba(255, 255, 255, 0.8)',\n    }\n  };\n\n  return (\n    <div className=\"relative perspective-1000\">\n      {/* Dreamy background glow */}\n      <motion.div\n        className=\"absolute inset-0 -m-20\"\n        animate={{\n          background: [\n            'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',\n            'radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%)',\n            'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)',\n          ]\n        }}\n        transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\" }}\n      />\n\n      {/* 3D Logo Container */}\n      <motion.div\n        className=\"relative transform-gpu\"\n        onMouseEnter={() => setIsHovered(true)}\n        onMouseLeave={() => setIsHovered(false)}\n        animate={{\n          rotateX: isHovered ? 5 : 0,\n          rotateY: isHovered ? 5 : 0,\n          scale: isHovered ? 1.05 : 1,\n        }}\n        transition={{ duration: 0.6, ease: \"easeOut\" }}\n        style={{ \n          transformStyle: 'preserve-3d',\n          perspective: '1000px'\n        }}\n      >\n        <svg\n          ref={logoRef}\n          width=\"266\"\n          height=\"275\"\n          viewBox=\"0 0 266 275\"\n          fill=\"none\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          className=\"w-80 h-auto cursor-pointer filter drop-shadow-2xl\"\n          style={{\n            filter: isHovered \n              ? 'brightness(1.3) drop-shadow(0 0 30px rgba(255, 255, 255, 0.5))' \n              : 'brightness(1) drop-shadow(0 0 15px rgba(255, 255, 255, 0.2))',\n            transition: 'filter 0.6s ease'\n          }}\n        >\n          <g id=\"howru\">\n            {/* Letter H */}\n            <motion.path\n              id=\"h\"\n              d=\"M3.55881 1.54838C6.09211 -0.984916 9.69161 -0.318516 11.425 2.88138C12.4912 4.88138 12.7582 11.8147 11.9582 27.4148C10.6249 58.7478 11.1585 60.2148 23.1582 54.3478C34.3582 48.8818 45.6912 51.5478 51.9582 61.1478C56.4912 67.8148 59.5592 83.0148 59.8252 99.4148C59.9592 111.148 59.5582 113.415 57.4252 115.815C55.9582 117.415 53.5582 118.615 52.0922 118.348C49.5582 117.948 49.2912 117.015 49.4252 109.948C49.5582 105.548 49.6922 97.1478 49.8252 91.2818C50.2252 77.6818 47.9582 71.0148 42.0922 67.0148C37.0252 63.5488 27.2912 62.8808 22.4912 65.6808C14.2252 70.3478 11.4255 78.4818 9.29221 103.282C8.89221 108.348 8.09211 113.681 7.82541 114.882C7.02541 117.815 1.29151 118.081 0.224813 115.415C-0.175087 114.346 -0.0413892 99.2798 0.625211 81.8148C1.29191 64.2148 1.82491 39.4148 1.95821 26.4808C1.95821 10.2147 2.49211 2.61508 3.55881 1.54838Z\"\n              fill={activeLetters[0] ? \"rgba(255, 255, 255, 0.9)\" : \"rgba(255, 255, 255, 0.7)\"}\n              variants={letterVariants}\n              initial=\"initial\"\n              whileHover=\"hover\"\n              whileTap=\"click\"\n              animate={activeLetters[0] ? \"active\" : \"initial\"}\n              onClick={() => handleLetterClick('h', 0)}\n              className=\"cursor-pointer animate-letter-dance\"\n              style={{ \n                animationDelay: '0s',\n                transformOrigin: 'center',\n                filter: activeLetters[0] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'\n              }}\n            />\n            \n            {/* Letter O */}\n            <motion.path\n              id=\"o\"\n              d=\"M137.425 21.4154C139.025 21.2821 142.225 22.6156 144.759 24.3486C147.159 25.9496 151.025 29.9486 153.292 33.1486C156.759 38.2156 157.292 39.8156 157.158 47.4156C156.358 81.1486 126.091 101.816 107.691 80.8826C100.492 72.6156 98.8922 56.6156 104.092 44.0826C106.758 37.6826 114.891 28.0826 119.158 26.3486C124.491 24.0826 134.358 21.4155 137.425 21.4154ZM139.825 32.7496C131.959 30.0826 129.025 30.0826 123.958 32.7496C114.092 37.8166 108.625 47.2826 108.625 59.0156C108.625 67.9496 111.025 74.7496 115.425 77.8156C119.158 80.3496 129.292 80.6156 133.559 78.2156C139.292 75.0146 145.825 65.6826 147.958 57.8156C150.491 48.2156 150.492 45.0156 148.092 39.4156C146.625 35.9486 144.892 34.4826 139.825 32.7496Z\"\n              fill={activeLetters[1] ? \"rgba(255, 255, 255, 0.9)\" : \"rgba(255, 255, 255, 0.7)\"}\n              variants={letterVariants}\n              initial=\"initial\"\n              whileHover=\"hover\"\n              whileTap=\"click\"\n              animate={activeLetters[1] ? \"active\" : \"initial\"}\n              onClick={() => handleLetterClick('o', 1)}\n              className=\"cursor-pointer animate-dreamy-float\"\n              style={{ \n                animationDelay: '1s',\n                transformOrigin: 'center',\n                filter: activeLetters[1] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'\n              }}\n            />\n            \n            {/* Letter W */}\n            <motion.path\n              id=\"w\"\n              d=\"M219.959 33.2824C222.892 24.3494 225.426 21.9491 228.226 25.5494C229.026 26.4824 229.959 31.8154 230.359 37.2824C231.026 47.6824 236.093 64.7494 238.76 65.8154C241.159 66.7484 242.36 65.1484 248.359 52.7494C256.626 35.9494 260.226 30.6154 263.293 30.6154C264.759 30.6154 265.959 31.4154 265.959 32.4814C265.959 35.6814 260.76 46.8824 255.293 55.2824C252.493 59.6824 248.626 67.2814 246.626 72.2154C243.026 81.1484 239.159 86.2154 236.893 84.7494C236.226 84.3494 234.626 81.1484 233.426 77.6814C232.092 74.2154 230.226 69.1484 229.293 66.6154C228.226 64.0824 226.759 59.4154 225.959 56.2154C225.159 52.8824 223.693 50.6154 222.626 50.6154C221.426 50.6154 217.426 57.2824 212.76 67.0154C202.76 87.5494 199.293 90.8814 197.56 81.6814C196.226 75.1484 187.293 51.5484 182.626 42.2154C180.093 37.1484 177.959 31.5494 177.959 29.8154C177.959 26.8824 178.493 26.4824 181.56 26.8824C185.693 27.4154 187.959 31.2824 194.492 49.6814C200.092 65.1474 201.693 68.2154 203.426 67.2824C205.292 65.9494 217.292 41.6824 219.959 33.2824Z\"\n              fill={activeLetters[2] ? \"rgba(255, 255, 255, 0.9)\" : \"rgba(255, 255, 255, 0.7)\"}\n              variants={letterVariants}\n              initial=\"initial\"\n              whileHover=\"hover\"\n              whileTap=\"click\"\n              animate={activeLetters[2] ? \"active\" : \"initial\"}\n              onClick={() => handleLetterClick('w', 2)}\n              className=\"cursor-pointer animate-gentle-sway\"\n              style={{ \n                animationDelay: '2s',\n                transformOrigin: 'center',\n                filter: activeLetters[2] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'\n              }}\n            />\n            \n            {/* Letter R */}\n            <motion.path\n              id=\"r\"\n              d=\"M34.7586 144.216C36.8916 140.883 40.8916 140.35 43.1576 143.149C43.9576 144.216 45.2906 148.483 45.9576 152.616C47.9576 165.283 52.4916 168.483 61.0246 163.55C64.0916 161.683 67.6916 159.016 69.1576 157.416C74.4916 151.549 87.9576 151.016 92.6246 156.616C96.6246 161.416 91.2906 165.949 76.2246 170.616C66.0906 173.816 56.8916 180.216 55.8246 185.016C55.4246 187.017 53.8246 191.549 52.3576 195.149C49.5576 201.949 49.5576 211.683 52.0916 231.416C53.1576 239.949 50.0916 247.016 45.5586 246.349C43.5586 246.083 41.9586 243.949 39.8246 238.216C36.7586 230.482 36.3576 223.416 36.6246 186.349C36.6246 180.083 36.3576 174.084 35.9576 173.016C35.5576 172.083 34.7576 165.816 34.0916 159.283C33.0246 149.55 33.1586 146.749 34.7586 144.216Z\"\n              fill={activeLetters[3] ? \"rgba(255, 255, 255, 0.9)\" : \"rgba(255, 255, 255, 0.7)\"}\n              variants={letterVariants}\n              initial=\"initial\"\n              whileHover=\"hover\"\n              whileTap=\"click\"\n              animate={activeLetters[3] ? \"active\" : \"initial\"}\n              onClick={() => handleLetterClick('r', 3)}\n              className=\"cursor-pointer animate-depth-shift\"\n              style={{ \n                animationDelay: '3s',\n                transformOrigin: 'center',\n                filter: activeLetters[3] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'\n              }}\n            />\n            \n            {/* Letter U */}\n            <motion.path\n              id=\"u\"\n              d=\"M148.892 228.881C148.092 209.814 152.092 200.481 159.691 204.214C161.825 205.28 162.092 207.281 161.825 221.414C161.692 236.214 161.958 237.947 165.158 244.614C172.758 260.081 184.759 263.414 190.759 251.548C191.959 249.148 194.358 245.814 195.958 243.947C200.091 239.414 201.958 231.947 201.958 219.814C201.958 211.681 202.491 209.28 204.491 207.414C207.558 204.614 210.092 205.548 213.158 210.748C215.158 214.214 215.425 216.481 214.625 224.748C213.825 233.281 212.625 237.014 205.825 250.347C198.892 263.947 197.158 266.481 191.425 270.347C185.158 274.614 184.891 274.614 177.958 273.147C172.092 271.947 169.558 270.348 163.958 264.748C153.558 254.348 149.558 244.881 148.892 228.881Z\"\n              fill={activeLetters[4] ? \"rgba(255, 255, 255, 0.9)\" : \"rgba(255, 255, 255, 0.7)\"}\n              variants={letterVariants}\n              initial=\"initial\"\n              whileHover=\"hover\"\n              whileTap=\"click\"\n              animate={activeLetters[4] ? \"active\" : \"initial\"}\n              onClick={() => handleLetterClick('u', 4)}\n              className=\"cursor-pointer animate-whisper-glow\"\n              style={{ \n                animationDelay: '4s',\n                transformOrigin: 'center',\n                filter: activeLetters[4] ? 'drop-shadow(0 0 15px rgba(255, 255, 255, 0.8))' : 'none'\n              }}\n            />\n          </g>\n        </svg>\n\n        {/* 3D depth layers */}\n        <motion.div\n          className=\"absolute inset-0 -z-10\"\n          animate={{\n            rotateX: isHovered ? 3 : 0,\n            rotateY: isHovered ? 3 : 0,\n            scale: isHovered ? 1.02 : 1,\n            opacity: isHovered ? 0.3 : 0.1,\n          }}\n          transition={{ duration: 0.6, ease: \"easeOut\" }}\n          style={{ \n            transformStyle: 'preserve-3d',\n            transform: 'translateZ(-20px)',\n            filter: 'blur(2px)',\n            background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)'\n          }}\n        />\n      </motion.div>\n\n      {/* Floating particles around logo */}\n      <motion.div\n        className=\"absolute inset-0 pointer-events-none\"\n        animate={{\n          rotate: 360,\n        }}\n        transition={{ duration: 60, repeat: Infinity, ease: \"linear\" }}\n      >\n        {[...Array(8)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-1 h-1 bg-white rounded-full opacity-30\"\n            style={{\n              left: `${20 + Math.cos(i * 45 * Math.PI / 180) * 150}px`,\n              top: `${20 + Math.sin(i * 45 * Math.PI / 180) * 150}px`,\n            }}\n            animate={{\n              opacity: [0.1, 0.5, 0.1],\n              scale: [0.5, 1.5, 0.5],\n            }}\n            transition={{\n              duration: 3 + i * 0.5,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: i * 0.3,\n            }}\n          />\n        ))}\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASO,SAAS,WAAW,EAAE,aAAa,EAAmB;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAAC;QAAO;QAAO;QAAO;QAAO;KAAM;IACjG,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IAEtC,MAAM,UAAU;QAAC;QAAK;QAAK;QAAK;QAAK;KAAI;IACzC,MAAM,iBAAiB;QAAC;QAAG;QAAG;QAAG;QAAG;KAAE,EAAE,YAAY;IAEpD,MAAM,oBAAoB,CAAC,QAAgB;QACzC,MAAM,cAAc;eAAI;YAAe;SAAM;QAC7C,iBAAiB;QAEjB,mCAAmC;QACnC,MAAM,mBAAmB;eAAI;SAAc;QAC3C,gBAAgB,CAAC,MAAM,GAAG;QAC1B,iBAAiB;QAEjB,wBAAwB;QACxB,WAAW;YACT,MAAM,eAAe;mBAAI;aAAc;YACvC,YAAY,CAAC,MAAM,GAAG;YACtB,iBAAiB;QACnB,GAAG;QAEH,2CAA2C;QAC3C,IAAI,YAAY,MAAM,KAAK,eAAe,MAAM,EAAE;YAChD,MAAM,YAAY,YAAY,KAAK,CAAC,CAAC,KAAK,IAAM,QAAQ,cAAc,CAAC,EAAE;YACzE,IAAI,WAAW;gBACb,gBAAgB,UAAU,CAAC;YAC7B;YACA,WAAW,IAAM,iBAAiB,EAAE,GAAG;QACzC;QAEA,gBAAgB,QAAQ;IAC1B;IAEA,MAAM,iBAAiB;QACrB,SAAS;YACP,SAAS;YACT,OAAO;YACP,SAAS;YACT,SAAS;YACT,GAAG;QACL;QACA,OAAO;YACL,SAAS;YACT,OAAO;YACP,SAAS;YACT,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;QAC/C;QACA,OAAO;YACL,SAAS;gBAAC;gBAAG;gBAAK;aAAE;YACpB,OAAO;gBAAC;gBAAG;gBAAK;gBAAK;aAAE;YACvB,SAAS;gBAAC;gBAAG;gBAAI,CAAC;gBAAI;aAAE;YACxB,SAAS;gBAAC;gBAAG,CAAC;gBAAI;gBAAI;aAAE;YACxB,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAY;QACjD;QACA,QAAQ;YACN,SAAS;YACT,OAAO;YACP,QAAQ;YACR,YAAY;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,YAAY;wBACV;wBACA;wBACA;qBACD;gBACH;gBACA,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAY;;;;;;0BAIjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,cAAc,IAAM,aAAa;gBACjC,cAAc,IAAM,aAAa;gBACjC,SAAS;oBACP,SAAS,YAAY,IAAI;oBACzB,SAAS,YAAY,IAAI;oBACzB,OAAO,YAAY,OAAO;gBAC5B;gBACA,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAU;gBAC7C,OAAO;oBACL,gBAAgB;oBAChB,aAAa;gBACf;;kCAEA,8OAAC;wBACC,KAAK;wBACL,OAAM;wBACN,QAAO;wBACP,SAAQ;wBACR,MAAK;wBACL,OAAM;wBACN,WAAU;wBACV,OAAO;4BACL,QAAQ,YACJ,mEACA;4BACJ,YAAY;wBACd;kCAEA,cAAA,8OAAC;4BAAE,IAAG;;8CAEJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,6BAA6B;oCACtD,UAAU;oCACV,SAAQ;oCACR,YAAW;oCACX,UAAS;oCACT,SAAS,aAAa,CAAC,EAAE,GAAG,WAAW;oCACvC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,gBAAgB;wCAChB,iBAAiB;wCACjB,QAAQ,aAAa,CAAC,EAAE,GAAG,mDAAmD;oCAChF;;;;;;8CAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,6BAA6B;oCACtD,UAAU;oCACV,SAAQ;oCACR,YAAW;oCACX,UAAS;oCACT,SAAS,aAAa,CAAC,EAAE,GAAG,WAAW;oCACvC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,gBAAgB;wCAChB,iBAAiB;wCACjB,QAAQ,aAAa,CAAC,EAAE,GAAG,mDAAmD;oCAChF;;;;;;8CAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,6BAA6B;oCACtD,UAAU;oCACV,SAAQ;oCACR,YAAW;oCACX,UAAS;oCACT,SAAS,aAAa,CAAC,EAAE,GAAG,WAAW;oCACvC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,gBAAgB;wCAChB,iBAAiB;wCACjB,QAAQ,aAAa,CAAC,EAAE,GAAG,mDAAmD;oCAChF;;;;;;8CAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,6BAA6B;oCACtD,UAAU;oCACV,SAAQ;oCACR,YAAW;oCACX,UAAS;oCACT,SAAS,aAAa,CAAC,EAAE,GAAG,WAAW;oCACvC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,gBAAgB;wCAChB,iBAAiB;wCACjB,QAAQ,aAAa,CAAC,EAAE,GAAG,mDAAmD;oCAChF;;;;;;8CAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,6BAA6B;oCACtD,UAAU;oCACV,SAAQ;oCACR,YAAW;oCACX,UAAS;oCACT,SAAS,aAAa,CAAC,EAAE,GAAG,WAAW;oCACvC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,gBAAgB;wCAChB,iBAAiB;wCACjB,QAAQ,aAAa,CAAC,EAAE,GAAG,mDAAmD;oCAChF;;;;;;;;;;;;;;;;;kCAMN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,SAAS,YAAY,IAAI;4BACzB,SAAS,YAAY,IAAI;4BACzB,OAAO,YAAY,OAAO;4BAC1B,SAAS,YAAY,MAAM;wBAC7B;wBACA,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAU;wBAC7C,OAAO;4BACL,gBAAgB;4BAChB,WAAW;4BACX,QAAQ;4BACR,YAAY;wBACd;;;;;;;;;;;;0BAKJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,QAAQ;gBACV;gBACA,YAAY;oBAAE,UAAU;oBAAI,QAAQ;oBAAU,MAAM;gBAAS;0BAE5D;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,OAAO;4BACL,MAAM,GAAG,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;4BACxD,KAAK,GAAG,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;wBACzD;wBACA,SAAS;4BACP,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;4BACxB,OAAO;gCAAC;gCAAK;gCAAK;6BAAI;wBACxB;wBACA,YAAY;4BACV,UAAU,IAAI,IAAI;4BAClB,QAAQ;4BACR,MAAM;4BACN,OAAO,IAAI;wBACb;uBAfK;;;;;;;;;;;;;;;;AAqBjB", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/AnimatedSVGLogo.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState, useEffect } from 'react';\nimport { motion, useAnimation, useMotionValue, useTransform } from 'framer-motion';\nimport { gsap } from 'gsap';\n\ninterface AnimatedSVGLogoProps {\n  onLetterClick?: (letter: string, index: number) => void;\n}\n\nexport function AnimatedSVGLogo({ onLetterClick }: AnimatedSVGLogoProps) {\n  const svgRef = useRef<SVGSVGElement>(null);\n  const [activeLetters, setActiveLetters] = useState<boolean[]>([false, false, false, false, false]);\n  const [isHovered, setIsHovered] = useState(false);\n  const [clickSequence, setClickSequence] = useState<number[]>([]);\n  \n  const mouseX = useMotionValue(0);\n  const mouseY = useMotionValue(0);\n  \n  const rotateX = useTransform(mouseY, [-300, 300], [15, -15]);\n  const rotateY = useTransform(mouseX, [-300, 300], [-15, 15]);\n  \n  const letters = ['h', 'o', 'w', 'r', 'u'];\n  const secretSequence = [0, 4, 1, 3, 2];\n\n  useEffect(() => {\n    // Animation d'entrée épique avec GSAP\n    if (svgRef.current) {\n      const paths = svgRef.current.querySelectorAll('path');\n      \n      gsap.set(paths, { \n        scale: 0,\n        rotation: 360,\n        transformOrigin: 'center',\n        opacity: 0\n      });\n      \n      gsap.to(paths, {\n        scale: 1,\n        rotation: 0,\n        opacity: 1,\n        duration: 2,\n        ease: \"elastic.out(1, 0.5)\",\n        stagger: 0.3,\n        delay: 0.5\n      });\n      \n      // Animation continue de respiration\n      gsap.to(svgRef.current, {\n        scale: 1.05,\n        duration: 3,\n        ease: \"power2.inOut\",\n        yoyo: true,\n        repeat: -1\n      });\n    }\n  }, []);\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    const rect = e.currentTarget.getBoundingClientRect();\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n    \n    mouseX.set(e.clientX - centerX);\n    mouseY.set(e.clientY - centerY);\n  };\n\n  const handleLetterClick = (letter: string, index: number) => {\n    const newSequence = [...clickSequence, index];\n    setClickSequence(newSequence);\n\n    // Animation de clic avec GSAP\n    if (svgRef.current) {\n      const path = svgRef.current.querySelector(`#${letter}`);\n      if (path) {\n        gsap.to(path, {\n          scale: 1.5,\n          rotation: 360,\n          duration: 0.8,\n          ease: \"back.out(1.7)\",\n          yoyo: true,\n          repeat: 1,\n          transformOrigin: 'center'\n        });\n        \n        // Effet de particules\n        gsap.to(path, {\n          filter: 'drop-shadow(0 0 20px #ffffff) drop-shadow(0 0 40px #ffffff)',\n          duration: 0.5,\n          yoyo: true,\n          repeat: 3\n        });\n      }\n    }\n\n    // Activer la lettre\n    const newActiveLetters = [...activeLetters];\n    newActiveLetters[index] = true;\n    setActiveLetters(newActiveLetters);\n\n    setTimeout(() => {\n      const resetLetters = [...activeLetters];\n      resetLetters[index] = false;\n      setActiveLetters(resetLetters);\n    }, 3000);\n\n    // Vérifier séquence secrète\n    if (newSequence.length === secretSequence.length) {\n      const isCorrect = newSequence.every((val, i) => val === secretSequence[i]);\n      if (isCorrect) {\n        // Animation secrète épique !\n        if (svgRef.current) {\n          const paths = svgRef.current.querySelectorAll('path');\n          gsap.to(paths, {\n            scale: 2,\n            rotation: 720,\n            duration: 3,\n            ease: \"power4.out\",\n            stagger: 0.1,\n            yoyo: true,\n            repeat: 1,\n            transformOrigin: 'center'\n          });\n          \n          gsap.to(svgRef.current, {\n            filter: 'hue-rotate(360deg) saturate(2) brightness(1.5)',\n            duration: 3,\n            ease: \"power2.inOut\"\n          });\n        }\n        onLetterClick?.('secret', -1);\n      }\n      setTimeout(() => setClickSequence([]), 1000);\n    }\n\n    onLetterClick?.(letter, index);\n  };\n\n  const handleHover = () => {\n    setIsHovered(true);\n    if (svgRef.current) {\n      gsap.to(svgRef.current.querySelectorAll('path'), {\n        scale: 1.1,\n        duration: 0.3,\n        ease: \"power2.out\",\n        stagger: 0.05,\n        transformOrigin: 'center'\n      });\n    }\n  };\n\n  const handleLeave = () => {\n    setIsHovered(false);\n    if (svgRef.current) {\n      gsap.to(svgRef.current.querySelectorAll('path'), {\n        scale: 1,\n        duration: 0.3,\n        ease: \"power2.out\",\n        stagger: 0.05,\n        transformOrigin: 'center'\n      });\n    }\n  };\n\n  return (\n    <div className=\"relative w-full h-[400px] flex items-center justify-center perspective-1000\">\n      {/* Effet de fond animé */}\n      <motion.div\n        className=\"absolute inset-0 opacity-20\"\n        animate={{\n          background: [\n            'radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%)',\n            'radial-gradient(circle at 80% 70%, rgba(255,255,255,0.2) 0%, transparent 50%)',\n            'radial-gradient(circle at 50% 50%, rgba(255,255,255,0.15) 0%, transparent 50%)',\n            'radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 0%, transparent 50%)',\n          ]\n        }}\n        transition={{ duration: 8, repeat: Infinity, ease: \"easeInOut\" }}\n      />\n      \n      <motion.div\n        className=\"relative\"\n        style={{\n          rotateX,\n          rotateY,\n          transformStyle: 'preserve-3d'\n        }}\n        onMouseMove={handleMouseMove}\n        onMouseEnter={handleHover}\n        onMouseLeave={handleLeave}\n        whileHover={{ scale: 1.05 }}\n        transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n      >\n        <motion.svg\n          ref={svgRef}\n          width=\"320\"\n          height=\"330\"\n          viewBox=\"0 0 266 275\"\n          fill=\"none\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          className=\"cursor-pointer filter drop-shadow-2xl\"\n          initial={{ opacity: 0, scale: 0.5 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 1.5, ease: \"easeOut\" }}\n        >\n          {/* Définition des filtres pour effets spéciaux */}\n          <defs>\n            <filter id=\"glow\">\n              <feGaussianBlur stdDeviation=\"3\" result=\"coloredBlur\"/>\n              <feMerge> \n                <feMergeNode in=\"coloredBlur\"/>\n                <feMergeNode in=\"SourceGraphic\"/>\n              </feMerge>\n            </filter>\n            <filter id=\"turbulence\">\n              <feTurbulence baseFrequency=\"0.02\" numOctaves=\"3\" result=\"noise\"/>\n              <feDisplacementMap in=\"SourceGraphic\" in2=\"noise\" scale=\"2\"/>\n            </filter>\n          </defs>\n          \n          <g id=\"howru\">\n            {/* Letter H */}\n            <motion.path\n              id=\"h\"\n              d=\"M3.55881 1.54838C6.09211 -0.984916 9.69161 -0.318516 11.425 2.88138C12.4912 4.88138 12.7582 11.8147 11.9582 27.4148C10.6249 58.7478 11.1585 60.2148 23.1582 54.3478C34.3582 48.8818 45.6912 51.5478 51.9582 61.1478C56.4912 67.8148 59.5592 83.0148 59.8252 99.4148C59.9592 111.148 59.5582 113.415 57.4252 115.815C55.9582 117.415 53.5582 118.615 52.0922 118.348C49.5582 117.948 49.2912 117.015 49.4252 109.948C49.5582 105.548 49.6922 97.1478 49.8252 91.2818C50.2252 77.6818 47.9582 71.0148 42.0922 67.0148C37.0252 63.5488 27.2912 62.8808 22.4912 65.6808C14.2252 70.3478 11.4255 78.4818 9.29221 103.282C8.89221 108.348 8.09211 113.681 7.82541 114.882C7.02541 117.815 1.29151 118.081 0.224813 115.415C-0.175087 114.346 -0.0413892 99.2798 0.625211 81.8148C1.29191 64.2148 1.82491 39.4148 1.95821 26.4808C1.95821 10.2147 2.49211 2.61508 3.55881 1.54838Z\"\n              fill={activeLetters[0] ? \"#ffffff\" : \"#c8c8c8\"}\n              onClick={() => handleLetterClick('h', 0)}\n              className=\"cursor-pointer transition-all duration-300\"\n              style={{\n                filter: activeLetters[0] ? 'url(#glow)' : 'none',\n                transformOrigin: 'center'\n              }}\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n            />\n            \n            {/* Letter O */}\n            <motion.path\n              id=\"o\"\n              d=\"M137.425 21.4154C139.025 21.2821 142.225 22.6156 144.759 24.3486C147.159 25.9496 151.025 29.9486 153.292 33.1486C156.759 38.2156 157.292 39.8156 157.158 47.4156C156.358 81.1486 126.091 101.816 107.691 80.8826C100.492 72.6156 98.8922 56.6156 104.092 44.0826C106.758 37.6826 114.891 28.0826 119.158 26.3486C124.491 24.0826 134.358 21.4155 137.425 21.4154ZM139.825 32.7496C131.959 30.0826 129.025 30.0826 123.958 32.7496C114.092 37.8166 108.625 47.2826 108.625 59.0156C108.625 67.9496 111.025 74.7496 115.425 77.8156C119.158 80.3496 129.292 80.6156 133.559 78.2156C139.292 75.0146 145.825 65.6826 147.958 57.8156C150.491 48.2156 150.492 45.0156 148.092 39.4156C146.625 35.9486 144.892 34.4826 139.825 32.7496Z\"\n              fill={activeLetters[1] ? \"#ffffff\" : \"#c8c8c8\"}\n              onClick={() => handleLetterClick('o', 1)}\n              className=\"cursor-pointer transition-all duration-300\"\n              style={{\n                filter: activeLetters[1] ? 'url(#glow)' : 'none',\n                transformOrigin: 'center'\n              }}\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n            />\n            \n            {/* Letter W */}\n            <motion.path\n              id=\"w\"\n              d=\"M219.959 33.2824C222.892 24.3494 225.426 21.9491 228.226 25.5494C229.026 26.4824 229.959 31.8154 230.359 37.2824C231.026 47.6824 236.093 64.7494 238.76 65.8154C241.159 66.7484 242.36 65.1484 248.359 52.7494C256.626 35.9494 260.226 30.6154 263.293 30.6154C264.759 30.6154 265.959 31.4154 265.959 32.4814C265.959 35.6814 260.76 46.8824 255.293 55.2824C252.493 59.6824 248.626 67.2814 246.626 72.2154C243.026 81.1484 239.159 86.2154 236.893 84.7494C236.226 84.3494 234.626 81.1484 233.426 77.6814C232.092 74.2154 230.226 69.1484 229.293 66.6154C228.226 64.0824 226.759 59.4154 225.959 56.2154C225.159 52.8824 223.693 50.6154 222.626 50.6154C221.426 50.6154 217.426 57.2824 212.76 67.0154C202.76 87.5494 199.293 90.8814 197.56 81.6814C196.226 75.1484 187.293 51.5484 182.626 42.2154C180.093 37.1484 177.959 31.5494 177.959 29.8154C177.959 26.8824 178.493 26.4824 181.56 26.8824C185.693 27.4154 187.959 31.2824 194.492 49.6814C200.092 65.1474 201.693 68.2154 203.426 67.2824C205.292 65.9494 217.292 41.6824 219.959 33.2824Z\"\n              fill={activeLetters[2] ? \"#ffffff\" : \"#c8c8c8\"}\n              onClick={() => handleLetterClick('w', 2)}\n              className=\"cursor-pointer transition-all duration-300\"\n              style={{\n                filter: activeLetters[2] ? 'url(#glow)' : 'none',\n                transformOrigin: 'center'\n              }}\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n            />\n            \n            {/* Letter R */}\n            <motion.path\n              id=\"r\"\n              d=\"M34.7586 144.216C36.8916 140.883 40.8916 140.35 43.1576 143.149C43.9576 144.216 45.2906 148.483 45.9576 152.616C47.9576 165.283 52.4916 168.483 61.0246 163.55C64.0916 161.683 67.6916 159.016 69.1576 157.416C74.4916 151.549 87.9576 151.016 92.6246 156.616C96.6246 161.416 91.2906 165.949 76.2246 170.616C66.0906 173.816 56.8916 180.216 55.8246 185.016C55.4246 187.017 53.8246 191.549 52.3576 195.149C49.5576 201.949 49.5576 211.683 52.0916 231.416C53.1576 239.949 50.0916 247.016 45.5586 246.349C43.5586 246.083 41.9586 243.949 39.8246 238.216C36.7586 230.482 36.3576 223.416 36.6246 186.349C36.6246 180.083 36.3576 174.084 35.9576 173.016C35.5576 172.083 34.7576 165.816 34.0916 159.283C33.0246 149.55 33.1586 146.749 34.7586 144.216Z\"\n              fill={activeLetters[3] ? \"#ffffff\" : \"#c8c8c8\"}\n              onClick={() => handleLetterClick('r', 3)}\n              className=\"cursor-pointer transition-all duration-300\"\n              style={{\n                filter: activeLetters[3] ? 'url(#glow)' : 'none',\n                transformOrigin: 'center'\n              }}\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n            />\n            \n            {/* Letter U */}\n            <motion.path\n              id=\"u\"\n              d=\"M148.892 228.881C148.092 209.814 152.092 200.481 159.691 204.214C161.825 205.28 162.092 207.281 161.825 221.414C161.692 236.214 161.958 237.947 165.158 244.614C172.758 260.081 184.759 263.414 190.759 251.548C191.959 249.148 194.358 245.814 195.958 243.947C200.091 239.414 201.958 231.947 201.958 219.814C201.958 211.681 202.491 209.28 204.491 207.414C207.558 204.614 210.092 205.548 213.158 210.748C215.158 214.214 215.425 216.481 214.625 224.748C213.825 233.281 212.625 237.014 205.825 250.347C198.892 263.947 197.158 266.481 191.425 270.347C185.158 274.614 184.891 274.614 177.958 273.147C172.092 271.947 169.558 270.348 163.958 264.748C153.558 254.348 149.558 244.881 148.892 228.881Z\"\n              fill={activeLetters[4] ? \"#ffffff\" : \"#c8c8c8\"}\n              onClick={() => handleLetterClick('u', 4)}\n              className=\"cursor-pointer transition-all duration-300\"\n              style={{\n                filter: activeLetters[4] ? 'url(#glow)' : 'none',\n                transformOrigin: 'center'\n              }}\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n            />\n          </g>\n        </motion.svg>\n      </motion.div>\n      \n      {/* Particules flottantes autour du logo */}\n      {[...Array(12)].map((_, i) => (\n        <motion.div\n          key={i}\n          className=\"absolute w-2 h-2 bg-white rounded-full opacity-20\"\n          style={{\n            left: `${50 + Math.cos(i * 30 * Math.PI / 180) * 200}px`,\n            top: `${50 + Math.sin(i * 30 * Math.PI / 180) * 200}px`,\n          }}\n          animate={{\n            scale: [0.5, 1.5, 0.5],\n            opacity: [0.1, 0.6, 0.1],\n            rotate: 360,\n          }}\n          transition={{\n            duration: 4 + i * 0.5,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: i * 0.2,\n          }}\n        />\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAUO,SAAS,gBAAgB,EAAE,aAAa,EAAwB;IACrE,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAAC;QAAO;QAAO;QAAO;QAAO;KAAM;IACjG,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAC9B,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAE9B,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;QAAC,CAAC;QAAK;KAAI,EAAE;QAAC;QAAI,CAAC;KAAG;IAC3D,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;QAAC,CAAC;QAAK;KAAI,EAAE;QAAC,CAAC;QAAI;KAAG;IAE3D,MAAM,UAAU;QAAC;QAAK;QAAK;QAAK;QAAK;KAAI;IACzC,MAAM,iBAAiB;QAAC;QAAG;QAAG;QAAG;QAAG;KAAE;IAEtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sCAAsC;QACtC,IAAI,OAAO,OAAO,EAAE;YAClB,MAAM,QAAQ,OAAO,OAAO,CAAC,gBAAgB,CAAC;YAE9C,6IAAA,CAAA,OAAI,CAAC,GAAG,CAAC,OAAO;gBACd,OAAO;gBACP,UAAU;gBACV,iBAAiB;gBACjB,SAAS;YACX;YAEA,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO;gBACb,OAAO;gBACP,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,SAAS;gBACT,OAAO;YACT;YAEA,oCAAoC;YACpC,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO,OAAO,EAAE;gBACtB,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,MAAM;gBACN,QAAQ,CAAC;YACX;QACF;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,EAAE,aAAa,CAAC,qBAAqB;QAClD,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;QACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;QAEzC,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG;QACvB,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG;IACzB;IAEA,MAAM,oBAAoB,CAAC,QAAgB;QACzC,MAAM,cAAc;eAAI;YAAe;SAAM;QAC7C,iBAAiB;QAEjB,8BAA8B;QAC9B,IAAI,OAAO,OAAO,EAAE;YAClB,MAAM,OAAO,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,QAAQ;YACtD,IAAI,MAAM;gBACR,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;oBACZ,OAAO;oBACP,UAAU;oBACV,UAAU;oBACV,MAAM;oBACN,MAAM;oBACN,QAAQ;oBACR,iBAAiB;gBACnB;gBAEA,sBAAsB;gBACtB,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,MAAM;oBACZ,QAAQ;oBACR,UAAU;oBACV,MAAM;oBACN,QAAQ;gBACV;YACF;QACF;QAEA,oBAAoB;QACpB,MAAM,mBAAmB;eAAI;SAAc;QAC3C,gBAAgB,CAAC,MAAM,GAAG;QAC1B,iBAAiB;QAEjB,WAAW;YACT,MAAM,eAAe;mBAAI;aAAc;YACvC,YAAY,CAAC,MAAM,GAAG;YACtB,iBAAiB;QACnB,GAAG;QAEH,4BAA4B;QAC5B,IAAI,YAAY,MAAM,KAAK,eAAe,MAAM,EAAE;YAChD,MAAM,YAAY,YAAY,KAAK,CAAC,CAAC,KAAK,IAAM,QAAQ,cAAc,CAAC,EAAE;YACzE,IAAI,WAAW;gBACb,6BAA6B;gBAC7B,IAAI,OAAO,OAAO,EAAE;oBAClB,MAAM,QAAQ,OAAO,OAAO,CAAC,gBAAgB,CAAC;oBAC9C,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO;wBACb,OAAO;wBACP,UAAU;wBACV,UAAU;wBACV,MAAM;wBACN,SAAS;wBACT,MAAM;wBACN,QAAQ;wBACR,iBAAiB;oBACnB;oBAEA,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO,OAAO,EAAE;wBACtB,QAAQ;wBACR,UAAU;wBACV,MAAM;oBACR;gBACF;gBACA,gBAAgB,UAAU,CAAC;YAC7B;YACA,WAAW,IAAM,iBAAiB,EAAE,GAAG;QACzC;QAEA,gBAAgB,QAAQ;IAC1B;IAEA,MAAM,cAAc;QAClB,aAAa;QACb,IAAI,OAAO,OAAO,EAAE;YAClB,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO,OAAO,CAAC,gBAAgB,CAAC,SAAS;gBAC/C,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,SAAS;gBACT,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,cAAc;QAClB,aAAa;QACb,IAAI,OAAO,OAAO,EAAE;YAClB,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,OAAO,OAAO,CAAC,gBAAgB,CAAC,SAAS;gBAC/C,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,SAAS;gBACT,iBAAiB;YACnB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,YAAY;wBACV;wBACA;wBACA;wBACA;qBACD;gBACH;gBACA,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAY;;;;;;0BAGjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL;oBACA;oBACA,gBAAgB;gBAClB;gBACA,aAAa;gBACb,cAAc;gBACd,cAAc;gBACd,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,YAAY;oBAAE,MAAM;oBAAU,WAAW;oBAAK,SAAS;gBAAG;0BAE1D,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;;sCAG7C,8OAAC;;8CACC,8OAAC;oCAAO,IAAG;;sDACT,8OAAC;4CAAe,cAAa;4CAAI,QAAO;;;;;;sDACxC,8OAAC;;8DACC,8OAAC;oDAAY,IAAG;;;;;;8DAChB,8OAAC;oDAAY,IAAG;;;;;;;;;;;;;;;;;;8CAGpB,8OAAC;oCAAO,IAAG;;sDACT,8OAAC;4CAAa,eAAc;4CAAO,YAAW;4CAAI,QAAO;;;;;;sDACzD,8OAAC;4CAAkB,IAAG;4CAAgB,KAAI;4CAAQ,OAAM;;;;;;;;;;;;;;;;;;sCAI5D,8OAAC;4BAAE,IAAG;;8CAEJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,YAAY;oCACrC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,QAAQ,aAAa,CAAC,EAAE,GAAG,eAAe;wCAC1C,iBAAiB;oCACnB;oCACA,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;;;;;;8CAI1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,YAAY;oCACrC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,QAAQ,aAAa,CAAC,EAAE,GAAG,eAAe;wCAC1C,iBAAiB;oCACnB;oCACA,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;;;;;;8CAI1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,YAAY;oCACrC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,QAAQ,aAAa,CAAC,EAAE,GAAG,eAAe;wCAC1C,iBAAiB;oCACnB;oCACA,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;;;;;;8CAI1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,YAAY;oCACrC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,QAAQ,aAAa,CAAC,EAAE,GAAG,eAAe;wCAC1C,iBAAiB;oCACnB;oCACA,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;;;;;;8CAI1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,IAAG;oCACH,GAAE;oCACF,MAAM,aAAa,CAAC,EAAE,GAAG,YAAY;oCACrC,SAAS,IAAM,kBAAkB,KAAK;oCACtC,WAAU;oCACV,OAAO;wCACL,QAAQ,aAAa,CAAC,EAAE,GAAG,eAAe;wCAC1C,iBAAiB;oCACnB;oCACA,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;;;;;;;;;;;;;;;;;;;;;;;YAO/B;mBAAI,MAAM;aAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,MAAM,GAAG,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;wBACxD,KAAK,GAAG,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;oBACzD;oBACA,SAAS;wBACP,OAAO;4BAAC;4BAAK;4BAAK;yBAAI;wBACtB,SAAS;4BAAC;4BAAK;4BAAK;yBAAI;wBACxB,QAAQ;oBACV;oBACA,YAAY;wBACV,UAAU,IAAI,IAAI;wBAClB,QAAQ;wBACR,MAAM;wBACN,OAAO,IAAI;oBACb;mBAhBK;;;;;;;;;;;AAqBf", "debugId": null}}, {"offset": {"line": 885, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/Logo3D.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useState, useEffect } from 'react';\nimport { <PERSON><PERSON>, useFrame, useThree } from '@react-three/fiber';\nimport { Text3D, Center, Float, MeshDistortMaterial, Sphere, Environment, Effects } from '@react-three/drei';\nimport { motion } from 'framer-motion';\nimport * as THREE from 'three';\n\ninterface Letter3DProps {\n  letter: string;\n  position: [number, number, number];\n  index: number;\n  isActive: boolean;\n  onClick: () => void;\n}\n\nfunction Letter3D({ letter, position, index, isActive, onClick }: Letter3DProps) {\n  const meshRef = useRef<THREE.Mesh>(null);\n  const [hovered, setHovered] = useState(false);\n  \n  useFrame((state) => {\n    if (meshRef.current) {\n      // Animation de base flottante\n      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + index) * 0.3;\n      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.5 + index) * 0.1;\n      meshRef.current.rotation.z = Math.cos(state.clock.elapsedTime * 0.3 + index) * 0.05;\n      \n      // Animation quand actif\n      if (isActive) {\n        meshRef.current.rotation.y += 0.02;\n        meshRef.current.scale.setScalar(1.2 + Math.sin(state.clock.elapsedTime * 3) * 0.1);\n      } else {\n        meshRef.current.scale.setScalar(hovered ? 1.1 : 1);\n      }\n    }\n  });\n\n  return (\n    <Float\n      speed={2}\n      rotationIntensity={0.5}\n      floatIntensity={0.5}\n    >\n      <Text3D\n        ref={meshRef}\n        font=\"/fonts/helvetiker_regular.typeface.json\"\n        size={1.5}\n        height={0.3}\n        position={position}\n        onPointerOver={() => setHovered(true)}\n        onPointerOut={() => setHovered(false)}\n        onClick={onClick}\n        curveSegments={12}\n      >\n        {letter}\n        <MeshDistortMaterial\n          color={isActive ? \"#ffffff\" : hovered ? \"#e0e0e0\" : \"#a0a0a0\"}\n          distort={isActive ? 0.4 : hovered ? 0.2 : 0.1}\n          speed={isActive ? 3 : 1}\n          roughness={0.1}\n          metalness={0.8}\n        />\n      </Text3D>\n    </Float>\n  );\n}\n\nfunction ParticleField() {\n  const pointsRef = useRef<THREE.Points>(null);\n  const particleCount = 200;\n  \n  const positions = new Float32Array(particleCount * 3);\n  for (let i = 0; i < particleCount; i++) {\n    positions[i * 3] = (Math.random() - 0.5) * 20;\n    positions[i * 3 + 1] = (Math.random() - 0.5) * 20;\n    positions[i * 3 + 2] = (Math.random() - 0.5) * 20;\n  }\n\n  useFrame((state) => {\n    if (pointsRef.current) {\n      pointsRef.current.rotation.y = state.clock.elapsedTime * 0.05;\n      pointsRef.current.rotation.x = state.clock.elapsedTime * 0.02;\n    }\n  });\n\n  return (\n    <points ref={pointsRef}>\n      <bufferGeometry>\n        <bufferAttribute\n          attach=\"attributes-position\"\n          count={particleCount}\n          array={positions}\n          itemSize={3}\n        />\n      </bufferGeometry>\n      <pointsMaterial\n        size={0.02}\n        color=\"#ffffff\"\n        transparent\n        opacity={0.6}\n        sizeAttenuation\n      />\n    </points>\n  );\n}\n\nfunction BackgroundSphere() {\n  const sphereRef = useRef<THREE.Mesh>(null);\n  \n  useFrame((state) => {\n    if (sphereRef.current) {\n      sphereRef.current.rotation.y = state.clock.elapsedTime * 0.01;\n      sphereRef.current.rotation.x = state.clock.elapsedTime * 0.005;\n    }\n  });\n\n  return (\n    <Sphere ref={sphereRef} args={[15, 32, 32]} position={[0, 0, -10]}>\n      <MeshDistortMaterial\n        color=\"#111111\"\n        distort={0.3}\n        speed={1}\n        roughness={0.8}\n        metalness={0.2}\n        transparent\n        opacity={0.3}\n      />\n    </Sphere>\n  );\n}\n\nfunction CameraController() {\n  const { camera } = useThree();\n  \n  useFrame((state) => {\n    camera.position.x = Math.sin(state.clock.elapsedTime * 0.1) * 2;\n    camera.position.y = Math.cos(state.clock.elapsedTime * 0.15) * 1;\n    camera.lookAt(0, 0, 0);\n  });\n  \n  return null;\n}\n\ninterface Logo3DProps {\n  onLetterClick?: (letter: string, index: number) => void;\n}\n\nexport function Logo3D({ onLetterClick }: Logo3DProps) {\n  const [activeLetters, setActiveLetters] = useState<boolean[]>([false, false, false, false, false]);\n  const [clickSequence, setClickSequence] = useState<number[]>([]);\n  \n  const letters = ['h', 'o', 'w', 'r', 'u'];\n  const positions: [number, number, number][] = [\n    [-3, 0, 0],\n    [-1.5, 0, 0],\n    [0, 0, 0],\n    [1.5, 0, 0],\n    [3, 0, 0]\n  ];\n  \n  const secretSequence = [0, 4, 1, 3, 2]; // h-u-o-r-w\n\n  const handleLetterClick = (letter: string, index: number) => {\n    const newSequence = [...clickSequence, index];\n    setClickSequence(newSequence);\n\n    // Activer la lettre avec animation\n    const newActiveLetters = [...activeLetters];\n    newActiveLetters[index] = true;\n    setActiveLetters(newActiveLetters);\n\n    // Reset après 3 secondes\n    setTimeout(() => {\n      const resetLetters = [...activeLetters];\n      resetLetters[index] = false;\n      setActiveLetters(resetLetters);\n    }, 3000);\n\n    // Vérifier la séquence secrète\n    if (newSequence.length === secretSequence.length) {\n      const isCorrect = newSequence.every((val, i) => val === secretSequence[i]);\n      if (isCorrect) {\n        onLetterClick?.('secret', -1);\n        // Animation spéciale pour toutes les lettres\n        setActiveLetters([true, true, true, true, true]);\n        setTimeout(() => setActiveLetters([false, false, false, false, false]), 5000);\n      }\n      setTimeout(() => setClickSequence([]), 1000);\n    }\n\n    onLetterClick?.(letter, index);\n  };\n\n  return (\n    <div className=\"w-full h-[600px] relative\">\n      <motion.div\n        initial={{ opacity: 0, scale: 0.8 }}\n        animate={{ opacity: 1, scale: 1 }}\n        transition={{ duration: 2, ease: \"easeOut\" }}\n        className=\"w-full h-full\"\n      >\n        <Canvas\n          camera={{ position: [0, 0, 8], fov: 50 }}\n          gl={{ antialias: true, alpha: true }}\n          style={{ background: 'transparent' }}\n        >\n          <ambientLight intensity={0.3} />\n          <pointLight position={[10, 10, 10]} intensity={1} />\n          <pointLight position={[-10, -10, -10]} intensity={0.5} color=\"#4a5568\" />\n          \n          <Environment preset=\"night\" />\n          \n          <BackgroundSphere />\n          <ParticleField />\n          \n          <Center>\n            {letters.map((letter, index) => (\n              <Letter3D\n                key={letter + index}\n                letter={letter}\n                position={positions[index]}\n                index={index}\n                isActive={activeLetters[index]}\n                onClick={() => handleLetterClick(letter, index)}\n              />\n            ))}\n          </Center>\n          \n          <CameraController />\n        </Canvas>\n      </motion.div>\n      \n      {/* Instructions flottantes */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 2, duration: 1 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center\"\n      >\n        <p className=\"text-whisper-400 text-sm font-light mb-2\">\n          cliquez sur les lettres pour les animer\n        </p>\n        <p className=\"text-whisper-600 text-xs font-light\">\n          trouvez la séquence secrète...\n        </p>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAgBA,SAAS,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAiB;IAC7E,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,QAAQ,OAAO,EAAE;YACnB,8BAA8B;YAC9B,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,SAAS;YACvF,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,MAAM,SAAS;YAC/E,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,MAAM,SAAS;YAE/E,wBAAwB;YACxB,IAAI,UAAU;gBACZ,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI;gBAC9B,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;YAChF,OAAO;gBACL,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,MAAM;YAClD;QACF;IACF;IAEA,qBACE,8OAAC,yJAAA,CAAA,QAAK;QACJ,OAAO;QACP,mBAAmB;QACnB,gBAAgB;kBAEhB,cAAA,8OAAC,0JAAA,CAAA,SAAM;YACL,KAAK;YACL,MAAK;YACL,MAAM;YACN,QAAQ;YACR,UAAU;YACV,eAAe,IAAM,WAAW;YAChC,cAAc,IAAM,WAAW;YAC/B,SAAS;YACT,eAAe;;gBAEd;8BACD,8OAAC,uKAAA,CAAA,sBAAmB;oBAClB,OAAO,WAAW,YAAY,UAAU,YAAY;oBACpD,SAAS,WAAW,MAAM,UAAU,MAAM;oBAC1C,OAAO,WAAW,IAAI;oBACtB,WAAW;oBACX,WAAW;;;;;;;;;;;;;;;;;AAKrB;AAEA,SAAS;IACP,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgB;IACvC,MAAM,gBAAgB;IAEtB,MAAM,YAAY,IAAI,aAAa,gBAAgB;IACnD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;QACtC,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAC3C,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAC/C,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;IACjD;IAEA,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACzD,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAC3D;IACF;IAEA,qBACE,8OAAC;QAAO,KAAK;;0BACX,8OAAC;0BACC,cAAA,8OAAC;oBACC,QAAO;oBACP,OAAO;oBACP,OAAO;oBACP,UAAU;;;;;;;;;;;0BAGd,8OAAC;gBACC,MAAM;gBACN,OAAM;gBACN,WAAW;gBACX,SAAS;gBACT,eAAe;;;;;;;;;;;;AAIvB;AAEA,SAAS;IACP,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAErC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACzD,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAC3D;IACF;IAEA,qBACE,8OAAC,0JAAA,CAAA,SAAM;QAAC,KAAK;QAAW,MAAM;YAAC;YAAI;YAAI;SAAG;QAAE,UAAU;YAAC;YAAG;YAAG,CAAC;SAAG;kBAC/D,cAAA,8OAAC,uKAAA,CAAA,sBAAmB;YAClB,OAAM;YACN,SAAS;YACT,OAAO;YACP,WAAW;YACX,WAAW;YACX,WAAW;YACX,SAAS;;;;;;;;;;;AAIjB;AAEA,SAAS;IACP,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD;IAE1B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,OAAO,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;QAC9D,OAAO,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ;QAC/D,OAAO,MAAM,CAAC,GAAG,GAAG;IACtB;IAEA,OAAO;AACT;AAMO,SAAS,OAAO,EAAE,aAAa,EAAe;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAAC;QAAO;QAAO;QAAO;QAAO;KAAM;IACjG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,UAAU;QAAC;QAAK;QAAK;QAAK;QAAK;KAAI;IACzC,MAAM,YAAwC;QAC5C;YAAC,CAAC;YAAG;YAAG;SAAE;QACV;YAAC,CAAC;YAAK;YAAG;SAAE;QACZ;YAAC;YAAG;YAAG;SAAE;QACT;YAAC;YAAK;YAAG;SAAE;QACX;YAAC;YAAG;YAAG;SAAE;KACV;IAED,MAAM,iBAAiB;QAAC;QAAG;QAAG;QAAG;QAAG;KAAE,EAAE,YAAY;IAEpD,MAAM,oBAAoB,CAAC,QAAgB;QACzC,MAAM,cAAc;eAAI;YAAe;SAAM;QAC7C,iBAAiB;QAEjB,mCAAmC;QACnC,MAAM,mBAAmB;eAAI;SAAc;QAC3C,gBAAgB,CAAC,MAAM,GAAG;QAC1B,iBAAiB;QAEjB,yBAAyB;QACzB,WAAW;YACT,MAAM,eAAe;mBAAI;aAAc;YACvC,YAAY,CAAC,MAAM,GAAG;YACtB,iBAAiB;QACnB,GAAG;QAEH,+BAA+B;QAC/B,IAAI,YAAY,MAAM,KAAK,eAAe,MAAM,EAAE;YAChD,MAAM,YAAY,YAAY,KAAK,CAAC,CAAC,KAAK,IAAM,QAAQ,cAAc,CAAC,EAAE;YACzE,IAAI,WAAW;gBACb,gBAAgB,UAAU,CAAC;gBAC3B,6CAA6C;gBAC7C,iBAAiB;oBAAC;oBAAM;oBAAM;oBAAM;oBAAM;iBAAK;gBAC/C,WAAW,IAAM,iBAAiB;wBAAC;wBAAO;wBAAO;wBAAO;wBAAO;qBAAM,GAAG;YAC1E;YACA,WAAW,IAAM,iBAAiB,EAAE,GAAG;QACzC;QAEA,gBAAgB,QAAQ;IAC1B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAI;gBAClC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,YAAY;oBAAE,UAAU;oBAAG,MAAM;gBAAU;gBAC3C,WAAU;0BAEV,cAAA,8OAAC,mMAAA,CAAA,SAAM;oBACL,QAAQ;wBAAE,UAAU;4BAAC;4BAAG;4BAAG;yBAAE;wBAAE,KAAK;oBAAG;oBACvC,IAAI;wBAAE,WAAW;wBAAM,OAAO;oBAAK;oBACnC,OAAO;wBAAE,YAAY;oBAAc;;sCAEnC,8OAAC;4BAAa,WAAW;;;;;;sCACzB,8OAAC;4BAAW,UAAU;gCAAC;gCAAI;gCAAI;6BAAG;4BAAE,WAAW;;;;;;sCAC/C,8OAAC;4BAAW,UAAU;gCAAC,CAAC;gCAAI,CAAC;gCAAI,CAAC;6BAAG;4BAAE,WAAW;4BAAK,OAAM;;;;;;sCAE7D,8OAAC,+JAAA,CAAA,cAAW;4BAAC,QAAO;;;;;;sCAEpB,8OAAC;;;;;sCACD,8OAAC;;;;;sCAED,8OAAC,0JAAA,CAAA,SAAM;sCACJ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;oCAEC,QAAQ;oCACR,UAAU,SAAS,CAAC,MAAM;oCAC1B,OAAO;oCACP,UAAU,aAAa,CAAC,MAAM;oCAC9B,SAAS,IAAM,kBAAkB,QAAQ;mCALpC,SAAS;;;;;;;;;;sCAUpB,8OAAC;;;;;;;;;;;;;;;;0BAKL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAG,UAAU;gBAAE;gBACpC,WAAU;;kCAEV,8OAAC;wBAAE,WAAU;kCAA2C;;;;;;kCAGxD,8OAAC;wBAAE,WAAU;kCAAsC;;;;;;;;;;;;;;;;;;AAM3D", "debugId": null}}, {"offset": {"line": 1324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/Scene3D.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useRef, useState } from \"react\";\nimport { <PERSON><PERSON>, use<PERSON>rame, useThree } from \"@react-three/fiber\";\nimport {\n  Text3D,\n  Center,\n  Float,\n  MeshDistortMaterial,\n  Sphere,\n  Environment,\n  OrbitControls,\n  Sparkles,\n  Stars,\n  Cloud,\n} from \"@react-three/drei\";\nimport {\n  EffectComposer,\n  Bloom,\n  ChromaticAberration,\n  Glitch,\n} from \"@react-three/postprocessing\";\nimport { BlendFunction } from \"postprocessing\";\nimport * as THREE from \"three\";\n\nfunction FloatingText({\n  text,\n  position,\n  color = \"#ffffff\",\n  isActive = false,\n}: {\n  text: string;\n  position: [number, number, number];\n  color?: string;\n  isActive?: boolean;\n}) {\n  const meshRef = useRef<THREE.Mesh>(null);\n\n  useFrame((state) => {\n    if (meshRef.current) {\n      meshRef.current.position.y =\n        position[1] + Math.sin(state.clock.elapsedTime * 2) * 0.5;\n      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime) * 0.3;\n\n      if (isActive) {\n        meshRef.current.scale.setScalar(\n          1.5 + Math.sin(state.clock.elapsedTime * 5) * 0.2\n        );\n      }\n    }\n  });\n\n  return (\n    <Float speed={3} rotationIntensity={0.5} floatIntensity={2}>\n      <Text3D\n        ref={meshRef}\n        font=\"/fonts/helvetiker_regular.typeface.json\"\n        size={3} // Plus grand pour une meilleure visibilité\n        height={1.5} // BEAUCOUP plus épais pour l'effet volumineux\n        position={position}\n        curveSegments={24} // Plus de segments pour des courbes plus lisses\n        bevelEnabled={true}\n        bevelSize={0.1} // Bords arrondis\n        bevelThickness={0.08}\n        bevelSegments={16}\n      >\n        {text}\n        <MeshDistortMaterial\n          color={color}\n          distort={isActive ? 0.3 : 0.1} // Moins de distorsion pour préserver l'épaisseur\n          speed={isActive ? 5 : 2}\n          roughness={isActive ? 0.05 : 0.1} // Plus lisse pour des reflets nets\n          metalness={isActive ? 0.95 : 0.85} // Plus métallique\n          clearcoat={1.0} // Vernis brillant pour accentuer l'épaisseur\n          clearcoatRoughness={0.05}\n        />\n      </Text3D>\n    </Float>\n  );\n}\n\nfunction DreamSphere() {\n  const sphereRef = useRef<THREE.Mesh>(null);\n\n  useFrame((state) => {\n    if (sphereRef.current) {\n      sphereRef.current.rotation.x = state.clock.elapsedTime * 0.1;\n      sphereRef.current.rotation.y = state.clock.elapsedTime * 0.15;\n      sphereRef.current.position.y = Math.sin(state.clock.elapsedTime) * 2;\n    }\n  });\n\n  return (\n    <Sphere ref={sphereRef} args={[3, 64, 64]} position={[0, 0, -8]}>\n      <MeshDistortMaterial\n        color=\"#4a5568\"\n        distort={0.4}\n        speed={2}\n        roughness={0.2}\n        metalness={0.9}\n        transparent\n        opacity={0.6}\n      />\n    </Sphere>\n  );\n}\n\nfunction ParticleSystem() {\n  const pointsRef = useRef<THREE.Points>(null);\n  const particleCount = 500;\n\n  const positions = new Float32Array(particleCount * 3);\n  const colors = new Float32Array(particleCount * 3);\n\n  for (let i = 0; i < particleCount; i++) {\n    positions[i * 3] = (Math.random() - 0.5) * 50;\n    positions[i * 3 + 1] = (Math.random() - 0.5) * 50;\n    positions[i * 3 + 2] = (Math.random() - 0.5) * 50;\n\n    colors[i * 3] = Math.random();\n    colors[i * 3 + 1] = Math.random();\n    colors[i * 3 + 2] = Math.random();\n  }\n\n  useFrame((state) => {\n    if (pointsRef.current) {\n      pointsRef.current.rotation.y = state.clock.elapsedTime * 0.05;\n      pointsRef.current.rotation.x = state.clock.elapsedTime * 0.02;\n    }\n  });\n\n  return (\n    <points ref={pointsRef}>\n      <bufferGeometry>\n        <bufferAttribute\n          attach=\"attributes-position\"\n          count={particleCount}\n          array={positions}\n          itemSize={3}\n        />\n        <bufferAttribute\n          attach=\"attributes-color\"\n          count={particleCount}\n          array={colors}\n          itemSize={3}\n        />\n      </bufferGeometry>\n      <pointsMaterial\n        size={0.05}\n        vertexColors\n        transparent\n        opacity={0.8}\n        sizeAttenuation\n      />\n    </points>\n  );\n}\n\nfunction CameraAnimation() {\n  const { camera } = useThree();\n\n  useFrame((state) => {\n    camera.position.x = Math.sin(state.clock.elapsedTime * 0.2) * 5;\n    camera.position.y = Math.cos(state.clock.elapsedTime * 0.15) * 3;\n    camera.position.z = 15 + Math.sin(state.clock.elapsedTime * 0.1) * 5;\n    camera.lookAt(0, 0, 0);\n  });\n\n  return null;\n}\n\ninterface Scene3DProps {\n  activeLetters?: boolean[];\n}\n\nexport function Scene3D({\n  activeLetters = [false, false, false, false, false],\n}: Scene3DProps) {\n  const [glitchActive, setGlitchActive] = useState(false);\n\n  const letters = [\"h\", \"o\", \"w\", \"r\", \"u\"];\n  const positions: [number, number, number][] = [\n    [-6, 0, 0], // h - plus centré\n    [-3, 0, 1], // o - légèrement en avant\n    [0, 0, 0], // w - au centre\n    [3, 0, 1], // r - légèrement en avant\n    [6, 0, 0], // u - plus centré\n  ];\n\n  return (\n    <div className=\"w-full h-screen\">\n      <Canvas\n        camera={{ position: [0, 0, 15], fov: 75 }}\n        gl={{ antialias: true, alpha: true }}\n      >\n        {/* Éclairage dramatique */}\n        <ambientLight intensity={0.2} />\n        <pointLight position={[10, 10, 10]} intensity={1} color=\"#ffffff\" />\n        <pointLight\n          position={[-10, -10, -10]}\n          intensity={0.5}\n          color=\"#4a5568\"\n        />\n        <spotLight\n          position={[0, 20, 0]}\n          angle={0.3}\n          penumbra={1}\n          intensity={1}\n          castShadow\n        />\n\n        {/* Environnement */}\n        <Environment preset=\"night\" />\n        <Stars\n          radius={100}\n          depth={50}\n          count={5000}\n          factor={4}\n          saturation={0}\n          fade\n        />\n\n        {/* Nuages atmosphériques */}\n        <Cloud\n          position={[-20, 10, -20]}\n          speed={0.2}\n          opacity={0.1}\n          width={10}\n          depth={1.5}\n          segments={20}\n        />\n        <Cloud\n          position={[20, -10, -20]}\n          speed={0.3}\n          opacity={0.1}\n          width={15}\n          depth={2}\n          segments={25}\n        />\n\n        {/* Système de particules */}\n        <ParticleSystem />\n        <Sparkles count={100} scale={[20, 20, 20]} size={2} speed={0.5} />\n\n        {/* Sphère de rêve */}\n        <DreamSphere />\n\n        {/* Lettres 3D flottantes */}\n        <Center>\n          {letters.map((letter, index) => (\n            <FloatingText\n              key={letter}\n              text={letter}\n              position={positions[index]}\n              color={activeLetters[index] ? \"#ffffff\" : \"#a0a0a0\"}\n              isActive={activeLetters[index]}\n            />\n          ))}\n        </Center>\n\n        {/* Animation de caméra */}\n        <CameraAnimation />\n\n        {/* Contrôles optionnels */}\n        <OrbitControls\n          enablePan={false}\n          enableZoom={false}\n          enableRotate={true}\n          autoRotate\n          autoRotateSpeed={0.5}\n        />\n\n        {/* Effets de post-processing */}\n        <EffectComposer>\n          <Bloom\n            intensity={1.5}\n            luminanceThreshold={0.2}\n            luminanceSmoothing={0.9}\n            height={300}\n          />\n          <ChromaticAberration\n            blendFunction={BlendFunction.NORMAL}\n            offset={[0.002, 0.002]}\n          />\n          {glitchActive && (\n            <Glitch\n              delay={[1.5, 3.5]}\n              duration={[0.6, 1.0]}\n              strength={[0.3, 1.0]}\n              mode={0}\n            />\n          )}\n        </EffectComposer>\n      </Canvas>\n\n      {/* Contrôles UI */}\n      <div className=\"absolute bottom-8 left-8 space-y-2\">\n        <button\n          onClick={() => setGlitchActive(!glitchActive)}\n          className=\"px-4 py-2 bg-ghost-100 border border-whisper-200 text-whisper-300 text-sm font-light hover:bg-ghost-200 transition-colors\"\n        >\n          {glitchActive ? \"Désactiver Glitch\" : \"Activer Glitch\"}\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAMA;AAtBA;;;;;;;AAyBA,SAAS,aAAa,EACpB,IAAI,EACJ,QAAQ,EACR,QAAQ,SAAS,EACjB,WAAW,KAAK,EAMjB;IACC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAEnC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,QAAQ,OAAO,EAAE;YACnB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GACxB,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;YACxD,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,IAAI;YAEjE,IAAI,UAAU;gBACZ,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAC7B,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;YAElD;QACF;IACF;IAEA,qBACE,8OAAC,yJAAA,CAAA,QAAK;QAAC,OAAO;QAAG,mBAAmB;QAAK,gBAAgB;kBACvD,cAAA,8OAAC,0JAAA,CAAA,SAAM;YACL,KAAK;YACL,MAAK;YACL,MAAM;YACN,QAAQ;YACR,UAAU;YACV,eAAe;YACf,cAAc;YACd,WAAW;YACX,gBAAgB;YAChB,eAAe;;gBAEd;8BACD,8OAAC,uKAAA,CAAA,sBAAmB;oBAClB,OAAO;oBACP,SAAS,WAAW,MAAM;oBAC1B,OAAO,WAAW,IAAI;oBACtB,WAAW,WAAW,OAAO;oBAC7B,WAAW,WAAW,OAAO;oBAC7B,WAAW;oBACX,oBAAoB;;;;;;;;;;;;;;;;;AAK9B;AAEA,SAAS;IACP,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAErC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACzD,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACzD,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,IAAI;QACrE;IACF;IAEA,qBACE,8OAAC,0JAAA,CAAA,SAAM;QAAC,KAAK;QAAW,MAAM;YAAC;YAAG;YAAI;SAAG;QAAE,UAAU;YAAC;YAAG;YAAG,CAAC;SAAE;kBAC7D,cAAA,8OAAC,uKAAA,CAAA,sBAAmB;YAClB,OAAM;YACN,SAAS;YACT,OAAO;YACP,WAAW;YACX,WAAW;YACX,WAAW;YACX,SAAS;;;;;;;;;;;AAIjB;AAEA,SAAS;IACP,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgB;IACvC,MAAM,gBAAgB;IAEtB,MAAM,YAAY,IAAI,aAAa,gBAAgB;IACnD,MAAM,SAAS,IAAI,aAAa,gBAAgB;IAEhD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;QACtC,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAC3C,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAC/C,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAE/C,MAAM,CAAC,IAAI,EAAE,GAAG,KAAK,MAAM;QAC3B,MAAM,CAAC,IAAI,IAAI,EAAE,GAAG,KAAK,MAAM;QAC/B,MAAM,CAAC,IAAI,IAAI,EAAE,GAAG,KAAK,MAAM;IACjC;IAEA,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACzD,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAC3D;IACF;IAEA,qBACE,8OAAC;QAAO,KAAK;;0BACX,8OAAC;;kCACC,8OAAC;wBACC,QAAO;wBACP,OAAO;wBACP,OAAO;wBACP,UAAU;;;;;;kCAEZ,8OAAC;wBACC,QAAO;wBACP,OAAO;wBACP,OAAO;wBACP,UAAU;;;;;;;;;;;;0BAGd,8OAAC;gBACC,MAAM;gBACN,YAAY;gBACZ,WAAW;gBACX,SAAS;gBACT,eAAe;;;;;;;;;;;;AAIvB;AAEA,SAAS;IACP,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD;IAE1B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,OAAO,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;QAC9D,OAAO,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ;QAC/D,OAAO,QAAQ,CAAC,CAAC,GAAG,KAAK,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;QACnE,OAAO,MAAM,CAAC,GAAG,GAAG;IACtB;IAEA,OAAO;AACT;AAMO,SAAS,QAAQ,EACtB,gBAAgB;IAAC;IAAO;IAAO;IAAO;IAAO;CAAM,EACtC;IACb,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,UAAU;QAAC;QAAK;QAAK;QAAK;QAAK;KAAI;IACzC,MAAM,YAAwC;QAC5C;YAAC,CAAC;YAAG;YAAG;SAAE;QACV;YAAC,CAAC;YAAG;YAAG;SAAE;QACV;YAAC;YAAG;YAAG;SAAE;QACT;YAAC;YAAG;YAAG;SAAE;QACT;YAAC;YAAG;YAAG;SAAE;KACV;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mMAAA,CAAA,SAAM;gBACL,QAAQ;oBAAE,UAAU;wBAAC;wBAAG;wBAAG;qBAAG;oBAAE,KAAK;gBAAG;gBACxC,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;;kCAGnC,8OAAC;wBAAa,WAAW;;;;;;kCACzB,8OAAC;wBAAW,UAAU;4BAAC;4BAAI;4BAAI;yBAAG;wBAAE,WAAW;wBAAG,OAAM;;;;;;kCACxD,8OAAC;wBACC,UAAU;4BAAC,CAAC;4BAAI,CAAC;4BAAI,CAAC;yBAAG;wBACzB,WAAW;wBACX,OAAM;;;;;;kCAER,8OAAC;wBACC,UAAU;4BAAC;4BAAG;4BAAI;yBAAE;wBACpB,OAAO;wBACP,UAAU;wBACV,WAAW;wBACX,UAAU;;;;;;kCAIZ,8OAAC,+JAAA,CAAA,cAAW;wBAAC,QAAO;;;;;;kCACpB,8OAAC,yJAAA,CAAA,QAAK;wBACJ,QAAQ;wBACR,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,YAAY;wBACZ,IAAI;;;;;;kCAIN,8OAAC,yJAAA,CAAA,QAAK;wBACJ,UAAU;4BAAC,CAAC;4BAAI;4BAAI,CAAC;yBAAG;wBACxB,OAAO;wBACP,SAAS;wBACT,OAAO;wBACP,OAAO;wBACP,UAAU;;;;;;kCAEZ,8OAAC,yJAAA,CAAA,QAAK;wBACJ,UAAU;4BAAC;4BAAI,CAAC;4BAAI,CAAC;yBAAG;wBACxB,OAAO;wBACP,SAAS;wBACT,OAAO;wBACP,OAAO;wBACP,UAAU;;;;;;kCAIZ,8OAAC;;;;;kCACD,8OAAC,4JAAA,CAAA,WAAQ;wBAAC,OAAO;wBAAK,OAAO;4BAAC;4BAAI;4BAAI;yBAAG;wBAAE,MAAM;wBAAG,OAAO;;;;;;kCAG3D,8OAAC;;;;;kCAGD,8OAAC,0JAAA,CAAA,SAAM;kCACJ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;gCAEC,MAAM;gCACN,UAAU,SAAS,CAAC,MAAM;gCAC1B,OAAO,aAAa,CAAC,MAAM,GAAG,YAAY;gCAC1C,UAAU,aAAa,CAAC,MAAM;+BAJzB;;;;;;;;;;kCAUX,8OAAC;;;;;kCAGD,8OAAC,iKAAA,CAAA,gBAAa;wBACZ,WAAW;wBACX,YAAY;wBACZ,cAAc;wBACd,UAAU;wBACV,iBAAiB;;;;;;kCAInB,8OAAC,mKAAA,CAAA,iBAAc;;0CACb,8OAAC,mKAAA,CAAA,QAAK;gCACJ,WAAW;gCACX,oBAAoB;gCACpB,oBAAoB;gCACpB,QAAQ;;;;;;0CAEV,8OAAC,mKAAA,CAAA,sBAAmB;gCAClB,eAAe,gJAAA,CAAA,gBAAa,CAAC,MAAM;gCACnC,QAAQ;oCAAC;oCAAO;iCAAM;;;;;;4BAEvB,8BACC,8OAAC,mKAAA,CAAA,SAAM;gCACL,OAAO;oCAAC;oCAAK;iCAAI;gCACjB,UAAU;oCAAC;oCAAK;iCAAI;gCACpB,UAAU;oCAAC;oCAAK;iCAAI;gCACpB,MAAM;;;;;;;;;;;;;;;;;;0BAOd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS,IAAM,gBAAgB,CAAC;oBAChC,WAAU;8BAET,eAAe,sBAAsB;;;;;;;;;;;;;;;;;AAKhD", "debugId": null}}, {"offset": {"line": 1819, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/SVG3DLogo.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useRef, useState, useEffect } from \"react\";\nimport { Canvas, useFrame, extend } from \"@react-three/fiber\";\nimport {\n  Center,\n  Float,\n  MeshDistortMaterial,\n  Environment,\n  OrbitControls,\n} from \"@react-three/drei\";\nimport { SVGLoader } from \"three-stdlib\";\nimport * as THREE from \"three\";\nimport { motion } from \"framer-motion\";\n\n// Extend pour utiliser SVGLoader\nextend({ SVGLoader });\n\ninterface Letter3DProps {\n  svgPath: string;\n  position: [number, number, number];\n  index: number;\n  isActive: boolean;\n  isHovered: boolean;\n  onClick: () => void;\n  onHover: (hovered: boolean) => void;\n}\n\nfunction Letter3D({\n  svgPath,\n  position,\n  index,\n  isActive,\n  isHovered,\n  onClick,\n  onHover,\n}: Letter3DProps) {\n  const meshRef = useRef<THREE.Mesh>(null);\n  const [geometry, setGeometry] = useState<THREE.ExtrudeGeometry | null>(null);\n\n  useEffect(() => {\n    // Créer la géométrie 3D à partir du path SVG\n    const loader = new SVGLoader();\n    const svgData = `<svg viewBox=\"0 0 266 275\"><path d=\"${svgPath}\"/></svg>`;\n    const svgResult = loader.parse(svgData);\n\n    if (svgResult.paths.length > 0) {\n      const path = svgResult.paths[0];\n      const shapes = SVGLoader.createShapes(path);\n\n      if (shapes.length > 0) {\n        const extrudeSettings = {\n          depth: 4.5, // ÉNORME épaisseur pour un effet ultra-volumineux\n          bevelEnabled: true,\n          bevelSegments: 48, // Maximum de segments pour des courbes parfaites\n          steps: 10, // Beaucoup plus d'étapes pour une extrusion ultra-douce\n          bevelSize: 0.4, // Bevel très large pour des bords ultra-arrondis\n          bevelThickness: 0.35, // Épaisseur maximale du bevel pour l'effet ultra-arrondi\n          curveSegments: 64, // Maximum absolu de segments pour des courbes parfaites\n        };\n\n        const extrudeGeometry = new THREE.ExtrudeGeometry(\n          shapes,\n          extrudeSettings\n        );\n        extrudeGeometry.center();\n        extrudeGeometry.scale(0.02, -0.02, 0.02); // Encore plus grand pour un effet massif\n\n        // Calculer les normales pour un rendu plus lisse\n        extrudeGeometry.computeVertexNormals();\n\n        setGeometry(extrudeGeometry);\n      }\n    }\n  }, [svgPath]);\n\n  useFrame((state) => {\n    if (meshRef.current) {\n      // Animation de base flottante\n      meshRef.current.position.y =\n        position[1] + Math.sin(state.clock.elapsedTime + index) * 0.2;\n      meshRef.current.rotation.x =\n        Math.sin(state.clock.elapsedTime * 0.5 + index) * 0.1;\n      meshRef.current.rotation.z =\n        Math.cos(state.clock.elapsedTime * 0.3 + index) * 0.05;\n\n      // Animation quand actif\n      if (isActive) {\n        meshRef.current.rotation.y += 0.02;\n        meshRef.current.scale.setScalar(\n          1.3 + Math.sin(state.clock.elapsedTime * 4) * 0.1\n        );\n      } else if (isHovered) {\n        meshRef.current.scale.setScalar(1.1);\n      } else {\n        meshRef.current.scale.setScalar(1);\n      }\n    }\n  });\n\n  if (!geometry) return null;\n\n  return (\n    <Float speed={2} rotationIntensity={0.3} floatIntensity={0.4}>\n      <mesh\n        ref={meshRef}\n        geometry={geometry}\n        position={position}\n        onClick={onClick}\n        onPointerOver={() => onHover(true)}\n        onPointerOut={() => onHover(false)}\n      >\n        <MeshDistortMaterial\n          color={isActive ? \"#ffffff\" : isHovered ? \"#f0f0f0\" : \"#d8d8d8\"}\n          distort={isActive ? 0.2 : isHovered ? 0.08 : 0.03} // Moins de distorsion pour préserver les bords arrondis\n          speed={isActive ? 4 : isHovered ? 2 : 1}\n          roughness={isActive ? 0.05 : 0.15} // Plus lisse pour accentuer les reflets sur l'épaisseur\n          metalness={isActive ? 0.95 : 0.8} // Plus métallique pour des reflets plus nets\n          clearcoat={1.0} // Maximum de vernis pour un effet ultra-brillant\n          clearcoatRoughness={0.05} // Très lisse pour des reflets parfaits\n        />\n      </mesh>\n    </Float>\n  );\n}\n\ninterface SVG3DLogoProps {\n  onLetterClick?: (letter: string, index: number) => void;\n  activeLetters?: boolean[];\n  className?: string;\n}\n\nexport function SVG3DLogo({\n  onLetterClick,\n  activeLetters = [false, false, false, false, false],\n  className = \"\",\n}: SVG3DLogoProps) {\n  const [hoveredLetters, setHoveredLetters] = useState<boolean[]>([\n    false,\n    false,\n    false,\n    false,\n    false,\n  ]);\n  const [isMobile, setIsMobile] = useState(false);\n\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n\n    checkMobile();\n    window.addEventListener(\"resize\", checkMobile);\n    return () => window.removeEventListener(\"resize\", checkMobile);\n  }, []);\n\n  const letters = [\n    {\n      letter: \"h\",\n      path: \"M3.55881 1.54838C6.09211 -0.984916 9.69161 -0.318516 11.425 2.88138C12.4912 4.88138 12.7582 11.8147 11.9582 27.4148C10.6249 58.7478 11.1585 60.2148 23.1582 54.3478C34.3582 48.8818 45.6912 51.5478 51.9582 61.1478C56.4912 67.8148 59.5592 83.0148 59.8252 99.4148C59.9592 111.148 59.5582 113.415 57.4252 115.815C55.9582 117.415 53.5582 118.615 52.0922 118.348C49.5582 117.948 49.2912 117.015 49.4252 109.948C49.5582 105.548 49.6922 97.1478 49.8252 91.2818C50.2252 77.6818 47.9582 71.0148 42.0922 67.0148C37.0252 63.5488 27.2912 62.8808 22.4912 65.6808C14.2252 70.3478 11.4255 78.4818 9.29221 103.282C8.89221 108.348 8.09211 113.681 7.82541 114.882C7.02541 117.815 1.29151 118.081 0.224813 115.415C-0.175087 114.346 -0.0413892 99.2798 0.625211 81.8148C1.29191 64.2148 1.82491 39.4148 1.95821 26.4808C1.95821 10.2147 2.49211 2.61508 3.55881 1.54838Z\",\n    },\n    {\n      letter: \"o\",\n      path: \"M137.425 21.4154C139.025 21.2821 142.225 22.6156 144.759 24.3486C147.159 25.9496 151.025 29.9486 153.292 33.1486C156.759 38.2156 157.292 39.8156 157.158 47.4156C156.358 81.1486 126.091 101.816 107.691 80.8826C100.492 72.6156 98.8922 56.6156 104.092 44.0826C106.758 37.6826 114.891 28.0826 119.158 26.3486C124.491 24.0826 134.358 21.4155 137.425 21.4154ZM139.825 32.7496C131.959 30.0826 129.025 30.0826 123.958 32.7496C114.092 37.8166 108.625 47.2826 108.625 59.0156C108.625 67.9496 111.025 74.7496 115.425 77.8156C119.158 80.3496 129.292 80.6156 133.559 78.2156C139.292 75.0146 145.825 65.6826 147.958 57.8156C150.491 48.2156 150.492 45.0156 148.092 39.4156C146.625 35.9486 144.892 34.4826 139.825 32.7496Z\",\n    },\n    {\n      letter: \"w\",\n      path: \"M219.959 33.2824C222.892 24.3494 225.426 21.9491 228.226 25.5494C229.026 26.4824 229.959 31.8154 230.359 37.2824C231.026 47.6824 236.093 64.7494 238.76 65.8154C241.159 66.7484 242.36 65.1484 248.359 52.7494C256.626 35.9494 260.226 30.6154 263.293 30.6154C264.759 30.6154 265.959 31.4154 265.959 32.4814C265.959 35.6814 260.76 46.8824 255.293 55.2824C252.493 59.6824 248.626 67.2814 246.626 72.2154C243.026 81.1484 239.159 86.2154 236.893 84.7494C236.226 84.3494 234.626 81.1484 233.426 77.6814C232.092 74.2154 230.226 69.1484 229.293 66.6154C228.226 64.0824 226.759 59.4154 225.959 56.2154C225.159 52.8824 223.693 50.6154 222.626 50.6154C221.426 50.6154 217.426 57.2824 212.76 67.0154C202.76 87.5494 199.293 90.8814 197.56 81.6814C196.226 75.1484 187.293 51.5484 182.626 42.2154C180.093 37.1484 177.959 31.5494 177.959 29.8154C177.959 26.8824 178.493 26.4824 181.56 26.8824C185.693 27.4154 187.959 31.2824 194.492 49.6814C200.092 65.1474 201.693 68.2154 203.426 67.2824C205.292 65.9494 217.292 41.6824 219.959 33.2824Z\",\n    },\n    {\n      letter: \"r\",\n      path: \"M34.7586 144.216C36.8916 140.883 40.8916 140.35 43.1576 143.149C43.9576 144.216 45.2906 148.483 45.9576 152.616C47.9576 165.283 52.4916 168.483 61.0246 163.55C64.0916 161.683 67.6916 159.016 69.1576 157.416C74.4916 151.549 87.9576 151.016 92.6246 156.616C96.6246 161.416 91.2906 165.949 76.2246 170.616C66.0906 173.816 56.8916 180.216 55.8246 185.016C55.4246 187.017 53.8246 191.549 52.3576 195.149C49.5576 201.949 49.5576 211.683 52.0916 231.416C53.1576 239.949 50.0916 247.016 45.5586 246.349C43.5586 246.083 41.9586 243.949 39.8246 238.216C36.7586 230.482 36.3576 223.416 36.6246 186.349C36.6246 180.083 36.3576 174.084 35.9576 173.016C35.5576 172.083 34.7576 165.816 34.0916 159.283C33.0246 149.55 33.1586 146.749 34.7586 144.216Z\",\n    },\n    {\n      letter: \"u\",\n      path: \"M148.892 228.881C148.092 209.814 152.092 200.481 159.691 204.214C161.825 205.28 162.092 207.281 161.825 221.414C161.692 236.214 161.958 237.947 165.158 244.614C172.758 260.081 184.759 263.414 190.759 251.548C191.959 249.148 194.358 245.814 195.958 243.947C200.091 239.414 201.958 231.947 201.958 219.814C201.958 211.681 202.491 209.28 204.491 207.414C207.558 204.614 210.092 205.548 213.158 210.748C215.158 214.214 215.425 216.481 214.625 224.748C213.825 233.281 212.625 237.014 205.825 250.347C198.892 263.947 197.158 266.481 191.425 270.347C185.158 274.614 184.891 274.614 177.958 273.147C172.092 271.947 169.558 270.348 163.958 264.748C153.558 254.348 149.558 244.881 148.892 228.881Z\",\n    },\n  ];\n\n  const positions: [number, number, number][] = isMobile\n    ? [\n        [-1.2, 1.2, 0],\n        [0, 0.6, 0],\n        [1.2, 0, 0],\n        [-0.6, -0.6, 0],\n        [0.6, -1.2, 0],\n      ] // Mobile: disposition verticale compacte\n    : [\n        [-4.5, 0, 0],\n        [-2.2, 0, 0],\n        [0, 0, 0],\n        [2.2, 0, 0],\n        [4.5, 0, 0],\n      ]; // Desktop: disposition horizontale plus espacée\n\n  const handleLetterClick = (letter: string, index: number) => {\n    onLetterClick?.(letter, index);\n  };\n\n  const handleLetterHover = (index: number, hovered: boolean) => {\n    const newHovered = [...hoveredLetters];\n    newHovered[index] = hovered;\n    setHoveredLetters(newHovered);\n  };\n\n  return (\n    <div className={`w-full h-screen ${className}`}>\n      <Canvas\n        camera={{\n          position: isMobile ? [0, 0, 8] : [0, 0, 15],\n          fov: isMobile ? 70 : 65,\n        }}\n        gl={{ antialias: true, alpha: true }}\n        style={{ width: \"100%\", height: \"100%\" }}\n      >\n        <ambientLight intensity={0.3} />\n        <pointLight position={[10, 10, 10]} intensity={1.2} color=\"#ffffff\" />\n        <pointLight\n          position={[-10, -10, -10]}\n          intensity={0.6}\n          color=\"#8892b0\"\n        />\n        <pointLight position={[0, 15, 5]} intensity={0.8} color=\"#64ffda\" />\n        <spotLight\n          position={[0, 10, 10]}\n          angle={0.3}\n          penumbra={1}\n          intensity={0.5}\n          castShadow\n        />\n\n        <Environment preset=\"night\" />\n\n        <Center>\n          {letters.map((letterData, index) => (\n            <Letter3D\n              key={letterData.letter}\n              svgPath={letterData.path}\n              position={positions[index]}\n              index={index}\n              isActive={activeLetters[index]}\n              isHovered={hoveredLetters[index]}\n              onClick={() => handleLetterClick(letterData.letter, index)}\n              onHover={(hovered) => handleLetterHover(index, hovered)}\n            />\n          ))}\n        </Center>\n\n        <OrbitControls\n          enablePan={false}\n          enableZoom={isMobile ? false : true}\n          enableRotate={true}\n          autoRotate={!isMobile}\n          autoRotateSpeed={0.3}\n          maxDistance={isMobile ? 10 : 18}\n          minDistance={isMobile ? 5 : 8}\n          maxPolarAngle={Math.PI / 1.5}\n          minPolarAngle={Math.PI / 3}\n        />\n      </Canvas>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;AAZA;;;;;;;AAeA,iCAAiC;AACjC,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE;IAAE,WAAA,uJAAA,CAAA,YAAS;AAAC;AAYnB,SAAS,SAAS,EAChB,OAAO,EACP,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,SAAS,EACT,OAAO,EACP,OAAO,EACO;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAEvE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6CAA6C;QAC7C,MAAM,SAAS,IAAI,uJAAA,CAAA,YAAS;QAC5B,MAAM,UAAU,CAAC,oCAAoC,EAAE,QAAQ,SAAS,CAAC;QACzE,MAAM,YAAY,OAAO,KAAK,CAAC;QAE/B,IAAI,UAAU,KAAK,CAAC,MAAM,GAAG,GAAG;YAC9B,MAAM,OAAO,UAAU,KAAK,CAAC,EAAE;YAC/B,MAAM,SAAS,uJAAA,CAAA,YAAS,CAAC,YAAY,CAAC;YAEtC,IAAI,OAAO,MAAM,GAAG,GAAG;gBACrB,MAAM,kBAAkB;oBACtB,OAAO;oBACP,cAAc;oBACd,eAAe;oBACf,OAAO;oBACP,WAAW;oBACX,gBAAgB;oBAChB,eAAe;gBACjB;gBAEA,MAAM,kBAAkB,IAAI,+IAAA,CAAA,kBAAqB,CAC/C,QACA;gBAEF,gBAAgB,MAAM;gBACtB,gBAAgB,KAAK,CAAC,MAAM,CAAC,MAAM,OAAO,yCAAyC;gBAEnF,iDAAiD;gBACjD,gBAAgB,oBAAoB;gBAEpC,YAAY;YACd;QACF;IACF,GAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,QAAQ,OAAO,EAAE;YACnB,8BAA8B;YAC9B,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GACxB,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,SAAS;YAC5D,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GACxB,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,MAAM,SAAS;YACpD,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GACxB,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,MAAM,SAAS;YAEpD,wBAAwB;YACxB,IAAI,UAAU;gBACZ,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI;gBAC9B,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAC7B,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;YAElD,OAAO,IAAI,WAAW;gBACpB,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;YAClC,OAAO;gBACL,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;YAClC;QACF;IACF;IAEA,IAAI,CAAC,UAAU,OAAO;IAEtB,qBACE,8OAAC,yJAAA,CAAA,QAAK;QAAC,OAAO;QAAG,mBAAmB;QAAK,gBAAgB;kBACvD,cAAA,8OAAC;YACC,KAAK;YACL,UAAU;YACV,UAAU;YACV,SAAS;YACT,eAAe,IAAM,QAAQ;YAC7B,cAAc,IAAM,QAAQ;sBAE5B,cAAA,8OAAC,uKAAA,CAAA,sBAAmB;gBAClB,OAAO,WAAW,YAAY,YAAY,YAAY;gBACtD,SAAS,WAAW,MAAM,YAAY,OAAO;gBAC7C,OAAO,WAAW,IAAI,YAAY,IAAI;gBACtC,WAAW,WAAW,OAAO;gBAC7B,WAAW,WAAW,OAAO;gBAC7B,WAAW;gBACX,oBAAoB;;;;;;;;;;;;;;;;AAK9B;AAQO,SAAS,UAAU,EACxB,aAAa,EACb,gBAAgB;IAAC;IAAO;IAAO;IAAO;IAAO;CAAM,EACnD,YAAY,EAAE,EACC;IACf,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAC9D;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,YAAY,OAAO,UAAU,GAAG;QAClC;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,UAAU;QACd;YACE,QAAQ;YACR,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;QACR;QACA;YACE,QAAQ;YACR,MAAM;QACR;KACD;IAED,MAAM,YAAwC,WAC1C;QACE;YAAC,CAAC;YAAK;YAAK;SAAE;QACd;YAAC;YAAG;YAAK;SAAE;QACX;YAAC;YAAK;YAAG;SAAE;QACX;YAAC,CAAC;YAAK,CAAC;YAAK;SAAE;QACf;YAAC;YAAK,CAAC;YAAK;SAAE;KACf,CAAC,yCAAyC;OAC3C;QACE;YAAC,CAAC;YAAK;YAAG;SAAE;QACZ;YAAC,CAAC;YAAK;YAAG;SAAE;QACZ;YAAC;YAAG;YAAG;SAAE;QACT;YAAC;YAAK;YAAG;SAAE;QACX;YAAC;YAAK;YAAG;SAAE;KACZ,EAAE,gDAAgD;IAEvD,MAAM,oBAAoB,CAAC,QAAgB;QACzC,gBAAgB,QAAQ;IAC1B;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,MAAM,aAAa;eAAI;SAAe;QACtC,UAAU,CAAC,MAAM,GAAG;QACpB,kBAAkB;IACpB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,gBAAgB,EAAE,WAAW;kBAC5C,cAAA,8OAAC,mMAAA,CAAA,SAAM;YACL,QAAQ;gBACN,UAAU,WAAW;oBAAC;oBAAG;oBAAG;iBAAE,GAAG;oBAAC;oBAAG;oBAAG;iBAAG;gBAC3C,KAAK,WAAW,KAAK;YACvB;YACA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,OAAO;gBAAE,OAAO;gBAAQ,QAAQ;YAAO;;8BAEvC,8OAAC;oBAAa,WAAW;;;;;;8BACzB,8OAAC;oBAAW,UAAU;wBAAC;wBAAI;wBAAI;qBAAG;oBAAE,WAAW;oBAAK,OAAM;;;;;;8BAC1D,8OAAC;oBACC,UAAU;wBAAC,CAAC;wBAAI,CAAC;wBAAI,CAAC;qBAAG;oBACzB,WAAW;oBACX,OAAM;;;;;;8BAER,8OAAC;oBAAW,UAAU;wBAAC;wBAAG;wBAAI;qBAAE;oBAAE,WAAW;oBAAK,OAAM;;;;;;8BACxD,8OAAC;oBACC,UAAU;wBAAC;wBAAG;wBAAI;qBAAG;oBACrB,OAAO;oBACP,UAAU;oBACV,WAAW;oBACX,UAAU;;;;;;8BAGZ,8OAAC,+JAAA,CAAA,cAAW;oBAAC,QAAO;;;;;;8BAEpB,8OAAC,0JAAA,CAAA,SAAM;8BACJ,QAAQ,GAAG,CAAC,CAAC,YAAY,sBACxB,8OAAC;4BAEC,SAAS,WAAW,IAAI;4BACxB,UAAU,SAAS,CAAC,MAAM;4BAC1B,OAAO;4BACP,UAAU,aAAa,CAAC,MAAM;4BAC9B,WAAW,cAAc,CAAC,MAAM;4BAChC,SAAS,IAAM,kBAAkB,WAAW,MAAM,EAAE;4BACpD,SAAS,CAAC,UAAY,kBAAkB,OAAO;2BAP1C,WAAW,MAAM;;;;;;;;;;8BAY5B,8OAAC,iKAAA,CAAA,gBAAa;oBACZ,WAAW;oBACX,YAAY,WAAW,QAAQ;oBAC/B,cAAc;oBACd,YAAY,CAAC;oBACb,iBAAiB;oBACjB,aAAa,WAAW,KAAK;oBAC7B,aAAa,WAAW,IAAI;oBAC5B,eAAe,KAAK,EAAE,GAAG;oBACzB,eAAe,KAAK,EAAE,GAAG;;;;;;;;;;;;;;;;;AAKnC", "debugId": null}}, {"offset": {"line": 2183, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/InteractiveGame.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useCallback } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\n\ninterface GameState {\n  level: number;\n  score: number;\n  lives: number;\n  sequence: number[];\n  playerSequence: number[];\n  isPlaying: boolean;\n  isShowingSequence: boolean;\n  gameOver: boolean;\n  victory: boolean;\n}\n\ninterface InteractiveGameProps {\n  onGameComplete?: (score: number) => void;\n  onLetterActivate?: (index: number) => void;\n}\n\nexport function InteractiveGame({\n  onGameComplete,\n  onLetterActivate,\n}: InteractiveGameProps) {\n  const [gameState, setGameState] = useState<GameState>({\n    level: 1,\n    score: 0,\n    lives: 3,\n    sequence: [],\n    playerSequence: [],\n    isPlaying: false,\n    isShowingSequence: false,\n    gameOver: false,\n    victory: false,\n  });\n\n  const [activeButton, setActiveButton] = useState<number | null>(null);\n  const [isMobile, setIsMobile] = useState(false);\n\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n\n    checkMobile();\n    window.addEventListener(\"resize\", checkMobile);\n    return () => window.removeEventListener(\"resize\", checkMobile);\n  }, []);\n\n  const letters = [\"h\", \"o\", \"w\", \"r\", \"u\"];\n  const colors = [\"#ff6b6b\", \"#4ecdc4\", \"#45b7d1\", \"#96ceb4\", \"#feca57\"];\n\n  const generateSequence = useCallback((level: number) => {\n    const sequence = [];\n    for (let i = 0; i < level + 2; i++) {\n      sequence.push(Math.floor(Math.random() * 5));\n    }\n    return sequence;\n  }, []);\n\n  const startGame = () => {\n    const newSequence = generateSequence(1);\n    setGameState({\n      level: 1,\n      score: 0,\n      lives: 3,\n      sequence: newSequence,\n      playerSequence: [],\n      isPlaying: true,\n      isShowingSequence: true,\n      gameOver: false,\n      victory: false,\n    });\n    showSequence(newSequence);\n  };\n\n  const showSequence = async (sequence: number[]) => {\n    setGameState((prev) => ({ ...prev, isShowingSequence: true }));\n\n    for (let i = 0; i < sequence.length; i++) {\n      await new Promise((resolve) => setTimeout(resolve, 600));\n      setActiveButton(sequence[i]);\n      onLetterActivate?.(sequence[i]);\n\n      await new Promise((resolve) => setTimeout(resolve, 400));\n      setActiveButton(null);\n    }\n\n    setGameState((prev) => ({ ...prev, isShowingSequence: false }));\n  };\n\n  const handleButtonClick = (index: number) => {\n    if (gameState.isShowingSequence || gameState.gameOver || gameState.victory)\n      return;\n\n    const newPlayerSequence = [...gameState.playerSequence, index];\n    setActiveButton(index);\n    onLetterActivate?.(index);\n\n    setTimeout(() => setActiveButton(null), 200);\n\n    // Vérifier si le joueur a fait une erreur\n    if (\n      newPlayerSequence[newPlayerSequence.length - 1] !==\n      gameState.sequence[newPlayerSequence.length - 1]\n    ) {\n      // Erreur !\n      const newLives = gameState.lives - 1;\n      if (newLives <= 0) {\n        setGameState((prev) => ({ ...prev, gameOver: true, isPlaying: false }));\n      } else {\n        setGameState((prev) => ({\n          ...prev,\n          lives: newLives,\n          playerSequence: [],\n          isShowingSequence: true,\n        }));\n        setTimeout(() => showSequence(gameState.sequence), 1000);\n      }\n      return;\n    }\n\n    // Vérifier si la séquence est complète\n    if (newPlayerSequence.length === gameState.sequence.length) {\n      const newScore = gameState.score + gameState.level * 100;\n      const newLevel = gameState.level + 1;\n\n      if (newLevel > 10) {\n        // Victoire !\n        setGameState((prev) => ({\n          ...prev,\n          victory: true,\n          isPlaying: false,\n          score: newScore,\n        }));\n        onGameComplete?.(newScore);\n      } else {\n        // Niveau suivant\n        const newSequence = generateSequence(newLevel);\n        setGameState((prev) => ({\n          ...prev,\n          level: newLevel,\n          score: newScore,\n          sequence: newSequence,\n          playerSequence: [],\n          isShowingSequence: true,\n        }));\n        setTimeout(() => showSequence(newSequence), 1500);\n      }\n    } else {\n      setGameState((prev) => ({ ...prev, playerSequence: newPlayerSequence }));\n    }\n  };\n\n  const resetGame = () => {\n    setGameState({\n      level: 1,\n      score: 0,\n      lives: 3,\n      sequence: [],\n      playerSequence: [],\n      isPlaying: false,\n      isShowingSequence: false,\n      gameOver: false,\n      victory: false,\n    });\n  };\n\n  return (\n    <div\n      className={`w-full max-w-2xl mx-auto p-6 ${isMobile ? \"px-4\" : \"px-6\"}`}\n    >\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"text-center mb-8\"\n      >\n        <h2 className=\"text-whisper-300 text-2xl font-light mb-4\">\n          Jeu de Mémoire \"how r u\"\n        </h2>\n        <p className=\"text-whisper-500 text-sm font-light mb-6\">\n          Mémorise et reproduis la séquence de lettres\n        </p>\n\n        {/* Stats du jeu */}\n        <div\n          className={`flex ${\n            isMobile ? \"flex-col space-y-2\" : \"justify-center space-x-8\"\n          } mb-6`}\n        >\n          <div className=\"text-whisper-400\">\n            <span className=\"text-whisper-300\">Niveau:</span> {gameState.level}\n          </div>\n          <div className=\"text-whisper-400\">\n            <span className=\"text-whisper-300\">Score:</span> {gameState.score}\n          </div>\n          <div className=\"text-whisper-400\">\n            <span className=\"text-whisper-300\">Vies:</span>\n            {Array.from({ length: gameState.lives }).map((_, i) => (\n              <span key={i} className=\"text-red-400 ml-1\">\n                ♥\n              </span>\n            ))}\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Boutons de lettres */}\n      <div\n        className={`grid ${\n          isMobile\n            ? \"grid-cols-2 gap-4 max-w-xs mx-auto\"\n            : \"grid-cols-5 gap-6 max-w-2xl mx-auto\"\n        } mb-8`}\n      >\n        {letters.map((letter, index) => (\n          <motion.button\n            key={letter}\n            onClick={() => handleButtonClick(index)}\n            disabled={\n              gameState.isShowingSequence ||\n              gameState.gameOver ||\n              gameState.victory\n            }\n            className={`\n              ${isMobile ? \"h-20 w-20 text-lg\" : \"h-24 w-24 text-2xl\"}\n              rounded-xl border-3 font-mono font-bold\n              transition-all duration-300 transform backdrop-blur-sm\n              ${\n                activeButton === index\n                  ? \"scale-110 shadow-2xl\"\n                  : \"hover:scale-105\"\n              }\n              ${\n                gameState.isShowingSequence ||\n                gameState.gameOver ||\n                gameState.victory\n                  ? \"opacity-40 cursor-not-allowed\"\n                  : \"cursor-pointer hover:shadow-lg\"\n              }\n            `}\n            style={{\n              backgroundColor:\n                activeButton === index\n                  ? colors[index]\n                  : \"rgba(255,255,255,0.05)\",\n              borderColor: colors[index],\n              color: activeButton === index ? \"#000\" : colors[index],\n              boxShadow:\n                activeButton === index ? `0 0 30px ${colors[index]}` : \"none\",\n            }}\n            whileTap={{ scale: 0.9 }}\n            whileHover={{\n              boxShadow: `0 0 20px ${colors[index]}40`,\n              borderWidth: \"3px\",\n            }}\n          >\n            {letter}\n          </motion.button>\n        ))}\n        {/* Bouton central pour mobile */}\n        {isMobile && (\n          <motion.div\n            className=\"col-span-2 flex justify-center\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.5 }}\n          >\n            <motion.button\n              onClick={() => handleButtonClick(4)}\n              disabled={\n                gameState.isShowingSequence ||\n                gameState.gameOver ||\n                gameState.victory\n              }\n              className={`\n                h-20 w-20 text-lg rounded-xl border-3 font-mono font-bold\n                transition-all duration-300 transform backdrop-blur-sm\n                ${\n                  activeButton === 4\n                    ? \"scale-110 shadow-2xl\"\n                    : \"hover:scale-105\"\n                }\n                ${\n                  gameState.isShowingSequence ||\n                  gameState.gameOver ||\n                  gameState.victory\n                    ? \"opacity-40 cursor-not-allowed\"\n                    : \"cursor-pointer hover:shadow-lg\"\n                }\n              `}\n              style={{\n                backgroundColor:\n                  activeButton === 4 ? colors[4] : \"rgba(255,255,255,0.05)\",\n                borderColor: colors[4],\n                color: activeButton === 4 ? \"#000\" : colors[4],\n                boxShadow:\n                  activeButton === 4 ? `0 0 30px ${colors[4]}` : \"none\",\n              }}\n              whileTap={{ scale: 0.9 }}\n            >\n              {letters[4]}\n            </motion.button>\n          </motion.div>\n        )}\n      </div>\n\n      {/* Messages de statut */}\n      <AnimatePresence>\n        {gameState.isShowingSequence && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"text-center text-whisper-400 mb-4\"\n          >\n            Mémorise la séquence...\n          </motion.div>\n        )}\n\n        {gameState.gameOver && (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"text-center mb-4\"\n          >\n            <div className=\"text-red-400 text-xl mb-2\">Game Over</div>\n            <div className=\"text-whisper-500 text-sm\">\n              Score final: {gameState.score}\n            </div>\n          </motion.div>\n        )}\n\n        {gameState.victory && (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"text-center mb-4\"\n          >\n            <div className=\"text-green-400 text-xl mb-2\">Victoire ! 🎉</div>\n            <div className=\"text-whisper-500 text-sm\">\n              Score final: {gameState.score}\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Boutons de contrôle */}\n      <div className=\"text-center space-y-4\">\n        {!gameState.isPlaying && !gameState.gameOver && !gameState.victory && (\n          <motion.button\n            onClick={startGame}\n            className=\"px-6 py-3 bg-ghost-100 border border-whisper-200 text-whisper-300 font-light hover:bg-ghost-200 transition-colors\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            Commencer le Jeu\n          </motion.button>\n        )}\n\n        {(gameState.gameOver || gameState.victory) && (\n          <motion.button\n            onClick={resetGame}\n            className=\"px-6 py-3 bg-ghost-100 border border-whisper-200 text-whisper-300 font-light hover:bg-ghost-200 transition-colors\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            Rejouer\n          </motion.button>\n        )}\n      </div>\n\n      {/* Instructions */}\n      {!gameState.isPlaying && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          className=\"text-center mt-8 text-whisper-600 text-xs font-light\"\n        >\n          <p>\n            Regarde la séquence, puis reproduis-la en cliquant sur les lettres\n          </p>\n          <p className=\"mt-1\">10 niveaux à compléter pour gagner !</p>\n        </motion.div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAsBO,SAAS,gBAAgB,EAC9B,cAAc,EACd,gBAAgB,EACK;IACrB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,OAAO;QACP,OAAO;QACP,OAAO;QACP,UAAU,EAAE;QACZ,gBAAgB,EAAE;QAClB,WAAW;QACX,mBAAmB;QACnB,UAAU;QACV,SAAS;IACX;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,YAAY,OAAO,UAAU,GAAG;QAClC;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,UAAU;QAAC;QAAK;QAAK;QAAK;QAAK;KAAI;IACzC,MAAM,SAAS;QAAC;QAAW;QAAW;QAAW;QAAW;KAAU;IAEtE,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,WAAW,EAAE;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,GAAG,IAAK;YAClC,SAAS,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAC3C;QACA,OAAO;IACT,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,MAAM,cAAc,iBAAiB;QACrC,aAAa;YACX,OAAO;YACP,OAAO;YACP,OAAO;YACP,UAAU;YACV,gBAAgB,EAAE;YAClB,WAAW;YACX,mBAAmB;YACnB,UAAU;YACV,SAAS;QACX;QACA,aAAa;IACf;IAEA,MAAM,eAAe,OAAO;QAC1B,aAAa,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,mBAAmB;YAAK,CAAC;QAE5D,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACxC,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;YACnD,gBAAgB,QAAQ,CAAC,EAAE;YAC3B,mBAAmB,QAAQ,CAAC,EAAE;YAE9B,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;YACnD,gBAAgB;QAClB;QAEA,aAAa,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,mBAAmB;YAAM,CAAC;IAC/D;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,UAAU,iBAAiB,IAAI,UAAU,QAAQ,IAAI,UAAU,OAAO,EACxE;QAEF,MAAM,oBAAoB;eAAI,UAAU,cAAc;YAAE;SAAM;QAC9D,gBAAgB;QAChB,mBAAmB;QAEnB,WAAW,IAAM,gBAAgB,OAAO;QAExC,0CAA0C;QAC1C,IACE,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE,KAC/C,UAAU,QAAQ,CAAC,kBAAkB,MAAM,GAAG,EAAE,EAChD;YACA,WAAW;YACX,MAAM,WAAW,UAAU,KAAK,GAAG;YACnC,IAAI,YAAY,GAAG;gBACjB,aAAa,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,UAAU;wBAAM,WAAW;oBAAM,CAAC;YACvE,OAAO;gBACL,aAAa,CAAC,OAAS,CAAC;wBACtB,GAAG,IAAI;wBACP,OAAO;wBACP,gBAAgB,EAAE;wBAClB,mBAAmB;oBACrB,CAAC;gBACD,WAAW,IAAM,aAAa,UAAU,QAAQ,GAAG;YACrD;YACA;QACF;QAEA,uCAAuC;QACvC,IAAI,kBAAkB,MAAM,KAAK,UAAU,QAAQ,CAAC,MAAM,EAAE;YAC1D,MAAM,WAAW,UAAU,KAAK,GAAG,UAAU,KAAK,GAAG;YACrD,MAAM,WAAW,UAAU,KAAK,GAAG;YAEnC,IAAI,WAAW,IAAI;gBACjB,aAAa;gBACb,aAAa,CAAC,OAAS,CAAC;wBACtB,GAAG,IAAI;wBACP,SAAS;wBACT,WAAW;wBACX,OAAO;oBACT,CAAC;gBACD,iBAAiB;YACnB,OAAO;gBACL,iBAAiB;gBACjB,MAAM,cAAc,iBAAiB;gBACrC,aAAa,CAAC,OAAS,CAAC;wBACtB,GAAG,IAAI;wBACP,OAAO;wBACP,OAAO;wBACP,UAAU;wBACV,gBAAgB,EAAE;wBAClB,mBAAmB;oBACrB,CAAC;gBACD,WAAW,IAAM,aAAa,cAAc;YAC9C;QACF,OAAO;YACL,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,gBAAgB;gBAAkB,CAAC;QACxE;IACF;IAEA,MAAM,YAAY;QAChB,aAAa;YACX,OAAO;YACP,OAAO;YACP,OAAO;YACP,UAAU,EAAE;YACZ,gBAAgB,EAAE;YAClB,WAAW;YACX,mBAAmB;YACnB,UAAU;YACV,SAAS;QACX;IACF;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,6BAA6B,EAAE,WAAW,SAAS,QAAQ;;0BAEvE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAG,WAAU;kCAA4C;;;;;;kCAG1D,8OAAC;wBAAE,WAAU;kCAA2C;;;;;;kCAKxD,8OAAC;wBACC,WAAW,CAAC,KAAK,EACf,WAAW,uBAAuB,2BACnC,KAAK,CAAC;;0CAEP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAmB;;;;;;oCAAc;oCAAE,UAAU,KAAK;;;;;;;0CAEpE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAmB;;;;;;oCAAa;oCAAE,UAAU,KAAK;;;;;;;0CAEnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAmB;;;;;;oCAClC,MAAM,IAAI,CAAC;wCAAE,QAAQ,UAAU,KAAK;oCAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAC/C,8OAAC;4CAAa,WAAU;sDAAoB;2CAAjC;;;;;;;;;;;;;;;;;;;;;;;0BASnB,8OAAC;gBACC,WAAW,CAAC,KAAK,EACf,WACI,uCACA,sCACL,KAAK,CAAC;;oBAEN,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BAEZ,SAAS,IAAM,kBAAkB;4BACjC,UACE,UAAU,iBAAiB,IAC3B,UAAU,QAAQ,IAClB,UAAU,OAAO;4BAEnB,WAAW,CAAC;cACV,EAAE,WAAW,sBAAsB,qBAAqB;;;cAGxD,EACE,iBAAiB,QACb,yBACA,kBACL;cACD,EACE,UAAU,iBAAiB,IAC3B,UAAU,QAAQ,IAClB,UAAU,OAAO,GACb,kCACA,iCACL;YACH,CAAC;4BACD,OAAO;gCACL,iBACE,iBAAiB,QACb,MAAM,CAAC,MAAM,GACb;gCACN,aAAa,MAAM,CAAC,MAAM;gCAC1B,OAAO,iBAAiB,QAAQ,SAAS,MAAM,CAAC,MAAM;gCACtD,WACE,iBAAiB,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG;4BAC3D;4BACA,UAAU;gCAAE,OAAO;4BAAI;4BACvB,YAAY;gCACV,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gCACxC,aAAa;4BACf;sCAEC;2BAxCI;;;;;oBA4CR,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS,IAAM,kBAAkB;4BACjC,UACE,UAAU,iBAAiB,IAC3B,UAAU,QAAQ,IAClB,UAAU,OAAO;4BAEnB,WAAW,CAAC;;;gBAGV,EACE,iBAAiB,IACb,yBACA,kBACL;gBACD,EACE,UAAU,iBAAiB,IAC3B,UAAU,QAAQ,IAClB,UAAU,OAAO,GACb,kCACA,iCACL;cACH,CAAC;4BACD,OAAO;gCACL,iBACE,iBAAiB,IAAI,MAAM,CAAC,EAAE,GAAG;gCACnC,aAAa,MAAM,CAAC,EAAE;gCACtB,OAAO,iBAAiB,IAAI,SAAS,MAAM,CAAC,EAAE;gCAC9C,WACE,iBAAiB,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,EAAE,GAAG;4BACnD;4BACA,UAAU;gCAAE,OAAO;4BAAI;sCAEtB,OAAO,CAAC,EAAE;;;;;;;;;;;;;;;;;0BAOnB,8OAAC,yLAAA,CAAA,kBAAe;;oBACb,UAAU,iBAAiB,kBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,MAAM;4BAAE,SAAS;wBAAE;wBACnB,WAAU;kCACX;;;;;;oBAKF,UAAU,QAAQ,kBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;0CAA4B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;;oCAA2B;oCAC1B,UAAU,KAAK;;;;;;;;;;;;;oBAKlC,UAAU,OAAO,kBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,8OAAC;gCAAI,WAAU;;oCAA2B;oCAC1B,UAAU,KAAK;;;;;;;;;;;;;;;;;;;0BAOrC,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,UAAU,SAAS,IAAI,CAAC,UAAU,QAAQ,IAAI,CAAC,UAAU,OAAO,kBAChE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;wBACT,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;oBAKF,CAAC,UAAU,QAAQ,IAAI,UAAU,OAAO,mBACvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;wBACT,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;;;;;;;YAOJ,CAAC,UAAU,SAAS,kBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,WAAU;;kCAEV,8OAAC;kCAAE;;;;;;kCAGH,8OAAC;wBAAE,WAAU;kCAAO;;;;;;;;;;;;;;;;;;AAK9B", "debugId": null}}, {"offset": {"line": 2712, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/MysticSoundscape.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useRef, useState } from \"react\";\n\ninterface MysticSoundscapeProps {\n  isActive?: boolean;\n  intensity?: number;\n  phase?: \"awakening\" | \"exploration\" | \"ritual\" | \"transcendence\" | \"void\";\n}\n\nexport function MysticSoundscape({ \n  isActive = true, \n  intensity = 1,\n  phase = \"awakening\" \n}: MysticSoundscapeProps) {\n  const audioContextRef = useRef<AudioContext | null>(null);\n  const oscillatorsRef = useRef<OscillatorNode[]>([]);\n  const gainNodesRef = useRef<GainNode[]>([]);\n  const [isInitialized, setIsInitialized] = useState(false);\n\n  useEffect(() => {\n    if (!isActive) return;\n\n    const initAudio = () => {\n      try {\n        // Créer le contexte audio\n        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();\n        \n        // Créer les oscillateurs pour différentes fréquences mystiques\n        const frequencies = [\n          55,    // Fréquence de base profonde\n          110,   // Harmonique\n          220,   // Harmonique supérieure\n          440,   // Note de référence\n          880,   // Octave supérieure\n        ];\n\n        frequencies.forEach((freq, index) => {\n          const oscillator = audioContextRef.current!.createOscillator();\n          const gainNode = audioContextRef.current!.createGain();\n          \n          // Configuration de l'oscillateur\n          oscillator.type = index % 2 === 0 ? 'sine' : 'triangle';\n          oscillator.frequency.setValueAtTime(freq, audioContextRef.current!.currentTime);\n          \n          // Configuration du gain (volume très bas pour l'ambiance)\n          gainNode.gain.setValueAtTime(0.01 * intensity, audioContextRef.current!.currentTime);\n          \n          // Connexion audio\n          oscillator.connect(gainNode);\n          gainNode.connect(audioContextRef.current!.destination);\n          \n          // Démarrer l'oscillateur\n          oscillator.start();\n          \n          // Stocker les références\n          oscillatorsRef.current.push(oscillator);\n          gainNodesRef.current.push(gainNode);\n        });\n\n        setIsInitialized(true);\n      } catch (error) {\n        console.log(\"Audio context not available:\", error);\n      }\n    };\n\n    // Initialiser l'audio au premier clic de l'utilisateur\n    const handleUserInteraction = () => {\n      if (!isInitialized) {\n        initAudio();\n        document.removeEventListener('click', handleUserInteraction);\n        document.removeEventListener('keydown', handleUserInteraction);\n      }\n    };\n\n    document.addEventListener('click', handleUserInteraction);\n    document.addEventListener('keydown', handleUserInteraction);\n\n    return () => {\n      document.removeEventListener('click', handleUserInteraction);\n      document.removeEventListener('keydown', handleUserInteraction);\n    };\n  }, [isActive, intensity, isInitialized]);\n\n  useEffect(() => {\n    if (!isInitialized || !audioContextRef.current) return;\n\n    // Ajuster les fréquences et volumes selon la phase\n    const phaseSettings = {\n      awakening: { baseFreq: 55, volume: 0.005 },\n      exploration: { baseFreq: 110, volume: 0.008 },\n      ritual: { baseFreq: 220, volume: 0.012 },\n      transcendence: { baseFreq: 440, volume: 0.015 },\n      void: { baseFreq: 880, volume: 0.020 },\n    };\n\n    const settings = phaseSettings[phase];\n    const currentTime = audioContextRef.current.currentTime;\n\n    oscillatorsRef.current.forEach((oscillator, index) => {\n      if (oscillator && gainNodesRef.current[index]) {\n        // Modulation de fréquence pour créer un effet mystique\n        const modulation = Math.sin(currentTime * 0.1 + index) * 0.1;\n        const newFreq = settings.baseFreq * (index + 1) * (1 + modulation);\n        \n        try {\n          oscillator.frequency.setTargetAtTime(\n            newFreq, \n            currentTime, \n            2 // Transition douce de 2 secondes\n          );\n          \n          gainNodesRef.current[index].gain.setTargetAtTime(\n            settings.volume * intensity * (1 + modulation * 0.5),\n            currentTime,\n            1\n          );\n        } catch (error) {\n          // Oscillateur peut-être déjà arrêté\n        }\n      }\n    });\n  }, [phase, intensity, isInitialized]);\n\n  useEffect(() => {\n    return () => {\n      // Nettoyage lors du démontage\n      oscillatorsRef.current.forEach(oscillator => {\n        try {\n          oscillator.stop();\n          oscillator.disconnect();\n        } catch (error) {\n          // Oscillateur déjà arrêté\n        }\n      });\n      \n      gainNodesRef.current.forEach(gainNode => {\n        try {\n          gainNode.disconnect();\n        } catch (error) {\n          // Gain node déjà déconnecté\n        }\n      });\n\n      if (audioContextRef.current) {\n        audioContextRef.current.close();\n      }\n    };\n  }, []);\n\n  // Fonction pour créer des effets sonores ponctuels\n  const playMysticChime = (frequency: number = 440, duration: number = 1000) => {\n    if (!audioContextRef.current) return;\n\n    const oscillator = audioContextRef.current.createOscillator();\n    const gainNode = audioContextRef.current.createGain();\n    \n    oscillator.type = 'sine';\n    oscillator.frequency.setValueAtTime(frequency, audioContextRef.current.currentTime);\n    \n    // Enveloppe ADSR pour un son de cloche mystique\n    const currentTime = audioContextRef.current.currentTime;\n    gainNode.gain.setValueAtTime(0, currentTime);\n    gainNode.gain.linearRampToValueAtTime(0.1 * intensity, currentTime + 0.1);\n    gainNode.gain.exponentialRampToValueAtTime(0.001, currentTime + duration / 1000);\n    \n    oscillator.connect(gainNode);\n    gainNode.connect(audioContextRef.current.destination);\n    \n    oscillator.start(currentTime);\n    oscillator.stop(currentTime + duration / 1000);\n  };\n\n  // Exposer la fonction pour les autres composants\n  useEffect(() => {\n    (window as any).playMysticChime = playMysticChime;\n    return () => {\n      delete (window as any).playMysticChime;\n    };\n  }, [intensity]);\n\n  return null; // Ce composant ne rend rien visuellement\n}\n\n// Hook personnalisé pour utiliser les sons mystiques\nexport function useMysticSounds() {\n  const playChime = (frequency?: number, duration?: number) => {\n    if ((window as any).playMysticChime) {\n      (window as any).playMysticChime(frequency, duration);\n    }\n  };\n\n  const playLetterSound = (letterIndex: number) => {\n    const frequencies = [220, 277, 330, 392, 440]; // Gamme pentatonique mystique\n    playChime(frequencies[letterIndex % frequencies.length], 800);\n  };\n\n  const playSecretSound = () => {\n    // Son spécial pour les secrets révélés\n    playChime(880, 1500);\n    setTimeout(() => playChime(660, 1000), 200);\n    setTimeout(() => playChime(440, 1200), 400);\n  };\n\n  const playRitualSound = () => {\n    // Séquence sonore pour les rituels\n    const sequence = [110, 165, 220, 330, 440];\n    sequence.forEach((freq, index) => {\n      setTimeout(() => playChime(freq, 600), index * 300);\n    });\n  };\n\n  return {\n    playChime,\n    playLetterSound,\n    playSecretSound,\n    playRitualSound,\n  };\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAUO,SAAS,iBAAiB,EAC/B,WAAW,IAAI,EACf,YAAY,CAAC,EACb,QAAQ,WAAW,EACG;IACtB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IACpD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB,EAAE;IAClD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc,EAAE;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU;QAEf,MAAM,YAAY;YAChB,IAAI;gBACF,0BAA0B;gBAC1B,gBAAgB,OAAO,GAAG,IAAI,CAAC,OAAO,YAAY,IAAI,AAAC,OAAe,kBAAkB;gBAExF,+DAA+D;gBAC/D,MAAM,cAAc;oBAClB;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,YAAY,OAAO,CAAC,CAAC,MAAM;oBACzB,MAAM,aAAa,gBAAgB,OAAO,CAAE,gBAAgB;oBAC5D,MAAM,WAAW,gBAAgB,OAAO,CAAE,UAAU;oBAEpD,iCAAiC;oBACjC,WAAW,IAAI,GAAG,QAAQ,MAAM,IAAI,SAAS;oBAC7C,WAAW,SAAS,CAAC,cAAc,CAAC,MAAM,gBAAgB,OAAO,CAAE,WAAW;oBAE9E,0DAA0D;oBAC1D,SAAS,IAAI,CAAC,cAAc,CAAC,OAAO,WAAW,gBAAgB,OAAO,CAAE,WAAW;oBAEnF,kBAAkB;oBAClB,WAAW,OAAO,CAAC;oBACnB,SAAS,OAAO,CAAC,gBAAgB,OAAO,CAAE,WAAW;oBAErD,yBAAyB;oBACzB,WAAW,KAAK;oBAEhB,yBAAyB;oBACzB,eAAe,OAAO,CAAC,IAAI,CAAC;oBAC5B,aAAa,OAAO,CAAC,IAAI,CAAC;gBAC5B;gBAEA,iBAAiB;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,gCAAgC;YAC9C;QACF;QAEA,uDAAuD;QACvD,MAAM,wBAAwB;YAC5B,IAAI,CAAC,eAAe;gBAClB;gBACA,SAAS,mBAAmB,CAAC,SAAS;gBACtC,SAAS,mBAAmB,CAAC,WAAW;YAC1C;QACF;QAEA,SAAS,gBAAgB,CAAC,SAAS;QACnC,SAAS,gBAAgB,CAAC,WAAW;QAErC,OAAO;YACL,SAAS,mBAAmB,CAAC,SAAS;YACtC,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;QAAU;QAAW;KAAc;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,OAAO,EAAE;QAEhD,mDAAmD;QACnD,MAAM,gBAAgB;YACpB,WAAW;gBAAE,UAAU;gBAAI,QAAQ;YAAM;YACzC,aAAa;gBAAE,UAAU;gBAAK,QAAQ;YAAM;YAC5C,QAAQ;gBAAE,UAAU;gBAAK,QAAQ;YAAM;YACvC,eAAe;gBAAE,UAAU;gBAAK,QAAQ;YAAM;YAC9C,MAAM;gBAAE,UAAU;gBAAK,QAAQ;YAAM;QACvC;QAEA,MAAM,WAAW,aAAa,CAAC,MAAM;QACrC,MAAM,cAAc,gBAAgB,OAAO,CAAC,WAAW;QAEvD,eAAe,OAAO,CAAC,OAAO,CAAC,CAAC,YAAY;YAC1C,IAAI,cAAc,aAAa,OAAO,CAAC,MAAM,EAAE;gBAC7C,uDAAuD;gBACvD,MAAM,aAAa,KAAK,GAAG,CAAC,cAAc,MAAM,SAAS;gBACzD,MAAM,UAAU,SAAS,QAAQ,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,UAAU;gBAEjE,IAAI;oBACF,WAAW,SAAS,CAAC,eAAe,CAClC,SACA,aACA,EAAE,iCAAiC;;oBAGrC,aAAa,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAC9C,SAAS,MAAM,GAAG,YAAY,CAAC,IAAI,aAAa,GAAG,GACnD,aACA;gBAEJ,EAAE,OAAO,OAAO;gBACd,oCAAoC;gBACtC;YACF;QACF;IACF,GAAG;QAAC;QAAO;QAAW;KAAc;IAEpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,8BAA8B;YAC9B,eAAe,OAAO,CAAC,OAAO,CAAC,CAAA;gBAC7B,IAAI;oBACF,WAAW,IAAI;oBACf,WAAW,UAAU;gBACvB,EAAE,OAAO,OAAO;gBACd,0BAA0B;gBAC5B;YACF;YAEA,aAAa,OAAO,CAAC,OAAO,CAAC,CAAA;gBAC3B,IAAI;oBACF,SAAS,UAAU;gBACrB,EAAE,OAAO,OAAO;gBACd,4BAA4B;gBAC9B;YACF;YAEA,IAAI,gBAAgB,OAAO,EAAE;gBAC3B,gBAAgB,OAAO,CAAC,KAAK;YAC/B;QACF;IACF,GAAG,EAAE;IAEL,mDAAmD;IACnD,MAAM,kBAAkB,CAAC,YAAoB,GAAG,EAAE,WAAmB,IAAI;QACvE,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAE9B,MAAM,aAAa,gBAAgB,OAAO,CAAC,gBAAgB;QAC3D,MAAM,WAAW,gBAAgB,OAAO,CAAC,UAAU;QAEnD,WAAW,IAAI,GAAG;QAClB,WAAW,SAAS,CAAC,cAAc,CAAC,WAAW,gBAAgB,OAAO,CAAC,WAAW;QAElF,gDAAgD;QAChD,MAAM,cAAc,gBAAgB,OAAO,CAAC,WAAW;QACvD,SAAS,IAAI,CAAC,cAAc,CAAC,GAAG;QAChC,SAAS,IAAI,CAAC,uBAAuB,CAAC,MAAM,WAAW,cAAc;QACrE,SAAS,IAAI,CAAC,4BAA4B,CAAC,OAAO,cAAc,WAAW;QAE3E,WAAW,OAAO,CAAC;QACnB,SAAS,OAAO,CAAC,gBAAgB,OAAO,CAAC,WAAW;QAEpD,WAAW,KAAK,CAAC;QACjB,WAAW,IAAI,CAAC,cAAc,WAAW;IAC3C;IAEA,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACP,OAAe,eAAe,GAAG;QAClC,OAAO;YACL,OAAO,AAAC,OAAe,eAAe;QACxC;IACF,GAAG;QAAC;KAAU;IAEd,OAAO,MAAM,yCAAyC;AACxD;AAGO,SAAS;IACd,MAAM,YAAY,CAAC,WAAoB;QACrC,IAAI,AAAC,OAAe,eAAe,EAAE;YAClC,OAAe,eAAe,CAAC,WAAW;QAC7C;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,cAAc;YAAC;YAAK;YAAK;YAAK;YAAK;SAAI,EAAE,8BAA8B;QAC7E,UAAU,WAAW,CAAC,cAAc,YAAY,MAAM,CAAC,EAAE;IAC3D;IAEA,MAAM,kBAAkB;QACtB,uCAAuC;QACvC,UAAU,KAAK;QACf,WAAW,IAAM,UAAU,KAAK,OAAO;QACvC,WAAW,IAAM,UAAU,KAAK,OAAO;IACzC;IAEA,MAAM,kBAAkB;QACtB,mCAAmC;QACnC,MAAM,WAAW;YAAC;YAAK;YAAK;YAAK;YAAK;SAAI;QAC1C,SAAS,OAAO,CAAC,CAAC,MAAM;YACtB,WAAW,IAAM,UAAU,MAAM,MAAM,QAAQ;QACjD;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 2924, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/MysticInteractiveGame.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useCallback, useRef } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { Canvas, useFrame } from \"@react-three/fiber\";\nimport { Float, Text3D, Environment } from \"@react-three/drei\";\nimport * as THREE from \"three\";\nimport { MysticSoundscape, useMysticSounds } from \"./MysticSoundscape\";\n\ninterface GameState {\n  phase: \"awakening\" | \"exploration\" | \"ritual\" | \"transcendence\" | \"void\";\n  level: number;\n  souls: number;\n  whispers: string[];\n  activeLetters: boolean[];\n  mysticalEnergy: number;\n  darkSecrets: string[];\n  isChanneling: boolean;\n  voidDepth: number;\n}\n\ninterface MysticLetterProps {\n  letter: string;\n  position: [number, number, number];\n  index: number;\n  isActive: boolean;\n  isChanneling: boolean;\n  mysticalEnergy: number;\n  onClick: () => void;\n}\n\nfunction MysticLetter({\n  letter,\n  position,\n  index,\n  isActive,\n  isChanneling,\n  mysticalEnergy,\n  onClick,\n}: MysticLetterProps) {\n  const meshRef = useRef<THREE.Mesh>(null);\n  const [isHovered, setIsHovered] = useState(false);\n\n  useFrame((state) => {\n    if (meshRef.current) {\n      // Animation mystique de base\n      const time = state.clock.elapsedTime;\n      meshRef.current.position.y =\n        position[1] + Math.sin(time + index * 2) * 0.3;\n\n      // Rotation mystique\n      meshRef.current.rotation.x = Math.sin(time * 0.5 + index) * 0.2;\n      meshRef.current.rotation.z = Math.cos(time * 0.3 + index) * 0.1;\n\n      if (isChanneling) {\n        // Animation de canalisation d'énergie\n        meshRef.current.rotation.y += 0.05;\n        meshRef.current.scale.setScalar(1.5 + Math.sin(time * 8) * 0.3);\n\n        // Effet de pulsation mystique\n        const intensity = 1 + Math.sin(time * 10) * 0.5;\n        meshRef.current.material.emissive.setRGB(\n          intensity * 0.3,\n          intensity * 0.1,\n          intensity * 0.8\n        );\n      } else if (isActive) {\n        meshRef.current.rotation.y += 0.02;\n        meshRef.current.scale.setScalar(1.2 + Math.sin(time * 4) * 0.1);\n\n        // Lueur surnaturelle\n        meshRef.current.material.emissive.setRGB(0.2, 0.05, 0.4);\n      } else if (isHovered) {\n        meshRef.current.scale.setScalar(1.1);\n        meshRef.current.material.emissive.setRGB(0.1, 0.02, 0.2);\n      } else {\n        meshRef.current.scale.setScalar(1);\n        meshRef.current.material.emissive.setRGB(0, 0, 0);\n      }\n    }\n  });\n\n  return (\n    <Float speed={3} rotationIntensity={0.8} floatIntensity={0.6}>\n      <Text3D\n        ref={meshRef}\n        font=\"/fonts/helvetiker_regular.typeface.json\"\n        size={1.8}\n        height={0.8}\n        position={position}\n        onPointerOver={() => setIsHovered(true)}\n        onPointerOut={() => setIsHovered(false)}\n        onClick={onClick}\n        curveSegments={24}\n      >\n        {letter}\n        <meshStandardMaterial\n          color={\n            isChanneling\n              ? \"#8a2be2\"\n              : isActive\n              ? \"#6a0dad\"\n              : isHovered\n              ? \"#4b0082\"\n              : \"#2e2e2e\"\n          }\n          metalness={0.9}\n          roughness={0.1}\n          emissive=\"#000000\"\n        />\n      </Text3D>\n    </Float>\n  );\n}\n\nfunction MysticParticles() {\n  const pointsRef = useRef<THREE.Points>(null);\n  const particleCount = 2000;\n\n  const positions = new Float32Array(particleCount * 3);\n  const colors = new Float32Array(particleCount * 3);\n\n  for (let i = 0; i < particleCount; i++) {\n    positions[i * 3] = (Math.random() - 0.5) * 50;\n    positions[i * 3 + 1] = (Math.random() - 0.5) * 50;\n    positions[i * 3 + 2] = (Math.random() - 0.5) * 50;\n\n    // Couleurs mystiques\n    colors[i * 3] = Math.random() * 0.5 + 0.3; // Rouge\n    colors[i * 3 + 1] = Math.random() * 0.2; // Vert\n    colors[i * 3 + 2] = Math.random() * 0.8 + 0.2; // Bleu\n  }\n\n  useFrame((state) => {\n    if (pointsRef.current) {\n      pointsRef.current.rotation.y = state.clock.elapsedTime * 0.02;\n      pointsRef.current.rotation.x = state.clock.elapsedTime * 0.01;\n    }\n  });\n\n  return (\n    <points ref={pointsRef}>\n      <bufferGeometry>\n        <bufferAttribute\n          attach=\"attributes-position\"\n          count={particleCount}\n          array={positions}\n          itemSize={3}\n        />\n        <bufferAttribute\n          attach=\"attributes-color\"\n          count={particleCount}\n          array={colors}\n          itemSize={3}\n        />\n      </bufferGeometry>\n      <pointsMaterial\n        size={0.05}\n        vertexColors\n        transparent\n        opacity={0.8}\n        sizeAttenuation\n        blending={THREE.AdditiveBlending}\n      />\n    </points>\n  );\n}\n\ninterface MysticInteractiveGameProps {\n  onGameComplete?: (souls: number) => void;\n  onSecretUnlocked?: (secret: string) => void;\n}\n\nexport function MysticInteractiveGame({\n  onGameComplete,\n  onSecretUnlocked,\n}: MysticInteractiveGameProps) {\n  const [gameState, setGameState] = useState<GameState>({\n    phase: \"awakening\",\n    level: 1,\n    souls: 0,\n    whispers: [],\n    activeLetters: [false, false, false, false, false],\n    mysticalEnergy: 0,\n    darkSecrets: [],\n    isChanneling: false,\n    voidDepth: 0,\n  });\n\n  const [currentWhisper, setCurrentWhisper] = useState(\"\");\n  const [isMobile, setIsMobile] = useState(false);\n  const { playLetterSound, playSecretSound, playRitualSound } =\n    useMysticSounds();\n\n  const letters = [\"h\", \"o\", \"w\", \"r\", \"u\"];\n  const positions: [number, number, number][] = isMobile\n    ? [\n        [-2, 2, 0],\n        [0, 1, 0],\n        [2, 0, 0],\n        [-1, -1, 0],\n        [1, -2, 0],\n      ]\n    : [\n        [-6, 0, 0],\n        [-3, 0, 0],\n        [0, 0, 0],\n        [3, 0, 0],\n        [6, 0, 0],\n      ];\n\n  const whispers = [\n    \"les âmes murmurent dans l'obscurité...\",\n    \"tu entends les échos du vide...\",\n    \"les lettres révèlent leurs secrets...\",\n    \"l'énergie mystique s'éveille...\",\n    \"les ombres dansent autour de toi...\",\n    \"le rituel commence...\",\n    \"tu touches l'essence de l'inconnu...\",\n    \"les dimensions se plient à ta volonté...\",\n    \"tu transcendes la réalité...\",\n    \"le vide t'appelle...\",\n  ];\n\n  const darkSecrets = [\n    \"La première lettre cache l'origine de tout\",\n    \"L'ordre des lettres révèle le chemin vers l'au-delà\",\n    \"Cinq âmes sont nécessaires pour ouvrir le portail\",\n    \"Le vide n'est que le début de l'infini\",\n    \"Chaque clic libère une parcelle d'éternité\",\n  ];\n\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n\n    checkMobile();\n    window.addEventListener(\"resize\", checkMobile);\n    return () => window.removeEventListener(\"resize\", checkMobile);\n  }, []);\n\n  const handleLetterClick = useCallback(\n    (index: number) => {\n      if (gameState.isChanneling) return;\n\n      // Jouer le son de la lettre\n      playLetterSound(index);\n\n      const newActiveLetters = [...gameState.activeLetters];\n      newActiveLetters[index] = !newActiveLetters[index];\n\n      const activeCount = newActiveLetters.filter(Boolean).length;\n      const newMysticalEnergy = Math.min(gameState.mysticalEnergy + 20, 100);\n      const newSouls = gameState.souls + (newActiveLetters[index] ? 1 : 0);\n\n      // Nouveau murmure aléatoire\n      const randomWhisper =\n        whispers[Math.floor(Math.random() * whispers.length)];\n      setCurrentWhisper(randomWhisper);\n\n      // Progression des phases\n      let newPhase = gameState.phase;\n      if (activeCount >= 3 && gameState.phase === \"awakening\") {\n        newPhase = \"exploration\";\n      } else if (activeCount >= 5 && gameState.phase === \"exploration\") {\n        newPhase = \"ritual\";\n        playRitualSound(); // Son de rituel\n        setGameState((prev) => ({ ...prev, isChanneling: true }));\n        setTimeout(() => {\n          setGameState((prev) => ({\n            ...prev,\n            isChanneling: false,\n            phase: \"transcendence\",\n          }));\n        }, 3000);\n      }\n\n      // Révélation de secrets\n      if (\n        newSouls > 0 &&\n        newSouls % 5 === 0 &&\n        gameState.darkSecrets.length < darkSecrets.length\n      ) {\n        const newSecret = darkSecrets[gameState.darkSecrets.length];\n        playSecretSound(); // Son spécial pour les secrets\n        setGameState((prev) => ({\n          ...prev,\n          darkSecrets: [...prev.darkSecrets, newSecret],\n        }));\n        onSecretUnlocked?.(newSecret);\n      }\n\n      setGameState((prev) => ({\n        ...prev,\n        activeLetters: newActiveLetters,\n        mysticalEnergy: newMysticalEnergy,\n        souls: newSouls,\n        phase: newPhase,\n        whispers: [...prev.whispers.slice(-4), randomWhisper],\n      }));\n\n      // Completion du jeu\n      if (newSouls >= 25) {\n        onGameComplete?.(newSouls);\n      }\n    },\n    [\n      gameState,\n      onGameComplete,\n      onSecretUnlocked,\n      playLetterSound,\n      playSecretSound,\n      playRitualSound,\n    ]\n  );\n\n  return (\n    <div className=\"w-full h-screen relative bg-black overflow-hidden\">\n      {/* Soundscape mystique */}\n      <MysticSoundscape\n        isActive={true}\n        intensity={gameState.mysticalEnergy / 100}\n        phase={gameState.phase}\n      />\n\n      {/* Canvas 3D */}\n      <Canvas\n        camera={{\n          position: isMobile ? [0, 0, 10] : [0, 0, 18],\n          fov: isMobile ? 75 : 70,\n        }}\n        gl={{ antialias: true, alpha: true }}\n        style={{ width: \"100%\", height: \"100%\" }}\n      >\n        <ambientLight intensity={0.1} />\n        <pointLight position={[10, 10, 10]} intensity={0.8} color=\"#8a2be2\" />\n        <pointLight\n          position={[-10, -10, -10]}\n          intensity={0.5}\n          color=\"#4b0082\"\n        />\n        <spotLight\n          position={[0, 20, 10]}\n          angle={0.5}\n          penumbra={1}\n          intensity={1}\n          color=\"#6a0dad\"\n          castShadow\n        />\n\n        <Environment preset=\"night\" />\n        <MysticParticles />\n\n        {letters.map((letter, index) => (\n          <MysticLetter\n            key={letter}\n            letter={letter}\n            position={positions[index]}\n            index={index}\n            isActive={gameState.activeLetters[index]}\n            isChanneling={gameState.isChanneling}\n            mysticalEnergy={gameState.mysticalEnergy}\n            onClick={() => handleLetterClick(index)}\n          />\n        ))}\n      </Canvas>\n\n      {/* Interface mystique overlay */}\n      <div className=\"absolute inset-0 pointer-events-none z-10\">\n        {/* Phase indicator */}\n        <motion.div\n          className=\"absolute top-8 left-8 text-purple-300\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n        >\n          <div className=\"text-sm font-mono mb-2\">Phase: {gameState.phase}</div>\n          <div className=\"text-xs\">Âmes: {gameState.souls}</div>\n          <div className=\"text-xs\">Énergie: {gameState.mysticalEnergy}%</div>\n        </motion.div>\n\n        {/* Whispers */}\n        <AnimatePresence>\n          {currentWhisper && (\n            <motion.div\n              key={currentWhisper}\n              className=\"absolute bottom-20 left-1/2 transform -translate-x-1/2 text-center\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 2 }}\n            >\n              <div className=\"text-purple-200 text-sm font-light italic\">\n                {currentWhisper}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        {/* Dark secrets */}\n        {gameState.darkSecrets.length > 0 && (\n          <motion.div\n            className=\"absolute top-8 right-8 max-w-xs\"\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n          >\n            <div className=\"text-purple-300 text-xs font-mono mb-2\">\n              Secrets révélés:\n            </div>\n            {gameState.darkSecrets.map((secret, index) => (\n              <motion.div\n                key={index}\n                className=\"text-purple-200 text-xs mb-1 opacity-80\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 0.8 }}\n                transition={{ delay: index * 0.5 }}\n              >\n                • {secret}\n              </motion.div>\n            ))}\n          </motion.div>\n        )}\n\n        {/* Channeling effect */}\n        {gameState.isChanneling && (\n          <motion.div\n            className=\"absolute inset-0 bg-purple-900 bg-opacity-30\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: [0, 0.5, 0] }}\n            transition={{ duration: 3, repeat: Infinity }}\n          >\n            <div className=\"absolute inset-0 flex items-center justify-center\">\n              <motion.div\n                className=\"text-purple-100 text-2xl font-light\"\n                animate={{ scale: [1, 1.2, 1] }}\n                transition={{ duration: 1, repeat: Infinity }}\n              >\n                Canalisation en cours...\n              </motion.div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Instructions */}\n        <motion.div\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center pointer-events-none\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 2 }}\n        >\n          <div className=\"text-purple-300 text-xs font-light\">\n            Cliquez sur les lettres pour éveiller leur pouvoir mystique\n          </div>\n          <div className=\"text-purple-400 text-xs font-light mt-1\">\n            Collectez des âmes pour révéler les secrets de l'au-delà\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AA+BA,SAAS,aAAa,EACpB,MAAM,EACN,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,cAAc,EACd,OAAO,EACW;IAClB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,QAAQ,OAAO,EAAE;YACnB,6BAA6B;YAC7B,MAAM,OAAO,MAAM,KAAK,CAAC,WAAW;YACpC,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GACxB,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,OAAO,QAAQ,KAAK;YAE7C,oBAAoB;YACpB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,MAAM,SAAS;YAC5D,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,MAAM,SAAS;YAE5D,IAAI,cAAc;gBAChB,sCAAsC;gBACtC,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI;gBAC9B,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG,CAAC,OAAO,KAAK;gBAE3D,8BAA8B;gBAC9B,MAAM,YAAY,IAAI,KAAK,GAAG,CAAC,OAAO,MAAM;gBAC5C,QAAQ,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CACtC,YAAY,KACZ,YAAY,KACZ,YAAY;YAEhB,OAAO,IAAI,UAAU;gBACnB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI;gBAC9B,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG,CAAC,OAAO,KAAK;gBAE3D,qBAAqB;gBACrB,QAAQ,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,MAAM;YACtD,OAAO,IAAI,WAAW;gBACpB,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;gBAChC,QAAQ,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,MAAM;YACtD,OAAO;gBACL,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;gBAChC,QAAQ,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,GAAG;YACjD;QACF;IACF;IAEA,qBACE,8OAAC,yJAAA,CAAA,QAAK;QAAC,OAAO;QAAG,mBAAmB;QAAK,gBAAgB;kBACvD,cAAA,8OAAC,0JAAA,CAAA,SAAM;YACL,KAAK;YACL,MAAK;YACL,MAAM;YACN,QAAQ;YACR,UAAU;YACV,eAAe,IAAM,aAAa;YAClC,cAAc,IAAM,aAAa;YACjC,SAAS;YACT,eAAe;;gBAEd;8BACD,8OAAC;oBACC,OACE,eACI,YACA,WACA,YACA,YACA,YACA;oBAEN,WAAW;oBACX,WAAW;oBACX,UAAS;;;;;;;;;;;;;;;;;AAKnB;AAEA,SAAS;IACP,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgB;IACvC,MAAM,gBAAgB;IAEtB,MAAM,YAAY,IAAI,aAAa,gBAAgB;IACnD,MAAM,SAAS,IAAI,aAAa,gBAAgB;IAEhD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;QACtC,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAC3C,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAC/C,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAE/C,qBAAqB;QACrB,MAAM,CAAC,IAAI,EAAE,GAAG,KAAK,MAAM,KAAK,MAAM,KAAK,QAAQ;QACnD,MAAM,CAAC,IAAI,IAAI,EAAE,GAAG,KAAK,MAAM,KAAK,KAAK,OAAO;QAChD,MAAM,CAAC,IAAI,IAAI,EAAE,GAAG,KAAK,MAAM,KAAK,MAAM,KAAK,OAAO;IACxD;IAEA,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACzD,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QAC3D;IACF;IAEA,qBACE,8OAAC;QAAO,KAAK;;0BACX,8OAAC;;kCACC,8OAAC;wBACC,QAAO;wBACP,OAAO;wBACP,OAAO;wBACP,UAAU;;;;;;kCAEZ,8OAAC;wBACC,QAAO;wBACP,OAAO;wBACP,OAAO;wBACP,UAAU;;;;;;;;;;;;0BAGd,8OAAC;gBACC,MAAM;gBACN,YAAY;gBACZ,WAAW;gBACX,SAAS;gBACT,eAAe;gBACf,UAAU,+IAAA,CAAA,mBAAsB;;;;;;;;;;;;AAIxC;AAOO,SAAS,sBAAsB,EACpC,cAAc,EACd,gBAAgB,EACW;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,OAAO;QACP,OAAO;QACP,OAAO;QACP,UAAU,EAAE;QACZ,eAAe;YAAC;YAAO;YAAO;YAAO;YAAO;SAAM;QAClD,gBAAgB;QAChB,aAAa,EAAE;QACf,cAAc;QACd,WAAW;IACb;IAEA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,GACzD,CAAA,GAAA,sIAAA,CAAA,kBAAe,AAAD;IAEhB,MAAM,UAAU;QAAC;QAAK;QAAK;QAAK;QAAK;KAAI;IACzC,MAAM,YAAwC,WAC1C;QACE;YAAC,CAAC;YAAG;YAAG;SAAE;QACV;YAAC;YAAG;YAAG;SAAE;QACT;YAAC;YAAG;YAAG;SAAE;QACT;YAAC,CAAC;YAAG,CAAC;YAAG;SAAE;QACX;YAAC;YAAG,CAAC;YAAG;SAAE;KACX,GACD;QACE;YAAC,CAAC;YAAG;YAAG;SAAE;QACV;YAAC,CAAC;YAAG;YAAG;SAAE;QACV;YAAC;YAAG;YAAG;SAAE;QACT;YAAC;YAAG;YAAG;SAAE;QACT;YAAC;YAAG;YAAG;SAAE;KACV;IAEL,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,YAAY,OAAO,UAAU,GAAG;QAClC;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,IAAI,UAAU,YAAY,EAAE;QAE5B,4BAA4B;QAC5B,gBAAgB;QAEhB,MAAM,mBAAmB;eAAI,UAAU,aAAa;SAAC;QACrD,gBAAgB,CAAC,MAAM,GAAG,CAAC,gBAAgB,CAAC,MAAM;QAElD,MAAM,cAAc,iBAAiB,MAAM,CAAC,SAAS,MAAM;QAC3D,MAAM,oBAAoB,KAAK,GAAG,CAAC,UAAU,cAAc,GAAG,IAAI;QAClE,MAAM,WAAW,UAAU,KAAK,GAAG,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC;QAEnE,4BAA4B;QAC5B,MAAM,gBACJ,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;QACvD,kBAAkB;QAElB,yBAAyB;QACzB,IAAI,WAAW,UAAU,KAAK;QAC9B,IAAI,eAAe,KAAK,UAAU,KAAK,KAAK,aAAa;YACvD,WAAW;QACb,OAAO,IAAI,eAAe,KAAK,UAAU,KAAK,KAAK,eAAe;YAChE,WAAW;YACX,mBAAmB,gBAAgB;YACnC,aAAa,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,cAAc;gBAAK,CAAC;YACvD,WAAW;gBACT,aAAa,CAAC,OAAS,CAAC;wBACtB,GAAG,IAAI;wBACP,cAAc;wBACd,OAAO;oBACT,CAAC;YACH,GAAG;QACL;QAEA,wBAAwB;QACxB,IACE,WAAW,KACX,WAAW,MAAM,KACjB,UAAU,WAAW,CAAC,MAAM,GAAG,YAAY,MAAM,EACjD;YACA,MAAM,YAAY,WAAW,CAAC,UAAU,WAAW,CAAC,MAAM,CAAC;YAC3D,mBAAmB,+BAA+B;YAClD,aAAa,CAAC,OAAS,CAAC;oBACtB,GAAG,IAAI;oBACP,aAAa;2BAAI,KAAK,WAAW;wBAAE;qBAAU;gBAC/C,CAAC;YACD,mBAAmB;QACrB;QAEA,aAAa,CAAC,OAAS,CAAC;gBACtB,GAAG,IAAI;gBACP,eAAe;gBACf,gBAAgB;gBAChB,OAAO;gBACP,OAAO;gBACP,UAAU;uBAAI,KAAK,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAAI;iBAAc;YACvD,CAAC;QAED,oBAAoB;QACpB,IAAI,YAAY,IAAI;YAClB,iBAAiB;QACnB;IACF,GACA;QACE;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,sIAAA,CAAA,mBAAgB;gBACf,UAAU;gBACV,WAAW,UAAU,cAAc,GAAG;gBACtC,OAAO,UAAU,KAAK;;;;;;0BAIxB,8OAAC,mMAAA,CAAA,SAAM;gBACL,QAAQ;oBACN,UAAU,WAAW;wBAAC;wBAAG;wBAAG;qBAAG,GAAG;wBAAC;wBAAG;wBAAG;qBAAG;oBAC5C,KAAK,WAAW,KAAK;gBACvB;gBACA,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBACnC,OAAO;oBAAE,OAAO;oBAAQ,QAAQ;gBAAO;;kCAEvC,8OAAC;wBAAa,WAAW;;;;;;kCACzB,8OAAC;wBAAW,UAAU;4BAAC;4BAAI;4BAAI;yBAAG;wBAAE,WAAW;wBAAK,OAAM;;;;;;kCAC1D,8OAAC;wBACC,UAAU;4BAAC,CAAC;4BAAI,CAAC;4BAAI,CAAC;yBAAG;wBACzB,WAAW;wBACX,OAAM;;;;;;kCAER,8OAAC;wBACC,UAAU;4BAAC;4BAAG;4BAAI;yBAAG;wBACrB,OAAO;wBACP,UAAU;wBACV,WAAW;wBACX,OAAM;wBACN,UAAU;;;;;;kCAGZ,8OAAC,+JAAA,CAAA,cAAW;wBAAC,QAAO;;;;;;kCACpB,8OAAC;;;;;oBAEA,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;4BAEC,QAAQ;4BACR,UAAU,SAAS,CAAC,MAAM;4BAC1B,OAAO;4BACP,UAAU,UAAU,aAAa,CAAC,MAAM;4BACxC,cAAc,UAAU,YAAY;4BACpC,gBAAgB,UAAU,cAAc;4BACxC,SAAS,IAAM,kBAAkB;2BAP5B;;;;;;;;;;;0BAaX,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;;0CAEtB,8OAAC;gCAAI,WAAU;;oCAAyB;oCAAQ,UAAU,KAAK;;;;;;;0CAC/D,8OAAC;gCAAI,WAAU;;oCAAU;oCAAO,UAAU,KAAK;;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;;oCAAU;oCAAU,UAAU,cAAc;oCAAC;;;;;;;;;;;;;kCAI9D,8OAAC,yLAAA,CAAA,kBAAe;kCACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,YAAY;gCAAE,UAAU;4BAAE;sCAE1B,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;2BARE;;;;;;;;;;oBAeV,UAAU,WAAW,CAAC,MAAM,GAAG,mBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;;0CAE5B,8OAAC;gCAAI,WAAU;0CAAyC;;;;;;4BAGvD,UAAU,WAAW,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAClC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAI;oCACxB,YAAY;wCAAE,OAAO,QAAQ;oCAAI;;wCAClC;wCACI;;mCANE;;;;;;;;;;;oBAaZ,UAAU,YAAY,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;gCAAC;gCAAG;gCAAK;6BAAE;wBAAC;wBAChC,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;kCAE5C,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;gCAAC;gCAC9B,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;0CAC7C;;;;;;;;;;;;;;;;kCAQP,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAE;;0CAEvB,8OAAC;gCAAI,WAAU;0CAAqC;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;;;AAOnE", "debugId": null}}, {"offset": {"line": 3610, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/MysticAtmosphere.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useRef, useState } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\n\ninterface MysticAtmosphereProps {\n  intensity?: number;\n  showRitualCircle?: boolean;\n  isChanneling?: boolean;\n}\n\nexport function MysticAtmosphere({ \n  intensity = 1, \n  showRitualCircle = false,\n  isChanneling = false \n}: MysticAtmosphereProps) {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });\n\n  useEffect(() => {\n    const updateDimensions = () => {\n      setDimensions({\n        width: window.innerWidth,\n        height: window.innerHeight,\n      });\n    };\n\n    updateDimensions();\n    window.addEventListener(\"resize\", updateDimensions);\n    return () => window.removeEventListener(\"resize\", updateDimensions);\n  }, []);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext(\"2d\");\n    if (!ctx) return;\n\n    canvas.width = dimensions.width;\n    canvas.height = dimensions.height;\n\n    // Particules mystiques\n    const particles: Array<{\n      x: number;\n      y: number;\n      vx: number;\n      vy: number;\n      size: number;\n      opacity: number;\n      color: string;\n      life: number;\n    }> = [];\n\n    const colors = [\n      \"rgba(138, 43, 226, \",\n      \"rgba(106, 13, 173, \",\n      \"rgba(75, 0, 130, \",\n      \"rgba(148, 0, 211, \",\n      \"rgba(186, 85, 211, \",\n    ];\n\n    // Créer des particules\n    for (let i = 0; i < 150 * intensity; i++) {\n      particles.push({\n        x: Math.random() * dimensions.width,\n        y: Math.random() * dimensions.height,\n        vx: (Math.random() - 0.5) * 0.5,\n        vy: (Math.random() - 0.5) * 0.5,\n        size: Math.random() * 3 + 1,\n        opacity: Math.random() * 0.8 + 0.2,\n        color: colors[Math.floor(Math.random() * colors.length)],\n        life: Math.random() * 100 + 50,\n      });\n    }\n\n    let animationId: number;\n\n    const animate = () => {\n      ctx.clearRect(0, 0, dimensions.width, dimensions.height);\n\n      // Fond mystique avec gradient\n      const gradient = ctx.createRadialGradient(\n        dimensions.width / 2,\n        dimensions.height / 2,\n        0,\n        dimensions.width / 2,\n        dimensions.height / 2,\n        Math.max(dimensions.width, dimensions.height) / 2\n      );\n      gradient.addColorStop(0, \"rgba(25, 0, 50, 0.1)\");\n      gradient.addColorStop(0.5, \"rgba(50, 0, 100, 0.05)\");\n      gradient.addColorStop(1, \"rgba(0, 0, 0, 0.2)\");\n      \n      ctx.fillStyle = gradient;\n      ctx.fillRect(0, 0, dimensions.width, dimensions.height);\n\n      // Animer les particules\n      particles.forEach((particle, index) => {\n        particle.x += particle.vx;\n        particle.y += particle.vy;\n        particle.life--;\n\n        // Effet de canalisation\n        if (isChanneling) {\n          const centerX = dimensions.width / 2;\n          const centerY = dimensions.height / 2;\n          const dx = centerX - particle.x;\n          const dy = centerY - particle.y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n          \n          if (distance > 50) {\n            particle.vx += dx * 0.0001;\n            particle.vy += dy * 0.0001;\n          }\n          \n          particle.opacity = Math.min(1, particle.opacity + 0.02);\n          particle.size = Math.min(5, particle.size + 0.05);\n        }\n\n        // Rebond sur les bords\n        if (particle.x < 0 || particle.x > dimensions.width) particle.vx *= -1;\n        if (particle.y < 0 || particle.y > dimensions.height) particle.vy *= -1;\n\n        // Dessiner la particule\n        ctx.save();\n        ctx.globalAlpha = particle.opacity;\n        ctx.fillStyle = particle.color + particle.opacity + \")\";\n        ctx.beginPath();\n        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n        ctx.fill();\n        \n        // Effet de lueur\n        ctx.shadowBlur = 20;\n        ctx.shadowColor = particle.color + \"0.8)\";\n        ctx.fill();\n        ctx.restore();\n\n        // Régénérer les particules mortes\n        if (particle.life <= 0) {\n          particles[index] = {\n            x: Math.random() * dimensions.width,\n            y: Math.random() * dimensions.height,\n            vx: (Math.random() - 0.5) * 0.5,\n            vy: (Math.random() - 0.5) * 0.5,\n            size: Math.random() * 3 + 1,\n            opacity: Math.random() * 0.8 + 0.2,\n            color: colors[Math.floor(Math.random() * colors.length)],\n            life: Math.random() * 100 + 50,\n          };\n        }\n      });\n\n      // Cercle rituel\n      if (showRitualCircle) {\n        const centerX = dimensions.width / 2;\n        const centerY = dimensions.height / 2;\n        const radius = Math.min(dimensions.width, dimensions.height) * 0.3;\n        \n        ctx.save();\n        ctx.strokeStyle = \"rgba(138, 43, 226, 0.6)\";\n        ctx.lineWidth = 2;\n        ctx.setLineDash([10, 5]);\n        ctx.lineDashOffset = Date.now() * 0.01;\n        \n        ctx.beginPath();\n        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);\n        ctx.stroke();\n        \n        // Symboles mystiques autour du cercle\n        const symbols = [\"✦\", \"✧\", \"✩\", \"✪\", \"✫\", \"✬\"];\n        for (let i = 0; i < 6; i++) {\n          const angle = (i / 6) * Math.PI * 2;\n          const x = centerX + Math.cos(angle) * (radius + 30);\n          const y = centerY + Math.sin(angle) * (radius + 30);\n          \n          ctx.fillStyle = \"rgba(186, 85, 211, 0.8)\";\n          ctx.font = \"20px serif\";\n          ctx.textAlign = \"center\";\n          ctx.fillText(symbols[i], x, y);\n        }\n        \n        ctx.restore();\n      }\n\n      animationId = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    return () => {\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, [dimensions, intensity, showRitualCircle, isChanneling]);\n\n  return (\n    <>\n      {/* Canvas pour les effets de particules */}\n      <canvas\n        ref={canvasRef}\n        className=\"fixed inset-0 pointer-events-none z-0\"\n        style={{ mixBlendMode: \"screen\" }}\n      />\n\n      {/* Effets de lueur mystique */}\n      <div className=\"fixed inset-0 pointer-events-none z-0\">\n        <div\n          className=\"absolute inset-0 opacity-30\"\n          style={{\n            background: `\n              radial-gradient(circle at 20% 20%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),\n              radial-gradient(circle at 80% 80%, rgba(106, 13, 173, 0.1) 0%, transparent 50%),\n              radial-gradient(circle at 40% 60%, rgba(75, 0, 130, 0.05) 0%, transparent 50%)\n            `,\n            animation: \"mysticPulse 8s ease-in-out infinite\",\n          }}\n        />\n      </div>\n\n      {/* Voiles mystiques */}\n      <AnimatePresence>\n        {isChanneling && (\n          <motion.div\n            className=\"fixed inset-0 pointer-events-none z-0\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 2 }}\n          >\n            <div\n              className=\"absolute inset-0\"\n              style={{\n                background: `\n                  conic-gradient(from 0deg at 50% 50%, \n                    rgba(138, 43, 226, 0.1) 0deg,\n                    rgba(106, 13, 173, 0.05) 60deg,\n                    rgba(75, 0, 130, 0.1) 120deg,\n                    rgba(148, 0, 211, 0.05) 180deg,\n                    rgba(186, 85, 211, 0.1) 240deg,\n                    rgba(138, 43, 226, 0.05) 300deg,\n                    rgba(138, 43, 226, 0.1) 360deg\n                  )\n                `,\n                animation: \"mysticRotate 10s linear infinite\",\n              }}\n            />\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Brouillard mystique */}\n      <div className=\"fixed inset-0 pointer-events-none z-0 opacity-20\">\n        <div\n          className=\"absolute inset-0\"\n          style={{\n            background: `\n              linear-gradient(45deg, \n                rgba(25, 0, 50, 0.3) 0%,\n                transparent 25%,\n                rgba(50, 0, 100, 0.2) 50%,\n                transparent 75%,\n                rgba(25, 0, 50, 0.3) 100%\n              )\n            `,\n            backgroundSize: \"200% 200%\",\n            animation: \"mysticFog 15s ease-in-out infinite\",\n          }}\n        />\n      </div>\n\n      <style jsx>{`\n        @keyframes mysticPulse {\n          0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.3; }\n          50% { transform: scale(1.1) rotate(180deg); opacity: 0.6; }\n        }\n        \n        @keyframes mysticRotate {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n        \n        @keyframes mysticFog {\n          0%, 100% { background-position: 0% 0%; }\n          25% { background-position: 100% 0%; }\n          50% { background-position: 100% 100%; }\n          75% { background-position: 0% 100%; }\n        }\n      `}</style>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAHA;;;;;AAWO,SAAS,iBAAiB,EAC/B,YAAY,CAAC,EACb,mBAAmB,KAAK,EACxB,eAAe,KAAK,EACE;IACtB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,OAAO;QAAG,QAAQ;IAAE;IAEnE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,cAAc;gBACZ,OAAO,OAAO,UAAU;gBACxB,QAAQ,OAAO,WAAW;YAC5B;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,OAAO,KAAK,GAAG,WAAW,KAAK;QAC/B,OAAO,MAAM,GAAG,WAAW,MAAM;QAEjC,uBAAuB;QACvB,MAAM,YASD,EAAE;QAEP,MAAM,SAAS;YACb;YACA;YACA;YACA;YACA;SACD;QAED,uBAAuB;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,WAAW,IAAK;YACxC,UAAU,IAAI,CAAC;gBACb,GAAG,KAAK,MAAM,KAAK,WAAW,KAAK;gBACnC,GAAG,KAAK,MAAM,KAAK,WAAW,MAAM;gBACpC,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC5B,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC5B,MAAM,KAAK,MAAM,KAAK,IAAI;gBAC1B,SAAS,KAAK,MAAM,KAAK,MAAM;gBAC/B,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;gBACxD,MAAM,KAAK,MAAM,KAAK,MAAM;YAC9B;QACF;QAEA,IAAI;QAEJ,MAAM,UAAU;YACd,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW,KAAK,EAAE,WAAW,MAAM;YAEvD,8BAA8B;YAC9B,MAAM,WAAW,IAAI,oBAAoB,CACvC,WAAW,KAAK,GAAG,GACnB,WAAW,MAAM,GAAG,GACpB,GACA,WAAW,KAAK,GAAG,GACnB,WAAW,MAAM,GAAG,GACpB,KAAK,GAAG,CAAC,WAAW,KAAK,EAAE,WAAW,MAAM,IAAI;YAElD,SAAS,YAAY,CAAC,GAAG;YACzB,SAAS,YAAY,CAAC,KAAK;YAC3B,SAAS,YAAY,CAAC,GAAG;YAEzB,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,WAAW,KAAK,EAAE,WAAW,MAAM;YAEtD,wBAAwB;YACxB,UAAU,OAAO,CAAC,CAAC,UAAU;gBAC3B,SAAS,CAAC,IAAI,SAAS,EAAE;gBACzB,SAAS,CAAC,IAAI,SAAS,EAAE;gBACzB,SAAS,IAAI;gBAEb,wBAAwB;gBACxB,IAAI,cAAc;oBAChB,MAAM,UAAU,WAAW,KAAK,GAAG;oBACnC,MAAM,UAAU,WAAW,MAAM,GAAG;oBACpC,MAAM,KAAK,UAAU,SAAS,CAAC;oBAC/B,MAAM,KAAK,UAAU,SAAS,CAAC;oBAC/B,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;oBAE1C,IAAI,WAAW,IAAI;wBACjB,SAAS,EAAE,IAAI,KAAK;wBACpB,SAAS,EAAE,IAAI,KAAK;oBACtB;oBAEA,SAAS,OAAO,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS,OAAO,GAAG;oBAClD,SAAS,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS,IAAI,GAAG;gBAC9C;gBAEA,uBAAuB;gBACvB,IAAI,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,WAAW,KAAK,EAAE,SAAS,EAAE,IAAI,CAAC;gBACrE,IAAI,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,WAAW,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC;gBAEtE,wBAAwB;gBACxB,IAAI,IAAI;gBACR,IAAI,WAAW,GAAG,SAAS,OAAO;gBAClC,IAAI,SAAS,GAAG,SAAS,KAAK,GAAG,SAAS,OAAO,GAAG;gBACpD,IAAI,SAAS;gBACb,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;gBAC5D,IAAI,IAAI;gBAER,iBAAiB;gBACjB,IAAI,UAAU,GAAG;gBACjB,IAAI,WAAW,GAAG,SAAS,KAAK,GAAG;gBACnC,IAAI,IAAI;gBACR,IAAI,OAAO;gBAEX,kCAAkC;gBAClC,IAAI,SAAS,IAAI,IAAI,GAAG;oBACtB,SAAS,CAAC,MAAM,GAAG;wBACjB,GAAG,KAAK,MAAM,KAAK,WAAW,KAAK;wBACnC,GAAG,KAAK,MAAM,KAAK,WAAW,MAAM;wBACpC,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBAC5B,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBAC5B,MAAM,KAAK,MAAM,KAAK,IAAI;wBAC1B,SAAS,KAAK,MAAM,KAAK,MAAM;wBAC/B,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;wBACxD,MAAM,KAAK,MAAM,KAAK,MAAM;oBAC9B;gBACF;YACF;YAEA,gBAAgB;YAChB,IAAI,kBAAkB;gBACpB,MAAM,UAAU,WAAW,KAAK,GAAG;gBACnC,MAAM,UAAU,WAAW,MAAM,GAAG;gBACpC,MAAM,SAAS,KAAK,GAAG,CAAC,WAAW,KAAK,EAAE,WAAW,MAAM,IAAI;gBAE/D,IAAI,IAAI;gBACR,IAAI,WAAW,GAAG;gBAClB,IAAI,SAAS,GAAG;gBAChB,IAAI,WAAW,CAAC;oBAAC;oBAAI;iBAAE;gBACvB,IAAI,cAAc,GAAG,KAAK,GAAG,KAAK;gBAElC,IAAI,SAAS;gBACb,IAAI,GAAG,CAAC,SAAS,SAAS,QAAQ,GAAG,KAAK,EAAE,GAAG;gBAC/C,IAAI,MAAM;gBAEV,sCAAsC;gBACtC,MAAM,UAAU;oBAAC;oBAAK;oBAAK;oBAAK;oBAAK;oBAAK;iBAAI;gBAC9C,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;oBAC1B,MAAM,QAAQ,AAAC,IAAI,IAAK,KAAK,EAAE,GAAG;oBAClC,MAAM,IAAI,UAAU,KAAK,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE;oBAClD,MAAM,IAAI,UAAU,KAAK,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE;oBAElD,IAAI,SAAS,GAAG;oBAChB,IAAI,IAAI,GAAG;oBACX,IAAI,SAAS,GAAG;oBAChB,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG;gBAC9B;gBAEA,IAAI,OAAO;YACb;YAEA,cAAc,sBAAsB;QACtC;QAEA;QAEA,OAAO;YACL,IAAI,aAAa;gBACf,qBAAqB;YACvB;QACF;IACF,GAAG;QAAC;QAAY;QAAW;QAAkB;KAAa;IAE1D,qBACE;;0BAEE,8OAAC;gBACC,KAAK;gBAEL,OAAO;oBAAE,cAAc;gBAAS;0DADtB;;;;;;0BAKZ,8OAAC;0DAAc;0BACb,cAAA,8OAAC;oBAEC,OAAO;wBACL,YAAY,CAAC;;;;YAIb,CAAC;wBACD,WAAW;oBACb;8DARU;;;;;;;;;;;0BAad,8OAAC,yLAAA,CAAA,kBAAe;0BACb,8BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAE;8BAE1B,cAAA,8OAAC;wBAEC,OAAO;4BACL,YAAY,CAAC;;;;;;;;;;gBAUb,CAAC;4BACD,WAAW;wBACb;kEAdU;;;;;;;;;;;;;;;;0BAqBlB,8OAAC;0DAAc;0BACb,cAAA,8OAAC;oBAEC,OAAO;wBACL,YAAY,CAAC;;;;;;;;YAQb,CAAC;wBACD,gBAAgB;wBAChB,WAAW;oBACb;8DAbU;;;;;;;;;;;;;;;;;AAqCpB", "debugId": null}}, {"offset": {"line": 3898, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/ResponsiveLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, ReactNode } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface ResponsiveLayoutProps {\n  children: ReactNode;\n  className?: string;\n}\n\nexport function ResponsiveLayout({ children, className = \"\" }: ResponsiveLayoutProps) {\n  const [isMobile, setIsMobile] = useState(false);\n  const [isTablet, setIsTablet] = useState(false);\n  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');\n\n  useEffect(() => {\n    const checkDevice = () => {\n      const width = window.innerWidth;\n      const height = window.innerHeight;\n      \n      setIsMobile(width < 768);\n      setIsTablet(width >= 768 && width < 1024);\n      setOrientation(height > width ? 'portrait' : 'landscape');\n    };\n    \n    checkDevice();\n    window.addEventListener('resize', checkDevice);\n    window.addEventListener('orientationchange', () => {\n      setTimeout(checkDevice, 100); // <PERSON><PERSON><PERSON> pour laisser le temps à l'orientation de changer\n    });\n    \n    return () => {\n      window.removeEventListener('resize', checkDevice);\n      window.removeEventListener('orientationchange', checkDevice);\n    };\n  }, []);\n\n  const getLayoutClasses = () => {\n    let classes = \"min-h-screen w-full relative overflow-hidden \";\n    \n    if (isMobile) {\n      classes += orientation === 'portrait' \n        ? \"px-4 py-6 \" \n        : \"px-6 py-4 \";\n    } else if (isTablet) {\n      classes += \"px-8 py-8 \";\n    } else {\n      classes += \"px-12 py-12 \";\n    }\n    \n    return classes + className;\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: { duration: 0.6 }\n    }\n  };\n\n  return (\n    <motion.div\n      className={getLayoutClasses()}\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      {/* Indicateur de device (dev only) */}\n      {process.env.NODE_ENV === 'development' && (\n        <div className=\"fixed top-2 right-2 z-50 text-xs bg-black/50 text-white px-2 py-1 rounded\">\n          {isMobile ? 'Mobile' : isTablet ? 'Tablet' : 'Desktop'} - {orientation}\n        </div>\n      )}\n      \n      <motion.div variants={itemVariants}>\n        {children}\n      </motion.div>\n    </motion.div>\n  );\n}\n\n// Hook personnalisé pour la responsivité\nexport function useResponsive() {\n  const [deviceInfo, setDeviceInfo] = useState({\n    isMobile: false,\n    isTablet: false,\n    isDesktop: false,\n    orientation: 'portrait' as 'portrait' | 'landscape',\n    width: 0,\n    height: 0\n  });\n\n  useEffect(() => {\n    const updateDeviceInfo = () => {\n      const width = window.innerWidth;\n      const height = window.innerHeight;\n      \n      setDeviceInfo({\n        isMobile: width < 768,\n        isTablet: width >= 768 && width < 1024,\n        isDesktop: width >= 1024,\n        orientation: height > width ? 'portrait' : 'landscape',\n        width,\n        height\n      });\n    };\n\n    updateDeviceInfo();\n    window.addEventListener('resize', updateDeviceInfo);\n    window.addEventListener('orientationchange', () => {\n      setTimeout(updateDeviceInfo, 100);\n    });\n\n    return () => {\n      window.removeEventListener('resize', updateDeviceInfo);\n      window.removeEventListener('orientationchange', updateDeviceInfo);\n    };\n  }, []);\n\n  return deviceInfo;\n}\n\n// Composant pour les contrôles adaptatifs\ninterface AdaptiveControlsProps {\n  onModeChange: (mode: string) => void;\n  currentMode: string;\n  modes: { key: string; label: string }[];\n}\n\nexport function AdaptiveControls({ onModeChange, currentMode, modes }: AdaptiveControlsProps) {\n  const { isMobile, isTablet } = useResponsive();\n\n  if (isMobile) {\n    // Version mobile : menu déroulant\n    return (\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"fixed top-4 left-4 right-4 z-20\"\n      >\n        <select\n          value={currentMode}\n          onChange={(e) => onModeChange(e.target.value)}\n          className=\"w-full bg-ghost-100 border border-whisper-200 text-whisper-300 text-sm font-light p-2 rounded\"\n        >\n          {modes.map((mode) => (\n            <option key={mode.key} value={mode.key}>\n              {mode.label}\n            </option>\n          ))}\n        </select>\n      </motion.div>\n    );\n  }\n\n  if (isTablet) {\n    // Version tablette : boutons horizontaux\n    return (\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"fixed top-4 left-4 right-4 z-20\"\n      >\n        <div className=\"flex space-x-2 justify-center\">\n          {modes.map((mode) => (\n            <button\n              key={mode.key}\n              onClick={() => onModeChange(mode.key)}\n              className={`px-3 py-1 text-xs font-light transition-colors duration-300 rounded ${\n                currentMode === mode.key\n                  ? 'text-whisper-300 bg-ghost-200 border border-whisper-300'\n                  : 'text-whisper-500 bg-ghost-100 border border-whisper-200 hover:text-whisper-400'\n              }`}\n            >\n              {mode.label}\n            </button>\n          ))}\n        </div>\n      </motion.div>\n    );\n  }\n\n  // Version desktop : menu vertical\n  return (\n    <motion.div\n      initial={{ opacity: 0, x: -20 }}\n      animate={{ opacity: 1, x: 0 }}\n      className=\"fixed top-8 left-8 space-y-2 z-20\"\n    >\n      <div className=\"text-whisper-500 text-xs font-light mb-2\">\n        mode d'affichage:\n      </div>\n      {modes.map((mode) => (\n        <button\n          key={mode.key}\n          onClick={() => onModeChange(mode.key)}\n          className={`block w-full text-left px-3 py-1 text-xs font-light transition-colors duration-300 ${\n            currentMode === mode.key\n              ? 'text-whisper-300 bg-ghost-200 border border-whisper-300'\n              : 'text-whisper-500 bg-ghost-100 border border-whisper-200 hover:text-whisper-400 hover:bg-ghost-150'\n          }`}\n        >\n          {mode.label}\n        </button>\n      ))}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAUO,SAAS,iBAAiB,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAyB;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IAEzE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,MAAM,QAAQ,OAAO,UAAU;YAC/B,MAAM,SAAS,OAAO,WAAW;YAEjC,YAAY,QAAQ;YACpB,YAAY,SAAS,OAAO,QAAQ;YACpC,eAAe,SAAS,QAAQ,aAAa;QAC/C;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,qBAAqB;YAC3C,WAAW,aAAa,MAAM,yDAAyD;QACzF;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,qBAAqB;QAClD;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI,UAAU;QAEd,IAAI,UAAU;YACZ,WAAW,gBAAgB,aACvB,eACA;QACN,OAAO,IAAI,UAAU;YACnB,WAAW;QACb,OAAO;YACL,WAAW;QACb;QAEA,OAAO,UAAU;IACnB;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU;QACV,SAAQ;QACR,SAAQ;;YAGP,oDAAyB,+BACxB,8OAAC;gBAAI,WAAU;;oBACZ,WAAW,WAAW,WAAW,WAAW;oBAAU;oBAAI;;;;;;;0BAI/D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;0BACnB;;;;;;;;;;;;AAIT;AAGO,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,UAAU;QACV,UAAU;QACV,WAAW;QACX,aAAa;QACb,OAAO;QACP,QAAQ;IACV;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,MAAM,QAAQ,OAAO,UAAU;YAC/B,MAAM,SAAS,OAAO,WAAW;YAEjC,cAAc;gBACZ,UAAU,QAAQ;gBAClB,UAAU,SAAS,OAAO,QAAQ;gBAClC,WAAW,SAAS;gBACpB,aAAa,SAAS,QAAQ,aAAa;gBAC3C;gBACA;YACF;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,qBAAqB;YAC3C,WAAW,kBAAkB;QAC/B;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,qBAAqB;QAClD;IACF,GAAG,EAAE;IAEL,OAAO;AACT;AASO,SAAS,iBAAiB,EAAE,YAAY,EAAE,WAAW,EAAE,KAAK,EAAyB;IAC1F,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;IAE/B,IAAI,UAAU;QACZ,kCAAkC;QAClC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC9B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,WAAU;sBAEV,cAAA,8OAAC;gBACC,OAAO;gBACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gBAC5C,WAAU;0BAET,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wBAAsB,OAAO,KAAK,GAAG;kCACnC,KAAK,KAAK;uBADA,KAAK,GAAG;;;;;;;;;;;;;;;IAO/B;IAEA,IAAI,UAAU;QACZ,yCAAyC;QACzC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC9B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,WAAU;sBAEV,cAAA,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wBAEC,SAAS,IAAM,aAAa,KAAK,GAAG;wBACpC,WAAW,CAAC,oEAAoE,EAC9E,gBAAgB,KAAK,GAAG,GACpB,4DACA,kFACJ;kCAED,KAAK,KAAK;uBARN,KAAK,GAAG;;;;;;;;;;;;;;;IAczB;IAEA,kCAAkC;IAClC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;0BAA2C;;;;;;YAGzD,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;oBAEC,SAAS,IAAM,aAAa,KAAK,GAAG;oBACpC,WAAW,CAAC,mFAAmF,EAC7F,gBAAgB,KAAK,GAAG,GACpB,4DACA,qGACJ;8BAED,KAAK,KAAK;mBARN,KAAK,GAAG;;;;;;;;;;;AAavB", "debugId": null}}, {"offset": {"line": 4148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/EnhancedUI.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useResponsive } from './ResponsiveLayout';\n\ninterface EnhancedButtonProps {\n  children: React.ReactNode;\n  onClick?: () => void;\n  variant?: 'primary' | 'secondary' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n  disabled?: boolean;\n}\n\nexport function EnhancedButton({ \n  children, \n  onClick, \n  variant = 'primary', \n  size = 'md', \n  className = '',\n  disabled = false \n}: EnhancedButtonProps) {\n  const { isMobile } = useResponsive();\n  \n  const baseClasses = \"font-light transition-all duration-300 transform backdrop-blur-sm border\";\n  \n  const variantClasses = {\n    primary: \"bg-ghost-100 border-whisper-200 text-whisper-300 hover:bg-ghost-200 hover:border-whisper-300\",\n    secondary: \"bg-whisper-100 border-whisper-300 text-void-700 hover:bg-whisper-200\",\n    ghost: \"bg-transparent border-whisper-200 text-whisper-400 hover:bg-ghost-100 hover:text-whisper-300\"\n  };\n  \n  const sizeClasses = {\n    sm: isMobile ? \"px-3 py-1 text-xs\" : \"px-4 py-2 text-sm\",\n    md: isMobile ? \"px-4 py-2 text-sm\" : \"px-6 py-3 text-base\",\n    lg: isMobile ? \"px-6 py-3 text-base\" : \"px-8 py-4 text-lg\"\n  };\n  \n  return (\n    <motion.button\n      onClick={onClick}\n      disabled={disabled}\n      className={`\n        ${baseClasses} \n        ${variantClasses[variant]} \n        ${sizeClasses[size]} \n        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:shadow-lg animate-glow-pulse'}\n        ${className}\n      `}\n      whileHover={disabled ? {} : { scale: 1.05 }}\n      whileTap={disabled ? {} : { scale: 0.95 }}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n    >\n      {children}\n    </motion.button>\n  );\n}\n\ninterface EnhancedCardProps {\n  children: React.ReactNode;\n  title?: string;\n  subtitle?: string;\n  onClick?: () => void;\n  className?: string;\n  glowing?: boolean;\n}\n\nexport function EnhancedCard({ \n  children, \n  title, \n  subtitle, \n  onClick, \n  className = '',\n  glowing = false \n}: EnhancedCardProps) {\n  const { isMobile } = useResponsive();\n  \n  return (\n    <motion.div\n      onClick={onClick}\n      className={`\n        border border-whisper-200 backdrop-blur-sm transition-all duration-700\n        ${onClick ? 'cursor-pointer hover:border-whisper-300 hover:bg-ghost-100' : ''}\n        ${glowing ? 'animate-glow-pulse' : ''}\n        ${isMobile ? 'p-6' : 'p-8'}\n        ${className}\n      `}\n      whileHover={onClick ? { scale: 1.02, y: -5 } : {}}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.6 }}\n    >\n      {title && (\n        <h3 className=\"text-whisper-300 text-xl font-light mb-4 animate-whisper-glow\">\n          {title}\n        </h3>\n      )}\n      {subtitle && (\n        <p className=\"text-whisper-500 text-sm opacity-80 mb-4\">\n          {subtitle}\n        </p>\n      )}\n      {children}\n      <div className=\"mt-4 w-full h-px bg-gradient-to-r from-whisper-200 to-transparent animate-shimmer\" />\n    </motion.div>\n  );\n}\n\ninterface FloatingElementProps {\n  children: React.ReactNode;\n  delay?: number;\n  duration?: number;\n  className?: string;\n}\n\nexport function FloatingElement({ \n  children, \n  delay = 0, \n  duration = 6, \n  className = '' \n}: FloatingElementProps) {\n  return (\n    <motion.div\n      className={`animate-float3d ${className}`}\n      initial={{ opacity: 0, y: 50 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ delay, duration: 1 }}\n      style={{\n        animationDelay: `${delay}s`,\n        animationDuration: `${duration}s`\n      }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n\ninterface GlowTextProps {\n  children: React.ReactNode;\n  intensity?: 'low' | 'medium' | 'high';\n  color?: string;\n  className?: string;\n}\n\nexport function GlowText({ \n  children, \n  intensity = 'medium', \n  color = 'var(--whisper-bright)', \n  className = '' \n}: GlowTextProps) {\n  const intensityMap = {\n    low: '0 0 10px',\n    medium: '0 0 20px',\n    high: '0 0 30px, 0 0 40px'\n  };\n  \n  return (\n    <span\n      className={`animate-whisper-glow ${className}`}\n      style={{\n        textShadow: `${intensityMap[intensity]} ${color}`\n      }}\n    >\n      {children}\n    </span>\n  );\n}\n\ninterface ProgressBarProps {\n  progress: number;\n  max: number;\n  label?: string;\n  color?: string;\n  className?: string;\n}\n\nexport function ProgressBar({ \n  progress, \n  max, \n  label, \n  color = '#64ffda', \n  className = '' \n}: ProgressBarProps) {\n  const percentage = (progress / max) * 100;\n  \n  return (\n    <div className={`w-full ${className}`}>\n      {label && (\n        <div className=\"flex justify-between text-whisper-400 text-xs mb-2\">\n          <span>{label}</span>\n          <span>{progress}/{max}</span>\n        </div>\n      )}\n      <div className=\"w-full bg-ghost-100 rounded-full h-2 overflow-hidden\">\n        <motion.div\n          className=\"h-full rounded-full\"\n          style={{ backgroundColor: color }}\n          initial={{ width: 0 }}\n          animate={{ width: `${percentage}%` }}\n          transition={{ duration: 0.8, ease: \"easeOut\" }}\n        />\n      </div>\n    </div>\n  );\n}\n\ninterface ParticleFieldProps {\n  count?: number;\n  className?: string;\n}\n\nexport function ParticleField({ count = 20, className = '' }: ParticleFieldProps) {\n  return (\n    <div className={`absolute inset-0 pointer-events-none ${className}`}>\n      {Array.from({ length: count }).map((_, i) => (\n        <motion.div\n          key={i}\n          className=\"absolute w-1 h-1 bg-whisper-300 rounded-full opacity-30\"\n          style={{\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`,\n          }}\n          animate={{\n            y: [0, -20, 0],\n            opacity: [0.3, 0.8, 0.3],\n            scale: [0.5, 1, 0.5],\n          }}\n          transition={{\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            delay: Math.random() * 2,\n            ease: \"easeInOut\",\n          }}\n        />\n      ))}\n    </div>\n  );\n}\n\ninterface StatusIndicatorProps {\n  status: 'idle' | 'loading' | 'success' | 'error';\n  message?: string;\n  className?: string;\n}\n\nexport function StatusIndicator({ status, message, className = '' }: StatusIndicatorProps) {\n  const statusConfig = {\n    idle: { color: 'text-whisper-500', icon: '○' },\n    loading: { color: 'text-blue-400', icon: '◐' },\n    success: { color: 'text-green-400', icon: '●' },\n    error: { color: 'text-red-400', icon: '✕' }\n  };\n  \n  const config = statusConfig[status];\n  \n  return (\n    <motion.div\n      className={`flex items-center space-x-2 ${className}`}\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      transition={{ duration: 0.3 }}\n    >\n      <motion.span\n        className={`${config.color} text-sm`}\n        animate={status === 'loading' ? { rotate: 360 } : {}}\n        transition={status === 'loading' ? { duration: 1, repeat: Infinity, ease: \"linear\" } : {}}\n      >\n        {config.icon}\n      </motion.span>\n      {message && (\n        <span className=\"text-whisper-400 text-xs font-light\">\n          {message}\n        </span>\n      )}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AAHA;;;;AAcO,SAAS,eAAe,EAC7B,QAAQ,EACR,OAAO,EACP,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EACI;IACpB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;IAEjC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,IAAI,WAAW,sBAAsB;QACrC,IAAI,WAAW,sBAAsB;QACrC,IAAI,WAAW,wBAAwB;IACzC;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;QACT,UAAU;QACV,WAAW,CAAC;QACV,EAAE,YAAY;QACd,EAAE,cAAc,CAAC,QAAQ,CAAC;QAC1B,EAAE,WAAW,CAAC,KAAK,CAAC;QACpB,EAAE,WAAW,kCAAkC,oDAAoD;QACnG,EAAE,UAAU;MACd,CAAC;QACD,YAAY,WAAW,CAAC,IAAI;YAAE,OAAO;QAAK;QAC1C,UAAU,WAAW,CAAC,IAAI;YAAE,OAAO;QAAK;QACxC,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;kBAE3B;;;;;;AAGP;AAWO,SAAS,aAAa,EAC3B,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,OAAO,EACP,YAAY,EAAE,EACd,UAAU,KAAK,EACG;IAClB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;IAEjC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;QACT,WAAW,CAAC;;QAEV,EAAE,UAAU,+DAA+D,GAAG;QAC9E,EAAE,UAAU,uBAAuB,GAAG;QACtC,EAAE,WAAW,QAAQ,MAAM;QAC3B,EAAE,UAAU;MACd,CAAC;QACD,YAAY,UAAU;YAAE,OAAO;YAAM,GAAG,CAAC;QAAE,IAAI,CAAC;QAChD,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;YAE3B,uBACC,8OAAC;gBAAG,WAAU;0BACX;;;;;;YAGJ,0BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAGJ;0BACD,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;AASO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,QAAQ,CAAC,EACT,WAAW,CAAC,EACZ,YAAY,EAAE,EACO;IACrB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,gBAAgB,EAAE,WAAW;QACzC,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE;YAAO,UAAU;QAAE;QACjC,OAAO;YACL,gBAAgB,GAAG,MAAM,CAAC,CAAC;YAC3B,mBAAmB,GAAG,SAAS,CAAC,CAAC;QACnC;kBAEC;;;;;;AAGP;AASO,SAAS,SAAS,EACvB,QAAQ,EACR,YAAY,QAAQ,EACpB,QAAQ,uBAAuB,EAC/B,YAAY,EAAE,EACA;IACd,MAAM,eAAe;QACnB,KAAK;QACL,QAAQ;QACR,MAAM;IACR;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,qBAAqB,EAAE,WAAW;QAC9C,OAAO;YACL,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO;QACnD;kBAEC;;;;;;AAGP;AAUO,SAAS,YAAY,EAC1B,QAAQ,EACR,GAAG,EACH,KAAK,EACL,QAAQ,SAAS,EACjB,YAAY,EAAE,EACG;IACjB,MAAM,aAAa,AAAC,WAAW,MAAO;IAEtC,qBACE,8OAAC;QAAI,WAAW,CAAC,OAAO,EAAE,WAAW;;YAClC,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAM;;;;;;kCACP,8OAAC;;4BAAM;4BAAS;4BAAE;;;;;;;;;;;;;0BAGtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,OAAO;wBAAE,iBAAiB;oBAAM;oBAChC,SAAS;wBAAE,OAAO;oBAAE;oBACpB,SAAS;wBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oBAAC;oBACnC,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAU;;;;;;;;;;;;;;;;;AAKvD;AAOO,SAAS,cAAc,EAAE,QAAQ,EAAE,EAAE,YAAY,EAAE,EAAsB;IAC9E,qBACE,8OAAC;QAAI,WAAW,CAAC,qCAAqC,EAAE,WAAW;kBAChE,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,kBACrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAU;gBACV,OAAO;oBACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oBAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;gBAChC;gBACA,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;oBACxB,OAAO;wBAAC;wBAAK;wBAAG;qBAAI;gBACtB;gBACA,YAAY;oBACV,UAAU,IAAI,KAAK,MAAM,KAAK;oBAC9B,QAAQ;oBACR,OAAO,KAAK,MAAM,KAAK;oBACvB,MAAM;gBACR;eAhBK;;;;;;;;;;AAqBf;AAQO,SAAS,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,EAAwB;IACvF,MAAM,eAAe;QACnB,MAAM;YAAE,OAAO;YAAoB,MAAM;QAAI;QAC7C,SAAS;YAAE,OAAO;YAAiB,MAAM;QAAI;QAC7C,SAAS;YAAE,OAAO;YAAkB,MAAM;QAAI;QAC9C,OAAO;YAAE,OAAO;YAAgB,MAAM;QAAI;IAC5C;IAEA,MAAM,SAAS,YAAY,CAAC,OAAO;IAEnC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,4BAA4B,EAAE,WAAW;QACrD,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;;0BAE5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,WAAW,GAAG,OAAO,KAAK,CAAC,QAAQ,CAAC;gBACpC,SAAS,WAAW,YAAY;oBAAE,QAAQ;gBAAI,IAAI,CAAC;gBACnD,YAAY,WAAW,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAS,IAAI,CAAC;0BAEvF,OAAO,IAAI;;;;;;YAEb,yBACC,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 4493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/EasterEggHunter.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface EasterEggHunterProps {\n  onSecretFound: (secretId: string) => void;\n}\n\nexport function EasterEggHunter({ onSecretFound }: EasterEggHunterProps) {\n  const [konamiSequence, setKonamiSequence] = useState<string[]>([]);\n  const [clickCount, setClickCount] = useState(0);\n  const [lastClickTime, setLastClickTime] = useState(0);\n\n  const konamiCode = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'KeyB', 'KeyA'];\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      const newSequence = [...konamiSequence, e.code];\n      \n      // Keep only the last 10 keys\n      if (newSequence.length > 10) {\n        newSequence.shift();\n      }\n      \n      setKonamiSequence(newSequence);\n      \n      // Check if konami code is complete\n      if (newSequence.length === 10 && \n          newSequence.every((key, index) => key === konamiCode[index])) {\n        onSecretFound('konami_code');\n        setKonamiSequence([]);\n      }\n    };\n\n    const handleClick = (e: MouseEvent) => {\n      const now = Date.now();\n      \n      // Reset if too much time has passed\n      if (now - lastClickTime > 1000) {\n        setClickCount(1);\n      } else {\n        setClickCount(prev => prev + 1);\n      }\n      \n      setLastClickTime(now);\n      \n      // Secret: 7 rapid clicks\n      if (clickCount >= 7) {\n        onSecretFound('rapid_clicks');\n        setClickCount(0);\n      }\n      \n      // Secret: clicking in corners\n      const { clientX, clientY } = e;\n      const { innerWidth, innerHeight } = window;\n      \n      if ((clientX < 50 && clientY < 50) || \n          (clientX > innerWidth - 50 && clientY < 50) ||\n          (clientX < 50 && clientY > innerHeight - 50) ||\n          (clientX > innerWidth - 50 && clientY > innerHeight - 50)) {\n        onSecretFound('corner_click');\n      }\n    };\n\n    const handleMouseMove = (e: MouseEvent) => {\n      // Secret: mouse idle in center for 5 seconds\n      const { clientX, clientY } = e;\n      const { innerWidth, innerHeight } = window;\n      \n      if (Math.abs(clientX - innerWidth / 2) < 20 && \n          Math.abs(clientY - innerHeight / 2) < 20) {\n        setTimeout(() => {\n          onSecretFound('center_idle');\n        }, 5000);\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    document.addEventListener('click', handleClick);\n    document.addEventListener('mousemove', handleMouseMove);\n\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.removeEventListener('click', handleClick);\n      document.removeEventListener('mousemove', handleMouseMove);\n    };\n  }, [konamiSequence, clickCount, lastClickTime, onSecretFound]);\n\n  return null; // This component is invisible\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAQO,SAAS,gBAAgB,EAAE,aAAa,EAAwB;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,aAAa;QAAC;QAAW;QAAW;QAAa;QAAa;QAAa;QAAc;QAAa;QAAc;QAAQ;KAAO;IAEzI,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,MAAM,cAAc;mBAAI;gBAAgB,EAAE,IAAI;aAAC;YAE/C,6BAA6B;YAC7B,IAAI,YAAY,MAAM,GAAG,IAAI;gBAC3B,YAAY,KAAK;YACnB;YAEA,kBAAkB;YAElB,mCAAmC;YACnC,IAAI,YAAY,MAAM,KAAK,MACvB,YAAY,KAAK,CAAC,CAAC,KAAK,QAAU,QAAQ,UAAU,CAAC,MAAM,GAAG;gBAChE,cAAc;gBACd,kBAAkB,EAAE;YACtB;QACF;QAEA,MAAM,cAAc,CAAC;YACnB,MAAM,MAAM,KAAK,GAAG;YAEpB,oCAAoC;YACpC,IAAI,MAAM,gBAAgB,MAAM;gBAC9B,cAAc;YAChB,OAAO;gBACL,cAAc,CAAA,OAAQ,OAAO;YAC/B;YAEA,iBAAiB;YAEjB,yBAAyB;YACzB,IAAI,cAAc,GAAG;gBACnB,cAAc;gBACd,cAAc;YAChB;YAEA,8BAA8B;YAC9B,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;YAC7B,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG;YAEpC,IAAI,AAAC,UAAU,MAAM,UAAU,MAC1B,UAAU,aAAa,MAAM,UAAU,MACvC,UAAU,MAAM,UAAU,cAAc,MACxC,UAAU,aAAa,MAAM,UAAU,cAAc,IAAK;gBAC7D,cAAc;YAChB;QACF;QAEA,MAAM,kBAAkB,CAAC;YACvB,6CAA6C;YAC7C,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;YAC7B,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG;YAEpC,IAAI,KAAK,GAAG,CAAC,UAAU,aAAa,KAAK,MACrC,KAAK,GAAG,CAAC,UAAU,cAAc,KAAK,IAAI;gBAC5C,WAAW;oBACT,cAAc;gBAChB,GAAG;YACL;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,SAAS,gBAAgB,CAAC,SAAS;QACnC,SAAS,gBAAgB,CAAC,aAAa;QAEvC,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,mBAAmB,CAAC,SAAS;YACtC,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC;QAAgB;QAAY;QAAe;KAAc;IAE7D,OAAO,MAAM,8BAA8B;AAC7C", "debugId": null}}, {"offset": {"line": 4585, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/AmbientSoundscape.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\nexport function AmbientSoundscape() {\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [volume, setVolume] = useState(0.3);\n\n  useEffect(() => {\n    // Create ambient sound context\n    let audioContext: AudioContext | null = null;\n    let oscillator: OscillatorNode | null = null;\n    let gainNode: GainNode | null = null;\n\n    const startAmbientSound = () => {\n      try {\n        audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();\n        oscillator = audioContext.createOscillator();\n        gainNode = audioContext.createGain();\n\n        // Create a very low frequency ambient tone\n        oscillator.frequency.setValueAtTime(40, audioContext.currentTime);\n        oscillator.type = 'sine';\n        \n        // Very quiet volume\n        gainNode.gain.setValueAtTime(volume * 0.1, audioContext.currentTime);\n        \n        oscillator.connect(gainNode);\n        gainNode.connect(audioContext.destination);\n        \n        oscillator.start();\n        setIsPlaying(true);\n      } catch (error) {\n        console.log('Audio context not available');\n      }\n    };\n\n    const stopAmbientSound = () => {\n      if (oscillator) {\n        oscillator.stop();\n        oscillator = null;\n      }\n      if (audioContext) {\n        audioContext.close();\n        audioContext = null;\n      }\n      setIsPlaying(false);\n    };\n\n    // Start ambient sound on first user interaction\n    const handleFirstInteraction = () => {\n      startAmbientSound();\n      document.removeEventListener('click', handleFirstInteraction);\n      document.removeEventListener('keydown', handleFirstInteraction);\n    };\n\n    document.addEventListener('click', handleFirstInteraction);\n    document.addEventListener('keydown', handleFirstInteraction);\n\n    return () => {\n      stopAmbientSound();\n      document.removeEventListener('click', handleFirstInteraction);\n      document.removeEventListener('keydown', handleFirstInteraction);\n    };\n  }, [volume]);\n\n  return (\n    <div className=\"fixed top-4 left-4 z-50\">\n      <div className=\"text-whisper-600 text-xs font-mono\">\n        {isPlaying ? '♪ ambient' : '♪ silent'}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B,IAAI,eAAoC;QACxC,IAAI,aAAoC;QACxC,IAAI,WAA4B;QAEhC,MAAM,oBAAoB;YACxB,IAAI;gBACF,eAAe,IAAI,CAAC,OAAO,YAAY,IAAI,AAAC,OAAe,kBAAkB;gBAC7E,aAAa,aAAa,gBAAgB;gBAC1C,WAAW,aAAa,UAAU;gBAElC,2CAA2C;gBAC3C,WAAW,SAAS,CAAC,cAAc,CAAC,IAAI,aAAa,WAAW;gBAChE,WAAW,IAAI,GAAG;gBAElB,oBAAoB;gBACpB,SAAS,IAAI,CAAC,cAAc,CAAC,SAAS,KAAK,aAAa,WAAW;gBAEnE,WAAW,OAAO,CAAC;gBACnB,SAAS,OAAO,CAAC,aAAa,WAAW;gBAEzC,WAAW,KAAK;gBAChB,aAAa;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC;YACd;QACF;QAEA,MAAM,mBAAmB;YACvB,IAAI,YAAY;gBACd,WAAW,IAAI;gBACf,aAAa;YACf;YACA,IAAI,cAAc;gBAChB,aAAa,KAAK;gBAClB,eAAe;YACjB;YACA,aAAa;QACf;QAEA,gDAAgD;QAChD,MAAM,yBAAyB;YAC7B;YACA,SAAS,mBAAmB,CAAC,SAAS;YACtC,SAAS,mBAAmB,CAAC,WAAW;QAC1C;QAEA,SAAS,gBAAgB,CAAC,SAAS;QACnC,SAAS,gBAAgB,CAAC,WAAW;QAErC,OAAO;YACL;YACA,SAAS,mBAAmB,CAAC,SAAS;YACtC,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;KAAO;IAEX,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ,YAAY,cAAc;;;;;;;;;;;AAInC", "debugId": null}}, {"offset": {"line": 4668, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/components/HiddenNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface HiddenNavigationProps {\n  secretsFound: string[];\n}\n\nexport function HiddenNavigation({ secretsFound }: HiddenNavigationProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY });\n      \n      // Show navigation when mouse is in top-left corner\n      if (e.clientX < 100 && e.clientY < 100) {\n        setIsVisible(true);\n      } else if (e.clientX > 200 || e.clientY > 200) {\n        setIsVisible(false);\n      }\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    return () => document.removeEventListener('mousemove', handleMouseMove);\n  }, []);\n\n  const navigationItems = [\n    { id: 'echoes', label: '◦ echoes', unlocked: secretsFound.includes('logo_sequence') },\n    { id: 'fragments', label: '◦ fragments', unlocked: secretsFound.includes('konami_code') },\n    { id: 'void', label: '◦ the void', unlocked: secretsFound.includes('rapid_clicks') },\n    { id: 'contact', label: '◦ contact', unlocked: secretsFound.length >= 3 },\n  ];\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.nav\n          initial={{ opacity: 0, x: -50 }}\n          animate={{ opacity: 1, x: 0 }}\n          exit={{ opacity: 0, x: -50 }}\n          transition={{ duration: 0.3 }}\n          className=\"fixed top-8 left-8 z-40\"\n        >\n          <div className=\"bg-void-600 border border-whisper-100 p-6 backdrop-blur-sm\">\n            <h3 className=\"text-whisper-300 text-sm font-mono mb-4\">navigate</h3>\n            <ul className=\"space-y-2\">\n              {navigationItems.map((item) => (\n                <li key={item.id}>\n                  <button\n                    className={`text-xs font-mono transition-colors duration-300 ${\n                      item.unlocked \n                        ? 'text-whisper-400 hover:text-whisper-200 cursor-pointer' \n                        : 'text-whisper-700 cursor-not-allowed'\n                    }`}\n                    disabled={!item.unlocked}\n                  >\n                    {item.label}\n                  </button>\n                </li>\n              ))}\n            </ul>\n            \n            <div className=\"mt-6 pt-4 border-t border-whisper-100\">\n              <p className=\"text-whisper-600 text-xs font-mono\">\n                secrets: {secretsFound.length}/7\n              </p>\n            </div>\n          </div>\n        </motion.nav>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AASO,SAAS,iBAAiB,EAAE,YAAY,EAAyB;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,CAAC;YACvB,iBAAiB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;YAE9C,mDAAmD;YACnD,IAAI,EAAE,OAAO,GAAG,OAAO,EAAE,OAAO,GAAG,KAAK;gBACtC,aAAa;YACf,OAAO,IAAI,EAAE,OAAO,GAAG,OAAO,EAAE,OAAO,GAAG,KAAK;gBAC7C,aAAa;YACf;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB;YAAE,IAAI;YAAU,OAAO;YAAY,UAAU,aAAa,QAAQ,CAAC;QAAiB;QACpF;YAAE,IAAI;YAAa,OAAO;YAAe,UAAU,aAAa,QAAQ,CAAC;QAAe;QACxF;YAAE,IAAI;YAAQ,OAAO;YAAc,UAAU,aAAa,QAAQ,CAAC;QAAgB;QACnF;YAAE,IAAI;YAAW,OAAO;YAAa,UAAU,aAAa,MAAM,IAAI;QAAE;KACzE;IAED,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC9B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC3B,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;sBAEV,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,8OAAC;wBAAG,WAAU;kCACX,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC;0CACC,cAAA,8OAAC;oCACC,WAAW,CAAC,iDAAiD,EAC3D,KAAK,QAAQ,GACT,2DACA,uCACJ;oCACF,UAAU,CAAC,KAAK,QAAQ;8CAEvB,KAAK,KAAK;;;;;;+BATN,KAAK,EAAE;;;;;;;;;;kCAepB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAqC;gCACtC,aAAa,MAAM;gCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C", "debugId": null}}, {"offset": {"line": 4816, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { DreamyLogo } from \"@/components/DreamyLogo\";\nimport { AnimatedSVGLogo } from \"@/components/AnimatedSVGLogo\";\nimport { Logo3D } from \"@/components/Logo3D\";\nimport { Scene3D } from \"@/components/Scene3D\";\nimport { SVG3DLogo } from \"@/components/SVG3DLogo\";\nimport { InteractiveGame } from \"@/components/InteractiveGame\";\nimport { MysticInteractiveGame } from \"@/components/MysticInteractiveGame\";\nimport { MysticAtmosphere } from \"@/components/MysticAtmosphere\";\nimport {\n  ResponsiveLayout,\n  AdaptiveControls,\n  useResponsive,\n} from \"@/components/ResponsiveLayout\";\nimport {\n  EnhancedCard,\n  EnhancedButton,\n  GlowText,\n  FloatingElement,\n  ParticleField,\n} from \"@/components/EnhancedUI\";\nimport { EasterEggHunter } from \"@/components/EasterEggHunter\";\nimport { AmbientSoundscape } from \"@/components/AmbientSoundscape\";\nimport { HiddenNavigation } from \"@/components/HiddenNavigation\";\n\nexport default function Home() {\n  const [currentPhase, setCurrentPhase] = useState<\n    \"entry\" | \"void\" | \"exploration\" | \"game\" | \"mystic\"\n  >(\"entry\");\n  const [secretsFound, setSecretsFound] = useState<string[]>([]);\n  const [showHiddenText, setShowHiddenText] = useState(false);\n  const [logoMode, setLogoMode] = useState<\n    \"2d\" | \"svg\" | \"3d\" | \"scene\" | \"svg3d\"\n  >(\"svg3d\");\n  const [activeLetters, setActiveLetters] = useState<boolean[]>([\n    false,\n    false,\n    false,\n    false,\n    false,\n  ]);\n  const [gameScore, setGameScore] = useState(0);\n\n  const { isMobile, isTablet } = useResponsive();\n\n  useEffect(() => {\n    // Auto-transition from entry to void after 3 seconds\n    const timer = setTimeout(() => {\n      if (currentPhase === \"entry\") {\n        setCurrentPhase(\"void\");\n      }\n    }, 3000);\n\n    return () => clearTimeout(timer);\n  }, [currentPhase]);\n\n  const handleLogoInteraction = (letter: string, index: number) => {\n    if (letter === \"secret\") {\n      setSecretsFound((prev) => [...prev, \"logo_sequence\"]);\n      setCurrentPhase(\"exploration\");\n      // Animation spéciale pour toutes les lettres\n      setActiveLetters([true, true, true, true, true]);\n      setTimeout(\n        () => setActiveLetters([false, false, false, false, false]),\n        5000\n      );\n    } else {\n      // Activer la lettre cliquée\n      const newActiveLetters = [...activeLetters];\n      newActiveLetters[index] = true;\n      setActiveLetters(newActiveLetters);\n      setTimeout(() => {\n        const resetLetters = [...activeLetters];\n        resetLetters[index] = false;\n        setActiveLetters(resetLetters);\n      }, 3000);\n    }\n  };\n\n  const handleSecretFound = (secretId: string) => {\n    setSecretsFound((prev) => [...prev, secretId]);\n  };\n\n  const handleGameComplete = (score: number) => {\n    setGameScore(score);\n    setSecretsFound((prev) => [...prev, \"game_master\"]);\n    setCurrentPhase(\"exploration\");\n  };\n\n  const handleGameLetterActivate = (index: number) => {\n    const newActiveLetters = [...activeLetters];\n    newActiveLetters[index] = true;\n    setActiveLetters(newActiveLetters);\n    setTimeout(() => {\n      const resetLetters = [...activeLetters];\n      resetLetters[index] = false;\n      setActiveLetters(resetLetters);\n    }, 500);\n  };\n\n  const logoModes = [\n    { key: \"svg3d\", label: isMobile ? \"SVG 3D\" : \"SVG 3D Extrudé\" },\n    { key: \"svg\", label: isMobile ? \"SVG\" : \"SVG Animé\" },\n    { key: \"2d\", label: isMobile ? \"2D\" : \"2D Dreamy\" },\n    { key: \"3d\", label: isMobile ? \"3D\" : \"3D Texte\" },\n    { key: \"scene\", label: isMobile ? \"Scène\" : \"Scène 3D\" },\n  ];\n\n  return (\n    <ResponsiveLayout className=\"bg-void-700\">\n      <MysticAtmosphere\n        intensity={currentPhase === \"mystic\" ? 3 : 1}\n        showRitualCircle={currentPhase === \"mystic\"}\n        isChanneling={false}\n      />\n      <AmbientSoundscape />\n      <EasterEggHunter onSecretFound={handleSecretFound} />\n      <AnimatePresence mode=\"wait\">\n        {currentPhase === \"entry\" && (\n          <motion.div\n            key=\"entry\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 2 }}\n            className=\"fixed inset-0 flex items-center justify-center z-10\"\n          >\n            <motion.div\n              initial={{ scale: 0.8, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              transition={{ delay: 0.5, duration: 1.5 }}\n              className=\"text-center\"\n            >\n              <motion.div\n                className=\"text-whisper-400 text-sm font-mono mb-8 animate-whisper-glow\"\n                animate={{ opacity: [0.5, 1, 0.5] }}\n                transition={{ duration: 3, repeat: Infinity }}\n              >\n                entering the void...\n              </motion.div>\n              <h1\n                className=\"text-whisper-300 text-6xl font-light mb-4 animate-dreamy-float\"\n                style={{\n                  textShadow: \"0 0 30px var(--whisper-bright)\",\n                }}\n              >\n                how r u\n              </h1>\n              <div className=\"w-32 h-px bg-gradient-to-r from-transparent via-whisper-300 to-transparent mx-auto animate-gentle-sway\" />\n              <motion.div\n                className=\"text-whisper-500 text-xs font-light mt-4\"\n                animate={{ opacity: [0.3, 0.8, 0.3] }}\n                transition={{ duration: 4, repeat: Infinity }}\n              >\n                consciousness loading...\n              </motion.div>\n            </motion.div>\n          </motion.div>\n        )}\n\n        {currentPhase === \"void\" && (\n          <motion.div\n            key=\"void\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 2 }}\n            className=\"fixed inset-0 flex flex-col items-center justify-center z-10\"\n          >\n            <motion.div\n              initial={{ y: 50, opacity: 0 }}\n              animate={{ y: 0, opacity: 1 }}\n              transition={{ delay: 1, duration: 2 }}\n              className=\"text-center mb-16\"\n            >\n              {logoMode === \"svg3d\" && (\n                <SVG3DLogo\n                  onLetterClick={handleLogoInteraction}\n                  activeLetters={activeLetters}\n                  className={isMobile ? \"h-[400px]\" : \"h-[600px]\"}\n                />\n              )}\n              {logoMode === \"2d\" && (\n                <DreamyLogo onLetterClick={handleLogoInteraction} />\n              )}\n              {logoMode === \"svg\" && (\n                <AnimatedSVGLogo onLetterClick={handleLogoInteraction} />\n              )}\n              {logoMode === \"3d\" && (\n                <Logo3D onLetterClick={handleLogoInteraction} />\n              )}\n              {logoMode === \"scene\" && (\n                <Scene3D activeLetters={activeLetters} />\n              )}\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 3, duration: 2 }}\n              className=\"text-center max-w-md\"\n            >\n              <p className=\"text-whisper-400 text-sm font-light mb-8 animate-whisper-glow\">\n                click the letters... listen... feel...\n              </p>\n\n              <motion.div\n                className=\"text-whisper-600 text-xs font-light\"\n                animate={{ opacity: [0.3, 0.7, 0.3] }}\n                transition={{ duration: 3, repeat: Infinity }}\n              >\n                there are secrets hidden in the darkness\n              </motion.div>\n            </motion.div>\n\n            {/* Contrôles adaptatifs */}\n            <AdaptiveControls\n              onModeChange={(mode) => setLogoMode(mode as any)}\n              currentMode={logoMode}\n              modes={logoModes}\n            />\n          </motion.div>\n        )}\n\n        {currentPhase === \"exploration\" && (\n          <motion.div\n            key=\"exploration\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 2 }}\n            className=\"min-h-screen relative\"\n          >\n            <HiddenNavigation secretsFound={secretsFound} />\n\n            {/* Main exploration area */}\n            <div className=\"container mx-auto px-8 py-16\">\n              <motion.div\n                initial={{ y: 20, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                transition={{ delay: 0.5 }}\n                className=\"text-center mb-16\"\n              >\n                <motion.div\n                  className=\"text-whisper-500 text-sm font-light mb-4 animate-whisper-glow\"\n                  animate={{ opacity: [0.5, 1, 0.5] }}\n                  transition={{ duration: 4, repeat: Infinity }}\n                >\n                  consciousness fragments detected...\n                </motion.div>\n                <h2\n                  className=\"text-whisper-300 text-4xl font-light mb-8 animate-dreamy-float\"\n                  style={{\n                    textShadow: \"0 0 25px var(--whisper-bright)\",\n                  }}\n                >\n                  you found the way\n                </h2>\n                <div className=\"w-64 h-px bg-gradient-to-r from-transparent via-whisper-300 to-transparent mx-auto mb-8 animate-gentle-sway\" />\n                <p className=\"text-whisper-400 text-lg font-light max-w-2xl mx-auto leading-relaxed\">\n                  welcome to the digital séance. here, consciousness fragments\n                  drift through the void, waiting to be discovered. each\n                  interaction reveals another layer of the mystery.\n                </p>\n              </motion.div>\n\n              {/* Interactive elements grid */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto\">\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1 }}\n                  className=\"group cursor-pointer\"\n                  onClick={() => setShowHiddenText(!showHiddenText)}\n                >\n                  <div className=\"border border-whisper-200 p-8 hover:border-whisper-300 hover:bg-ghost-100 transition-all duration-700 bg-ghost-50 backdrop-blur-sm animate-dreamy-float\">\n                    <h3 className=\"text-whisper-300 text-xl font-light mb-4 animate-whisper-glow\">\n                      echoes\n                    </h3>\n                    <p className=\"text-whisper-500 text-sm opacity-80\">\n                      fragments of sound and memory\n                    </p>\n                    <div className=\"mt-4 w-full h-px bg-gradient-to-r from-whisper-200 to-transparent animate-gentle-sway\" />\n                    <div className=\"text-whisper-600 text-xs mt-2 font-light\">\n                      drift through silence...\n                    </div>\n                  </div>\n                </motion.div>\n\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.2 }}\n                  className=\"group cursor-pointer\"\n                  onClick={() => setCurrentPhase(\"game\")}\n                >\n                  <div className=\"border border-whisper-200 p-8 hover:border-whisper-300 hover:bg-ghost-100 transition-all duration-700 bg-ghost-50 backdrop-blur-sm animate-gentle-sway\">\n                    <h3 className=\"text-whisper-300 text-xl font-light mb-4 animate-whisper-glow\">\n                      jeu de mémoire\n                    </h3>\n                    <p className=\"text-whisper-500 text-sm opacity-80\">\n                      teste ta conscience avec les lettres\n                    </p>\n                    <div className=\"mt-4 w-full h-px bg-gradient-to-r from-whisper-200 to-transparent animate-dreamy-float\" />\n                    <div className=\"text-whisper-600 text-xs mt-2 font-light\">\n                      {gameScore > 0\n                        ? `meilleur score: ${gameScore}`\n                        : \"clique pour jouer...\"}\n                    </div>\n                  </div>\n                </motion.div>\n\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1.4 }}\n                  className=\"group cursor-pointer\"\n                  onClick={() => setCurrentPhase(\"mystic\")}\n                >\n                  <div className=\"border border-purple-400 p-8 hover:border-purple-300 hover:bg-purple-900 hover:bg-opacity-20 transition-all duration-700 bg-purple-950 bg-opacity-10 backdrop-blur-sm animate-gentle-sway\">\n                    <h3 className=\"text-purple-300 text-xl font-light mb-4 animate-whisper-glow\">\n                      rituel mystique\n                    </h3>\n                    <p className=\"text-purple-400 text-sm opacity-80\">\n                      éveillez les pouvoirs cachés des lettres\n                    </p>\n                    <div className=\"mt-4 w-full h-px bg-gradient-to-r from-purple-400 to-transparent animate-dreamy-float\" />\n                    <div className=\"text-purple-500 text-xs mt-2 font-light\">\n                      entrez dans l'au-delà...\n                    </div>\n                  </div>\n                </motion.div>\n              </div>\n\n              {/* Hidden text that appears on interaction */}\n              <AnimatePresence>\n                {showHiddenText && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -20 }}\n                    className=\"mt-16 text-center\"\n                  >\n                    <div className=\"bg-ghost-100 border border-whisper-300 p-6 max-w-2xl mx-auto backdrop-blur-md animate-whisper-glow\">\n                      <div className=\"text-whisper-400 text-xs font-light mb-2 animate-dreamy-float\">\n                        revealing hidden memory...\n                      </div>\n                      <p className=\"text-whisper-300 text-sm font-light typewriter\">\n                        \"in the space between silence and sound, we exist...\"\n                      </p>\n                      <div className=\"text-whisper-500 text-xs font-light mt-4 opacity-60\">\n                        fragment recovered from the void\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n\n              {/* Secrets counter */}\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 2 }}\n                className=\"fixed bottom-8 right-8 bg-ghost-100 border border-whisper-200 p-3 text-whisper-400 text-xs font-light backdrop-blur-sm animate-whisper-glow\"\n              >\n                <div className=\"text-whisper-300 mb-1\">secrets found</div>\n                <div>{secretsFound.length}/7</div>\n                <div className=\"text-whisper-500 text-xs mt-1\">\n                  {secretsFound.length >= 3\n                    ? \"consciousness awakening...\"\n                    : \"searching the void...\"}\n                </div>\n              </motion.div>\n            </div>\n          </motion.div>\n        )}\n\n        {currentPhase === \"game\" && (\n          <motion.div\n            key=\"game\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 1 }}\n            className=\"min-h-screen relative flex items-center justify-center\"\n          >\n            <div className=\"fixed top-4 left-4 z-30\">\n              <EnhancedButton\n                onClick={() => setCurrentPhase(\"exploration\")}\n                variant=\"ghost\"\n                size=\"sm\"\n              >\n                ← Retour\n              </EnhancedButton>\n            </div>\n\n            <InteractiveGame\n              onGameComplete={handleGameComplete}\n              onLetterActivate={handleGameLetterActivate}\n            />\n          </motion.div>\n        )}\n\n        {currentPhase === \"mystic\" && (\n          <motion.div\n            key=\"mystic\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 2 }}\n            className=\"min-h-screen relative\"\n          >\n            <div className=\"fixed top-4 left-4 z-30\">\n              <EnhancedButton\n                onClick={() => setCurrentPhase(\"exploration\")}\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"text-purple-300 border-purple-400 hover:bg-purple-900 hover:bg-opacity-20\"\n              >\n                ← Retour au monde\n              </EnhancedButton>\n            </div>\n\n            <MysticInteractiveGame\n              onGameComplete={(souls) => {\n                console.log(`Rituel terminé avec ${souls} âmes collectées`);\n                setCurrentPhase(\"exploration\");\n              }}\n              onSecretUnlocked={(secret) => {\n                console.log(`Secret mystique révélé: ${secret}`);\n              }}\n            />\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </ResponsiveLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAOA;AACA;AACA;AA1BA;;;;;;;;;;;;;;;;;AA4Be,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE7C;IACF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAErC;IACF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAC5D;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qDAAqD;QACrD,MAAM,QAAQ,WAAW;YACvB,IAAI,iBAAiB,SAAS;gBAC5B,gBAAgB;YAClB;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAa;IAEjB,MAAM,wBAAwB,CAAC,QAAgB;QAC7C,IAAI,WAAW,UAAU;YACvB,gBAAgB,CAAC,OAAS;uBAAI;oBAAM;iBAAgB;YACpD,gBAAgB;YAChB,6CAA6C;YAC7C,iBAAiB;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;YAC/C,WACE,IAAM,iBAAiB;oBAAC;oBAAO;oBAAO;oBAAO;oBAAO;iBAAM,GAC1D;QAEJ,OAAO;YACL,4BAA4B;YAC5B,MAAM,mBAAmB;mBAAI;aAAc;YAC3C,gBAAgB,CAAC,MAAM,GAAG;YAC1B,iBAAiB;YACjB,WAAW;gBACT,MAAM,eAAe;uBAAI;iBAAc;gBACvC,YAAY,CAAC,MAAM,GAAG;gBACtB,iBAAiB;YACnB,GAAG;QACL;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB,CAAC,OAAS;mBAAI;gBAAM;aAAS;IAC/C;IAEA,MAAM,qBAAqB,CAAC;QAC1B,aAAa;QACb,gBAAgB,CAAC,OAAS;mBAAI;gBAAM;aAAc;QAClD,gBAAgB;IAClB;IAEA,MAAM,2BAA2B,CAAC;QAChC,MAAM,mBAAmB;eAAI;SAAc;QAC3C,gBAAgB,CAAC,MAAM,GAAG;QAC1B,iBAAiB;QACjB,WAAW;YACT,MAAM,eAAe;mBAAI;aAAc;YACvC,YAAY,CAAC,MAAM,GAAG;YACtB,iBAAiB;QACnB,GAAG;IACL;IAEA,MAAM,YAAY;QAChB;YAAE,KAAK;YAAS,OAAO,WAAW,WAAW;QAAiB;QAC9D;YAAE,KAAK;YAAO,OAAO,WAAW,QAAQ;QAAY;QACpD;YAAE,KAAK;YAAM,OAAO,WAAW,OAAO;QAAY;QAClD;YAAE,KAAK;YAAM,OAAO,WAAW,OAAO;QAAW;QACjD;YAAE,KAAK;YAAS,OAAO,WAAW,UAAU;QAAW;KACxD;IAED,qBACE,8OAAC,sIAAA,CAAA,mBAAgB;QAAC,WAAU;;0BAC1B,8OAAC,sIAAA,CAAA,mBAAgB;gBACf,WAAW,iBAAiB,WAAW,IAAI;gBAC3C,kBAAkB,iBAAiB;gBACnC,cAAc;;;;;;0BAEhB,8OAAC,uIAAA,CAAA,oBAAiB;;;;;0BAClB,8OAAC,qIAAA,CAAA,kBAAe;gBAAC,eAAe;;;;;;0BAChC,8OAAC,yLAAA,CAAA,kBAAe;gBAAC,MAAK;;oBACnB,iBAAiB,yBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,MAAM;4BAAE,SAAS;wBAAE;wBACnB,YAAY;4BAAE,UAAU;wBAAE;wBAC1B,WAAU;kCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BAClC,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;4CAAC;4CAAK;4CAAG;yCAAI;oCAAC;oCAClC,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;8CAC7C;;;;;;8CAGD,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,YAAY;oCACd;8CACD;;;;;;8CAGD,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;4CAAC;4CAAK;4CAAK;yCAAI;oCAAC;oCACpC,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;8CAC7C;;;;;;;;;;;;uBAjCC;;;;;oBAwCP,iBAAiB,wBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,MAAM;4BAAE,SAAS;wBAAE;wBACnB,YAAY;4BAAE,UAAU;wBAAE;wBAC1B,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;oCAAI,SAAS;gCAAE;gCAC7B,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;oCAAG,UAAU;gCAAE;gCACpC,WAAU;;oCAET,aAAa,yBACZ,8OAAC,+HAAA,CAAA,YAAS;wCACR,eAAe;wCACf,eAAe;wCACf,WAAW,WAAW,cAAc;;;;;;oCAGvC,aAAa,sBACZ,8OAAC,gIAAA,CAAA,aAAU;wCAAC,eAAe;;;;;;oCAE5B,aAAa,uBACZ,8OAAC,qIAAA,CAAA,kBAAe;wCAAC,eAAe;;;;;;oCAEjC,aAAa,sBACZ,8OAAC,4HAAA,CAAA,SAAM;wCAAC,eAAe;;;;;;oCAExB,aAAa,yBACZ,8OAAC,6HAAA,CAAA,UAAO;wCAAC,eAAe;;;;;;;;;;;;0CAI5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,OAAO;oCAAG,UAAU;gCAAE;gCACpC,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;kDAAgE;;;;;;kDAI7E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;gDAAC;gDAAK;gDAAK;6CAAI;wCAAC;wCACpC,YAAY;4CAAE,UAAU;4CAAG,QAAQ;wCAAS;kDAC7C;;;;;;;;;;;;0CAMH,8OAAC,sIAAA,CAAA,mBAAgB;gCACf,cAAc,CAAC,OAAS,YAAY;gCACpC,aAAa;gCACb,OAAO;;;;;;;uBAzDL;;;;;oBA8DP,iBAAiB,+BAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;wBAAE;wBAC1B,WAAU;;0CAEV,8OAAC,sIAAA,CAAA,mBAAgB;gCAAC,cAAc;;;;;;0CAGhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,GAAG;4CAAI,SAAS;wCAAE;wCAC7B,SAAS;4CAAE,GAAG;4CAAG,SAAS;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;;0DAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS;wDAAC;wDAAK;wDAAG;qDAAI;gDAAC;gDAClC,YAAY;oDAAE,UAAU;oDAAG,QAAQ;gDAAS;0DAC7C;;;;;;0DAGD,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,YAAY;gDACd;0DACD;;;;;;0DAGD,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAE,WAAU;0DAAwE;;;;;;;;;;;;kDAQvF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAE;gDACvB,WAAU;gDACV,SAAS,IAAM,kBAAkB,CAAC;0DAElC,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAgE;;;;;;sEAG9E,8OAAC;4DAAE,WAAU;sEAAsC;;;;;;sEAGnD,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;;;;;;0DAM9D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAI;gDACzB,WAAU;gDACV,SAAS,IAAM,gBAAgB;0DAE/B,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAgE;;;;;;sEAG9E,8OAAC;4DAAE,WAAU;sEAAsC;;;;;;sEAGnD,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;sEACZ,YAAY,IACT,CAAC,gBAAgB,EAAE,WAAW,GAC9B;;;;;;;;;;;;;;;;;0DAKV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAI;gDACzB,WAAU;gDACV,SAAS,IAAM,gBAAgB;0DAE/B,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA+D;;;;;;sEAG7E,8OAAC;4DAAE,WAAU;sEAAqC;;;;;;sEAGlD,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;sEAA0C;;;;;;;;;;;;;;;;;;;;;;;kDAQ/D,8OAAC,yLAAA,CAAA,kBAAe;kDACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,MAAM;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC3B,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAgE;;;;;;kEAG/E,8OAAC;wDAAE,WAAU;kEAAiD;;;;;;kEAG9D,8OAAC;wDAAI,WAAU;kEAAsD;;;;;;;;;;;;;;;;;;;;;;kDAS7E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,YAAY;4CAAE,OAAO;wCAAE;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,8OAAC;;oDAAK,aAAa,MAAM;oDAAC;;;;;;;0DAC1B,8OAAC;gDAAI,WAAU;0DACZ,aAAa,MAAM,IAAI,IACpB,+BACA;;;;;;;;;;;;;;;;;;;uBA/IN;;;;;oBAsJP,iBAAiB,wBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,MAAM;4BAAE,SAAS;wBAAE;wBACnB,YAAY;4BAAE,UAAU;wBAAE;wBAC1B,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gIAAA,CAAA,iBAAc;oCACb,SAAS,IAAM,gBAAgB;oCAC/B,SAAQ;oCACR,MAAK;8CACN;;;;;;;;;;;0CAKH,8OAAC,qIAAA,CAAA,kBAAe;gCACd,gBAAgB;gCAChB,kBAAkB;;;;;;;uBAnBhB;;;;;oBAwBP,iBAAiB,0BAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,MAAM;4BAAE,SAAS;wBAAE;wBACnB,YAAY;4BAAE,UAAU;wBAAE;wBAC1B,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gIAAA,CAAA,iBAAc;oCACb,SAAS,IAAM,gBAAgB;oCAC/B,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;0CAKH,8OAAC,2IAAA,CAAA,wBAAqB;gCACpB,gBAAgB,CAAC;oCACf,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,MAAM,gBAAgB,CAAC;oCAC1D,gBAAgB;gCAClB;gCACA,kBAAkB,CAAC;oCACjB,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;gCACjD;;;;;;;uBAzBE;;;;;;;;;;;;;;;;;AAgChB", "debugId": null}}]}