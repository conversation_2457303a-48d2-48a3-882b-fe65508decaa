"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { type GameState } from "./MindExplorer";

interface GameUIProps {
  gameState: GameState;
  onModeChange: (mode: 'exploration' | 'connection' | 'memory') => void;
  onToggleGame: () => void;
  isGameActive: boolean;
}

export function GameUI({ gameState, onModeChange, onToggleGame, isGameActive }: GameUIProps) {
  const [showInstructions, setShowInstructions] = useState(false);
  const [discoveryCount, setDiscoveryCount] = useState(0);

  useEffect(() => {
    setDiscoveryCount(gameState.discoveredMemories.length);
  }, [gameState.discoveredMemories]);

  const modeDescriptions = {
    exploration: "Explore l'espace mental et découvre des fragments de mémoire cachés",
    connection: "Crée des connexions neuronales entre les lettres",
    memory: "Plonge dans les souvenirs découverts"
  };

  return (
    <div className="fixed inset-0 pointer-events-none z-50">
      {/* Bouton principal pour activer/désactiver le jeu */}
      <motion.button
        className="fixed top-6 right-6 pointer-events-auto bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-full font-medium shadow-lg hover:shadow-xl transition-all duration-300"
        onClick={onToggleGame}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1 }}
      >
        {isGameActive ? "Quitter l'exploration" : "Explorer l'esprit"}
      </motion.button>

      {/* Interface du jeu */}
      <AnimatePresence>
        {isGameActive && (
          <>
            {/* Panel de contrôle */}
            <motion.div
              className="fixed top-6 left-6 pointer-events-auto bg-black/20 backdrop-blur-md border border-white/10 rounded-2xl p-6 text-white max-w-sm"
              initial={{ opacity: 0, x: -100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -100 }}
              transition={{ duration: 0.5 }}
            >
              <h3 className="text-xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                Mind Explorer
              </h3>
              
              {/* Modes de jeu */}
              <div className="space-y-3 mb-6">
                <h4 className="text-sm font-medium text-gray-300">Mode d'exploration</h4>
                {(['exploration', 'connection', 'memory'] as const).map((mode) => (
                  <button
                    key={mode}
                    onClick={() => onModeChange(mode)}
                    className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${
                      gameState.mode === mode
                        ? 'bg-gradient-to-r from-purple-500/30 to-blue-500/30 border border-purple-400/50'
                        : 'bg-white/5 hover:bg-white/10 border border-transparent'
                    }`}
                  >
                    <div className="font-medium capitalize">{mode}</div>
                    <div className="text-xs text-gray-400 mt-1">
                      {modeDescriptions[mode]}
                    </div>
                  </button>
                ))}
              </div>

              {/* Statistiques */}
              <div className="space-y-2 mb-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-300">Mémoires découvertes</span>
                  <span className="text-lg font-bold text-purple-400">{discoveryCount}/5</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${(discoveryCount / 5) * 100}%` }}
                  />
                </div>
              </div>

              {/* Bouton d'instructions */}
              <button
                onClick={() => setShowInstructions(!showInstructions)}
                className="w-full text-center text-sm text-gray-400 hover:text-white transition-colors"
              >
                {showInstructions ? "Masquer" : "Afficher"} les instructions
              </button>
            </motion.div>

            {/* Instructions détaillées */}
            <AnimatePresence>
              {showInstructions && (
                <motion.div
                  className="fixed bottom-6 left-6 right-6 pointer-events-auto bg-black/20 backdrop-blur-md border border-white/10 rounded-2xl p-6 text-white max-w-2xl mx-auto"
                  initial={{ opacity: 0, y: 100 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 100 }}
                  transition={{ duration: 0.3 }}
                >
                  <h4 className="text-lg font-bold mb-4 text-purple-400">Comment jouer</h4>
                  <div className="grid md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <h5 className="font-medium text-blue-400 mb-2">🌌 Exploration</h5>
                      <p className="text-gray-300">
                        Navigue librement dans l'espace mental. Clique sur les sphères colorées pour découvrir des fragments de mémoire cachés.
                      </p>
                    </div>
                    <div>
                      <h5 className="font-medium text-purple-400 mb-2">🔗 Connexion</h5>
                      <p className="text-gray-300">
                        Observe les connexions neuronales qui se forment entre les lettres au fur et à mesure de tes découvertes.
                      </p>
                    </div>
                    <div>
                      <h5 className="font-medium text-pink-400 mb-2">💭 Mémoire</h5>
                      <p className="text-gray-300">
                        Plonge dans les souvenirs découverts et explore les profondeurs de la conscience.
                      </p>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-lg">
                    <p className="text-xs text-gray-300">
                      💡 <strong>Astuce :</strong> Utilise la molette de la souris pour zoomer et les clics pour faire tourner la vue. 
                      Chaque fragment découvert révèle une partie de l'histoire cachée.
                    </p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Notifications de découverte */}
            <AnimatePresence>
              {discoveryCount > 0 && (
                <motion.div
                  className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-full shadow-2xl">
                    <div className="text-center">
                      <div className="text-2xl font-bold">✨ Découverte !</div>
                      <div className="text-sm opacity-90">Fragment de mémoire #{discoveryCount}</div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Particules d'ambiance */}
            <div className="fixed inset-0 pointer-events-none overflow-hidden">
              {[...Array(20)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 bg-purple-400 rounded-full opacity-30"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                  }}
                  animate={{
                    y: [0, -100, 0],
                    opacity: [0.3, 0.8, 0.3],
                  }}
                  transition={{
                    duration: 3 + Math.random() * 2,
                    repeat: Infinity,
                    delay: Math.random() * 2,
                  }}
                />
              ))}
            </div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}
