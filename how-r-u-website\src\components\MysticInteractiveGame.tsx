"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Canvas, useFrame } from "@react-three/fiber";
import { Float, Text3D, Environment } from "@react-three/drei";
import * as THREE from "three";
import { MysticSoundscape, useMysticSounds } from "./MysticSoundscape";

interface GameState {
  phase: "awakening" | "exploration" | "ritual" | "transcendence" | "void";
  level: number;
  souls: number;
  whispers: string[];
  activeLetters: boolean[];
  mysticalEnergy: number;
  darkSecrets: string[];
  isChanneling: boolean;
  voidDepth: number;
}

interface MysticLetterProps {
  letter: string;
  position: [number, number, number];
  index: number;
  isActive: boolean;
  isChanneling: boolean;
  mysticalEnergy: number;
  onClick: () => void;
}

function MysticLetter({
  letter,
  position,
  index,
  isActive,
  isChanneling,
  mysticalEnergy,
  onClick,
}: MysticLetterProps) {
  const meshRef = useRef<THREE.Mesh>(null);
  const [isHovered, setIsHovered] = useState(false);

  useFrame((state) => {
    if (meshRef.current) {
      // Animation mystique de base
      const time = state.clock.elapsedTime;
      meshRef.current.position.y =
        position[1] + Math.sin(time + index * 2) * 0.3;

      // Rotation mystique
      meshRef.current.rotation.x = Math.sin(time * 0.5 + index) * 0.2;
      meshRef.current.rotation.z = Math.cos(time * 0.3 + index) * 0.1;

      if (isChanneling) {
        // Animation de canalisation d'énergie
        meshRef.current.rotation.y += 0.05;
        meshRef.current.scale.setScalar(1.5 + Math.sin(time * 8) * 0.3);

        // Effet de pulsation mystique
        const intensity = 1 + Math.sin(time * 10) * 0.5;
        meshRef.current.material.emissive.setRGB(
          intensity * 0.3,
          intensity * 0.1,
          intensity * 0.8
        );
      } else if (isActive) {
        meshRef.current.rotation.y += 0.02;
        meshRef.current.scale.setScalar(1.2 + Math.sin(time * 4) * 0.1);

        // Lueur surnaturelle
        meshRef.current.material.emissive.setRGB(0.2, 0.05, 0.4);
      } else if (isHovered) {
        meshRef.current.scale.setScalar(1.1);
        meshRef.current.material.emissive.setRGB(0.1, 0.02, 0.2);
      } else {
        meshRef.current.scale.setScalar(1);
        meshRef.current.material.emissive.setRGB(0, 0, 0);
      }
    }
  });

  return (
    <Float speed={3} rotationIntensity={0.8} floatIntensity={0.6}>
      <Text3D
        ref={meshRef}
        font="/fonts/helvetiker_regular.typeface.json"
        size={1.8}
        height={0.8}
        position={position}
        onPointerOver={() => setIsHovered(true)}
        onPointerOut={() => setIsHovered(false)}
        onClick={onClick}
        curveSegments={24}
      >
        {letter}
        <meshStandardMaterial
          color={
            isChanneling
              ? "#8a2be2"
              : isActive
              ? "#6a0dad"
              : isHovered
              ? "#4b0082"
              : "#2e2e2e"
          }
          metalness={0.9}
          roughness={0.1}
          emissive="#000000"
        />
      </Text3D>
    </Float>
  );
}

function MysticParticles() {
  const pointsRef = useRef<THREE.Points>(null);
  const particleCount = 2000;

  const positions = new Float32Array(particleCount * 3);
  const colors = new Float32Array(particleCount * 3);

  for (let i = 0; i < particleCount; i++) {
    positions[i * 3] = (Math.random() - 0.5) * 50;
    positions[i * 3 + 1] = (Math.random() - 0.5) * 50;
    positions[i * 3 + 2] = (Math.random() - 0.5) * 50;

    // Couleurs mystiques
    colors[i * 3] = Math.random() * 0.5 + 0.3; // Rouge
    colors[i * 3 + 1] = Math.random() * 0.2; // Vert
    colors[i * 3 + 2] = Math.random() * 0.8 + 0.2; // Bleu
  }

  useFrame((state) => {
    if (pointsRef.current) {
      pointsRef.current.rotation.y = state.clock.elapsedTime * 0.02;
      pointsRef.current.rotation.x = state.clock.elapsedTime * 0.01;
    }
  });

  return (
    <points ref={pointsRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={positions}
          itemSize={3}
        />
        <bufferAttribute
          attach="attributes-color"
          count={particleCount}
          array={colors}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.05}
        vertexColors
        transparent
        opacity={0.8}
        sizeAttenuation
        blending={THREE.AdditiveBlending}
      />
    </points>
  );
}

interface MysticInteractiveGameProps {
  onGameComplete?: (souls: number) => void;
  onSecretUnlocked?: (secret: string) => void;
}

export function MysticInteractiveGame({
  onGameComplete,
  onSecretUnlocked,
}: MysticInteractiveGameProps) {
  const [gameState, setGameState] = useState<GameState>({
    phase: "awakening",
    level: 1,
    souls: 0,
    whispers: [],
    activeLetters: [false, false, false, false, false],
    mysticalEnergy: 0,
    darkSecrets: [],
    isChanneling: false,
    voidDepth: 0,
  });

  const [currentWhisper, setCurrentWhisper] = useState("");
  const [isMobile, setIsMobile] = useState(false);
  const { playLetterSound, playSecretSound, playRitualSound } =
    useMysticSounds();

  const letters = ["h", "o", "w", "r", "u"];
  const positions: [number, number, number][] = isMobile
    ? [
        [-2, 2, 0],
        [0, 1, 0],
        [2, 0, 0],
        [-1, -1, 0],
        [1, -2, 0],
      ]
    : [
        [-6, 0, 0],
        [-3, 0, 0],
        [0, 0, 0],
        [3, 0, 0],
        [6, 0, 0],
      ];

  const whispers = [
    "les âmes murmurent dans l'obscurité...",
    "tu entends les échos du vide...",
    "les lettres révèlent leurs secrets...",
    "l'énergie mystique s'éveille...",
    "les ombres dansent autour de toi...",
    "le rituel commence...",
    "tu touches l'essence de l'inconnu...",
    "les dimensions se plient à ta volonté...",
    "tu transcendes la réalité...",
    "le vide t'appelle...",
  ];

  const darkSecrets = [
    "La première lettre cache l'origine de tout",
    "L'ordre des lettres révèle le chemin vers l'au-delà",
    "Cinq âmes sont nécessaires pour ouvrir le portail",
    "Le vide n'est que le début de l'infini",
    "Chaque clic libère une parcelle d'éternité",
  ];

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const handleLetterClick = useCallback(
    (index: number) => {
      if (gameState.isChanneling) return;

      // Jouer le son de la lettre
      playLetterSound(index);

      const newActiveLetters = [...gameState.activeLetters];
      newActiveLetters[index] = !newActiveLetters[index];

      const activeCount = newActiveLetters.filter(Boolean).length;
      const newMysticalEnergy = Math.min(gameState.mysticalEnergy + 20, 100);
      const newSouls = gameState.souls + (newActiveLetters[index] ? 1 : 0);

      // Nouveau murmure aléatoire
      const randomWhisper =
        whispers[Math.floor(Math.random() * whispers.length)];
      setCurrentWhisper(randomWhisper);

      // Progression des phases
      let newPhase = gameState.phase;
      if (activeCount >= 3 && gameState.phase === "awakening") {
        newPhase = "exploration";
      } else if (activeCount >= 5 && gameState.phase === "exploration") {
        newPhase = "ritual";
        playRitualSound(); // Son de rituel
        setGameState((prev) => ({ ...prev, isChanneling: true }));
        setTimeout(() => {
          setGameState((prev) => ({
            ...prev,
            isChanneling: false,
            phase: "transcendence",
          }));
        }, 3000);
      }

      // Révélation de secrets
      if (
        newSouls > 0 &&
        newSouls % 5 === 0 &&
        gameState.darkSecrets.length < darkSecrets.length
      ) {
        const newSecret = darkSecrets[gameState.darkSecrets.length];
        playSecretSound(); // Son spécial pour les secrets
        setGameState((prev) => ({
          ...prev,
          darkSecrets: [...prev.darkSecrets, newSecret],
        }));
        onSecretUnlocked?.(newSecret);
      }

      setGameState((prev) => ({
        ...prev,
        activeLetters: newActiveLetters,
        mysticalEnergy: newMysticalEnergy,
        souls: newSouls,
        phase: newPhase,
        whispers: [...prev.whispers.slice(-4), randomWhisper],
      }));

      // Completion du jeu
      if (newSouls >= 25) {
        onGameComplete?.(newSouls);
      }
    },
    [
      gameState,
      onGameComplete,
      onSecretUnlocked,
      playLetterSound,
      playSecretSound,
      playRitualSound,
    ]
  );

  return (
    <div className="w-full h-screen relative bg-black overflow-hidden">
      {/* Soundscape mystique */}
      <MysticSoundscape
        isActive={true}
        intensity={gameState.mysticalEnergy / 100}
        phase={gameState.phase}
      />

      {/* Canvas 3D */}
      <Canvas
        camera={{
          position: isMobile ? [0, 0, 10] : [0, 0, 18],
          fov: isMobile ? 75 : 70,
        }}
        gl={{ antialias: true, alpha: true }}
        style={{ width: "100%", height: "100%" }}
      >
        <ambientLight intensity={0.1} />
        <pointLight position={[10, 10, 10]} intensity={0.8} color="#8a2be2" />
        <pointLight
          position={[-10, -10, -10]}
          intensity={0.5}
          color="#4b0082"
        />
        <spotLight
          position={[0, 20, 10]}
          angle={0.5}
          penumbra={1}
          intensity={1}
          color="#6a0dad"
          castShadow
        />

        <Environment preset="night" />
        <MysticParticles />

        {letters.map((letter, index) => (
          <MysticLetter
            key={letter}
            letter={letter}
            position={positions[index]}
            index={index}
            isActive={gameState.activeLetters[index]}
            isChanneling={gameState.isChanneling}
            mysticalEnergy={gameState.mysticalEnergy}
            onClick={() => handleLetterClick(index)}
          />
        ))}
      </Canvas>

      {/* Interface mystique overlay */}
      <div className="absolute inset-0 pointer-events-none z-10">
        {/* Phase indicator */}
        <motion.div
          className="absolute top-8 left-8 text-purple-300"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <div className="text-sm font-mono mb-2">Phase: {gameState.phase}</div>
          <div className="text-xs">Âmes: {gameState.souls}</div>
          <div className="text-xs">Énergie: {gameState.mysticalEnergy}%</div>
        </motion.div>

        {/* Whispers */}
        <AnimatePresence>
          {currentWhisper && (
            <motion.div
              key={currentWhisper}
              className="absolute bottom-20 left-1/2 transform -translate-x-1/2 text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 2 }}
            >
              <div className="text-purple-200 text-sm font-light italic">
                {currentWhisper}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Dark secrets */}
        {gameState.darkSecrets.length > 0 && (
          <motion.div
            className="absolute top-8 right-8 max-w-xs"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
          >
            <div className="text-purple-300 text-xs font-mono mb-2">
              Secrets révélés:
            </div>
            {gameState.darkSecrets.map((secret, index) => (
              <motion.div
                key={index}
                className="text-purple-200 text-xs mb-1 opacity-80"
                initial={{ opacity: 0 }}
                animate={{ opacity: 0.8 }}
                transition={{ delay: index * 0.5 }}
              >
                • {secret}
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Channeling effect */}
        {gameState.isChanneling && (
          <motion.div
            className="absolute inset-0 bg-purple-900 bg-opacity-30"
            initial={{ opacity: 0 }}
            animate={{ opacity: [0, 0.5, 0] }}
            transition={{ duration: 3, repeat: Infinity }}
          >
            <div className="absolute inset-0 flex items-center justify-center">
              <motion.div
                className="text-purple-100 text-2xl font-light"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
              >
                Canalisation en cours...
              </motion.div>
            </div>
          </motion.div>
        )}

        {/* Instructions */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center pointer-events-none"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2 }}
        >
          <div className="text-purple-300 text-xs font-light">
            Cliquez sur les lettres pour éveiller leur pouvoir mystique
          </div>
          <div className="text-purple-400 text-xs font-light mt-1">
            Collectez des âmes pour révéler les secrets de l'au-delà
          </div>
        </motion.div>
      </div>
    </div>
  );
}
