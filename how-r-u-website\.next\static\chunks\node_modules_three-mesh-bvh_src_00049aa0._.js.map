{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/Constants.js"], "sourcesContent": ["// Split strategy constants\nexport const CENTER = 0;\nexport const AVERAGE = 1;\nexport const SAH = 2;\n\n// Traversal constants\nexport const NOT_INTERSECTED = 0;\nexport const INTERSECTED = 1;\nexport const CONTAINED = 2;\n\n// SAH cost constants\n// TODO: hone these costs more. The relative difference between them should be the\n// difference in measured time to perform a triangle intersection vs traversing\n// bounds.\nexport const TRIANGLE_INTERSECT_COST = 1.25;\nexport const TRAVERSAL_COST = 1;\n\n\n// Build constants\nexport const BYTES_PER_NODE = 6 * 4 + 4 + 4;\nexport const IS_LEAFNODE_FLAG = 0xFFFF;\n\n// EPSILON for computing floating point error during build\n// https://en.wikipedia.org/wiki/Machine_epsilon#Values_for_standard_hardware_floating_point_arithmetics\nexport const FLOAT32_EPSILON = Math.pow( 2, - 24 );\n\nexport const SKIP_GENERATION = Symbol( 'SKIP_GENERATION' );\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;;;;;;;;;;;AACpB,MAAM,SAAS;AACf,MAAM,UAAU;AAChB,MAAM,MAAM;AAGZ,MAAM,kBAAkB;AACxB,MAAM,cAAc;AACpB,MAAM,YAAY;AAMlB,MAAM,0BAA0B;AAChC,MAAM,iBAAiB;AAIvB,MAAM,iBAAiB,IAAI,IAAI,IAAI;AACnC,MAAM,mBAAmB;AAIzB,MAAM,kBAAkB,KAAK,GAAG,CAAE,GAAG,CAAE;AAEvC,MAAM,kBAAkB,OAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/utils/GeometryRayIntersectUtilities.js"], "sourcesContent": ["// converts the given BVH raycast intersection to align with the three.js raycast\n// structure (include object, world space distance and point).\nexport function convertRaycastIntersect( hit, object, raycaster ) {\n\n\tif ( hit === null ) {\n\n\t\treturn null;\n\n\t}\n\n\thit.point.applyMatrix4( object.matrixWorld );\n\thit.distance = hit.point.distanceTo( raycaster.ray.origin );\n\thit.object = object;\n\n\treturn hit;\n\n}\n"], "names": [], "mappings": "AAAA,iFAAiF;AACjF,8DAA8D;;;;AACvD,SAAS,wBAAyB,GAAG,EAAE,MAAM,EAAE,SAAS;IAE9D,IAAK,QAAQ,MAAO;QAEnB,OAAO;IAER;IAEA,IAAI,KAAK,CAAC,YAAY,CAAE,OAAO,WAAW;IAC1C,IAAI,QAAQ,GAAG,IAAI,KAAK,CAAC,UAAU,CAAE,UAAU,GAAG,CAAC,MAAM;IACzD,IAAI,MAAM,GAAG;IAEb,OAAO;AAER", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/build/geometryUtils.js"], "sourcesContent": ["import { BufferAttribute } from 'three';\n\nexport function getVertexCount( geo ) {\n\n\treturn geo.index ? geo.index.count : geo.attributes.position.count;\n\n}\n\nexport function getTriCount( geo ) {\n\n\treturn getVertexCount( geo ) / 3;\n\n}\n\nexport function getIndexArray( vertexCount, BufferConstructor = ArrayBuffer ) {\n\n\tif ( vertexCount > 65535 ) {\n\n\t\treturn new Uint32Array( new BufferConstructor( 4 * vertexCount ) );\n\n\t} else {\n\n\t\treturn new Uint16Array( new BufferConstructor( 2 * vertexCount ) );\n\n\t}\n\n}\n\n// ensures that an index is present on the geometry\nexport function ensureIndex( geo, options ) {\n\n\tif ( ! geo.index ) {\n\n\t\tconst vertexCount = geo.attributes.position.count;\n\t\tconst BufferConstructor = options.useSharedArrayBuffer ? SharedArrayBuffer : ArrayBuffer;\n\t\tconst index = getIndexArray( vertexCount, BufferConstructor );\n\t\tgeo.setIndex( new BufferAttribute( index, 1 ) );\n\n\t\tfor ( let i = 0; i < vertexCount; i ++ ) {\n\n\t\t\tindex[ i ] = i;\n\n\t\t}\n\n\t}\n\n}\n\n// Computes the set of { offset, count } ranges which need independent BVH roots. Each\n// region in the geometry index that belongs to a different set of material groups requires\n// a separate BVH root, so that triangles indices belonging to one group never get swapped\n// with triangle indices belongs to another group. For example, if the groups were like this:\n//\n// [-------------------------------------------------------------]\n// |__________________|\n//   g0 = [0, 20]  |______________________||_____________________|\n//                      g1 = [16, 40]           g2 = [41, 60]\n//\n// we would need four BVH roots: [0, 15], [16, 20], [21, 40], [41, 60].\nexport function getFullGeometryRange( geo, range ) {\n\n\tconst triCount = getTriCount( geo );\n\tconst drawRange = range ? range : geo.drawRange;\n\tconst start = drawRange.start / 3;\n\tconst end = ( drawRange.start + drawRange.count ) / 3;\n\n\tconst offset = Math.max( 0, start );\n\tconst count = Math.min( triCount, end ) - offset;\n\treturn [ {\n\t\toffset: Math.floor( offset ),\n\t\tcount: Math.floor( count ),\n\t} ];\n\n}\n\nexport function getRootIndexRanges( geo, range ) {\n\n\tif ( ! geo.groups || ! geo.groups.length ) {\n\n\t\treturn getFullGeometryRange( geo, range );\n\n\t}\n\n\tconst ranges = [];\n\tconst rangeBoundaries = new Set();\n\n\tconst drawRange = range ? range : geo.drawRange;\n\tconst drawRangeStart = drawRange.start / 3;\n\tconst drawRangeEnd = ( drawRange.start + drawRange.count ) / 3;\n\tfor ( const group of geo.groups ) {\n\n\t\tconst groupStart = group.start / 3;\n\t\tconst groupEnd = ( group.start + group.count ) / 3;\n\t\trangeBoundaries.add( Math.max( drawRangeStart, groupStart ) );\n\t\trangeBoundaries.add( Math.min( drawRangeEnd, groupEnd ) );\n\n\t}\n\n\n\t// note that if you don't pass in a comparator, it sorts them lexicographically as strings :-(\n\tconst sortedBoundaries = Array.from( rangeBoundaries.values() ).sort( ( a, b ) => a - b );\n\tfor ( let i = 0; i < sortedBoundaries.length - 1; i ++ ) {\n\n\t\tconst start = sortedBoundaries[ i ];\n\t\tconst end = sortedBoundaries[ i + 1 ];\n\n\t\tranges.push( {\n\t\t\toffset: Math.floor( start ),\n\t\t\tcount: Math.floor( end - start ),\n\t\t} );\n\n\t}\n\n\treturn ranges;\n\n}\n\nexport function hasGroupGaps( geometry, range ) {\n\n\tconst vertexCount = getTriCount( geometry );\n\tconst groups = getRootIndexRanges( geometry, range )\n\t\t.sort( ( a, b ) => a.offset - b.offset );\n\n\tconst finalGroup = groups[ groups.length - 1 ];\n\tfinalGroup.count = Math.min( vertexCount - finalGroup.offset, finalGroup.count );\n\n\tlet total = 0;\n\tgroups.forEach( ( { count } ) => total += count );\n\treturn vertexCount !== total;\n\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAEO,SAAS,eAAgB,GAAG;IAElC,OAAO,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK;AAEnE;AAEO,SAAS,YAAa,GAAG;IAE/B,OAAO,eAAgB,OAAQ;AAEhC;AAEO,SAAS,cAAe,WAAW,EAAE,oBAAoB,WAAW;IAE1E,IAAK,cAAc,OAAQ;QAE1B,OAAO,IAAI,YAAa,IAAI,kBAAmB,IAAI;IAEpD,OAAO;QAEN,OAAO,IAAI,YAAa,IAAI,kBAAmB,IAAI;IAEpD;AAED;AAGO,SAAS,YAAa,GAAG,EAAE,OAAO;IAExC,IAAK,CAAE,IAAI,KAAK,EAAG;QAElB,MAAM,cAAc,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK;QACjD,MAAM,oBAAoB,QAAQ,oBAAoB,GAAG,oBAAoB;QAC7E,MAAM,QAAQ,cAAe,aAAa;QAC1C,IAAI,QAAQ,CAAE,IAAI,kJAAA,CAAA,kBAAe,CAAE,OAAO;QAE1C,IAAM,IAAI,IAAI,GAAG,IAAI,aAAa,IAAO;YAExC,KAAK,CAAE,EAAG,GAAG;QAEd;IAED;AAED;AAaO,SAAS,qBAAsB,GAAG,EAAE,KAAK;IAE/C,MAAM,WAAW,YAAa;IAC9B,MAAM,YAAY,QAAQ,QAAQ,IAAI,SAAS;IAC/C,MAAM,QAAQ,UAAU,KAAK,GAAG;IAChC,MAAM,MAAM,CAAE,UAAU,KAAK,GAAG,UAAU,KAAK,AAAC,IAAI;IAEpD,MAAM,SAAS,KAAK,GAAG,CAAE,GAAG;IAC5B,MAAM,QAAQ,KAAK,GAAG,CAAE,UAAU,OAAQ;IAC1C,OAAO;QAAE;YACR,QAAQ,KAAK,KAAK,CAAE;YACpB,OAAO,KAAK,KAAK,CAAE;QACpB;KAAG;AAEJ;AAEO,SAAS,mBAAoB,GAAG,EAAE,KAAK;IAE7C,IAAK,CAAE,IAAI,MAAM,IAAI,CAAE,IAAI,MAAM,CAAC,MAAM,EAAG;QAE1C,OAAO,qBAAsB,KAAK;IAEnC;IAEA,MAAM,SAAS,EAAE;IACjB,MAAM,kBAAkB,IAAI;IAE5B,MAAM,YAAY,QAAQ,QAAQ,IAAI,SAAS;IAC/C,MAAM,iBAAiB,UAAU,KAAK,GAAG;IACzC,MAAM,eAAe,CAAE,UAAU,KAAK,GAAG,UAAU,KAAK,AAAC,IAAI;IAC7D,KAAM,MAAM,SAAS,IAAI,MAAM,CAAG;QAEjC,MAAM,aAAa,MAAM,KAAK,GAAG;QACjC,MAAM,WAAW,CAAE,MAAM,KAAK,GAAG,MAAM,KAAK,AAAC,IAAI;QACjD,gBAAgB,GAAG,CAAE,KAAK,GAAG,CAAE,gBAAgB;QAC/C,gBAAgB,GAAG,CAAE,KAAK,GAAG,CAAE,cAAc;IAE9C;IAGA,8FAA8F;IAC9F,MAAM,mBAAmB,MAAM,IAAI,CAAE,gBAAgB,MAAM,IAAK,IAAI,CAAE,CAAE,GAAG,IAAO,IAAI;IACtF,IAAM,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,GAAG,GAAG,IAAO;QAExD,MAAM,QAAQ,gBAAgB,CAAE,EAAG;QACnC,MAAM,MAAM,gBAAgB,CAAE,IAAI,EAAG;QAErC,OAAO,IAAI,CAAE;YACZ,QAAQ,KAAK,KAAK,CAAE;YACpB,OAAO,KAAK,KAAK,CAAE,MAAM;QAC1B;IAED;IAEA,OAAO;AAER;AAEO,SAAS,aAAc,QAAQ,EAAE,KAAK;IAE5C,MAAM,cAAc,YAAa;IACjC,MAAM,SAAS,mBAAoB,UAAU,OAC3C,IAAI,CAAE,CAAE,GAAG,IAAO,EAAE,MAAM,GAAG,EAAE,MAAM;IAEvC,MAAM,aAAa,MAAM,CAAE,OAAO,MAAM,GAAG,EAAG;IAC9C,WAAW,KAAK,GAAG,KAAK,GAAG,CAAE,cAAc,WAAW,MAAM,EAAE,WAAW,KAAK;IAE9E,IAAI,QAAQ;IACZ,OAAO,OAAO,CAAE,CAAE,EAAE,KAAK,EAAE,GAAM,SAAS;IAC1C,OAAO,gBAAgB;AAExB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/build/computeBoundsUtils.js"], "sourcesContent": ["import { FLOAT32_EPSILON } from '../Constants.js';\nimport { getTriCount } from './geometryUtils.js';\n\n// computes the union of the bounds of all of the given triangles and puts the resulting box in \"target\".\n// A bounding box is computed for the centroids of the triangles, as well, and placed in \"centroidTarget\".\n// These are computed together to avoid redundant accesses to bounds array.\nexport function getBounds( triangleBounds, offset, count, target, centroidTarget ) {\n\n\tlet minx = Infinity;\n\tlet miny = Infinity;\n\tlet minz = Infinity;\n\tlet maxx = - Infinity;\n\tlet maxy = - Infinity;\n\tlet maxz = - Infinity;\n\n\tlet cminx = Infinity;\n\tlet cminy = Infinity;\n\tlet cminz = Infinity;\n\tlet cmaxx = - Infinity;\n\tlet cmaxy = - Infinity;\n\tlet cmaxz = - Infinity;\n\n\tfor ( let i = offset * 6, end = ( offset + count ) * 6; i < end; i += 6 ) {\n\n\t\tconst cx = triangleBounds[ i + 0 ];\n\t\tconst hx = triangleBounds[ i + 1 ];\n\t\tconst lx = cx - hx;\n\t\tconst rx = cx + hx;\n\t\tif ( lx < minx ) minx = lx;\n\t\tif ( rx > maxx ) maxx = rx;\n\t\tif ( cx < cminx ) cminx = cx;\n\t\tif ( cx > cmaxx ) cmaxx = cx;\n\n\t\tconst cy = triangleBounds[ i + 2 ];\n\t\tconst hy = triangleBounds[ i + 3 ];\n\t\tconst ly = cy - hy;\n\t\tconst ry = cy + hy;\n\t\tif ( ly < miny ) miny = ly;\n\t\tif ( ry > maxy ) maxy = ry;\n\t\tif ( cy < cminy ) cminy = cy;\n\t\tif ( cy > cmaxy ) cmaxy = cy;\n\n\t\tconst cz = triangleBounds[ i + 4 ];\n\t\tconst hz = triangleBounds[ i + 5 ];\n\t\tconst lz = cz - hz;\n\t\tconst rz = cz + hz;\n\t\tif ( lz < minz ) minz = lz;\n\t\tif ( rz > maxz ) maxz = rz;\n\t\tif ( cz < cminz ) cminz = cz;\n\t\tif ( cz > cmaxz ) cmaxz = cz;\n\n\t}\n\n\ttarget[ 0 ] = minx;\n\ttarget[ 1 ] = miny;\n\ttarget[ 2 ] = minz;\n\n\ttarget[ 3 ] = maxx;\n\ttarget[ 4 ] = maxy;\n\ttarget[ 5 ] = maxz;\n\n\tcentroidTarget[ 0 ] = cminx;\n\tcentroidTarget[ 1 ] = cminy;\n\tcentroidTarget[ 2 ] = cminz;\n\n\tcentroidTarget[ 3 ] = cmaxx;\n\tcentroidTarget[ 4 ] = cmaxy;\n\tcentroidTarget[ 5 ] = cmaxz;\n\n}\n\n// precomputes the bounding box for each triangle; required for quickly calculating tree splits.\n// result is an array of size tris.length * 6 where triangle i maps to a\n// [x_center, x_delta, y_center, y_delta, z_center, z_delta] tuple starting at index i * 6,\n// representing the center and half-extent in each dimension of triangle i\nexport function computeTriangleBounds( geo, target = null, offset = null, count = null ) {\n\n\tconst posAttr = geo.attributes.position;\n\tconst index = geo.index ? geo.index.array : null;\n\tconst triCount = getTriCount( geo );\n\tconst normalized = posAttr.normalized;\n\tlet triangleBounds;\n\tif ( target === null ) {\n\n\t\ttriangleBounds = new Float32Array( triCount * 6 );\n\t\toffset = 0;\n\t\tcount = triCount;\n\n\t} else {\n\n\t\ttriangleBounds = target;\n\t\toffset = offset || 0;\n\t\tcount = count || triCount;\n\n\t}\n\n\t// used for non-normalized positions\n\tconst posArr = posAttr.array;\n\n\t// support for an interleaved position buffer\n\tconst bufferOffset = posAttr.offset || 0;\n\tlet stride = 3;\n\tif ( posAttr.isInterleavedBufferAttribute ) {\n\n\t\tstride = posAttr.data.stride;\n\n\t}\n\n\t// used for normalized positions\n\tconst getters = [ 'getX', 'getY', 'getZ' ];\n\n\tfor ( let tri = offset; tri < offset + count; tri ++ ) {\n\n\t\tconst tri3 = tri * 3;\n\t\tconst tri6 = tri * 6;\n\n\t\tlet ai = tri3 + 0;\n\t\tlet bi = tri3 + 1;\n\t\tlet ci = tri3 + 2;\n\n\t\tif ( index ) {\n\n\t\t\tai = index[ ai ];\n\t\t\tbi = index[ bi ];\n\t\t\tci = index[ ci ];\n\n\t\t}\n\n\t\t// we add the stride and offset here since we access the array directly\n\t\t// below for the sake of performance\n\t\tif ( ! normalized ) {\n\n\t\t\tai = ai * stride + bufferOffset;\n\t\t\tbi = bi * stride + bufferOffset;\n\t\t\tci = ci * stride + bufferOffset;\n\n\t\t}\n\n\t\tfor ( let el = 0; el < 3; el ++ ) {\n\n\t\t\tlet a, b, c;\n\n\t\t\tif ( normalized ) {\n\n\t\t\t\ta = posAttr[ getters[ el ] ]( ai );\n\t\t\t\tb = posAttr[ getters[ el ] ]( bi );\n\t\t\t\tc = posAttr[ getters[ el ] ]( ci );\n\n\t\t\t} else {\n\n\t\t\t\ta = posArr[ ai + el ];\n\t\t\t\tb = posArr[ bi + el ];\n\t\t\t\tc = posArr[ ci + el ];\n\n\t\t\t}\n\n\t\t\tlet min = a;\n\t\t\tif ( b < min ) min = b;\n\t\t\tif ( c < min ) min = c;\n\n\t\t\tlet max = a;\n\t\t\tif ( b > max ) max = b;\n\t\t\tif ( c > max ) max = c;\n\n\t\t\t// Increase the bounds size by float32 epsilon to avoid precision errors when\n\t\t\t// converting to 32 bit float. Scale the epsilon by the size of the numbers being\n\t\t\t// worked with.\n\t\t\tconst halfExtents = ( max - min ) / 2;\n\t\t\tconst el2 = el * 2;\n\t\t\ttriangleBounds[ tri6 + el2 + 0 ] = min + halfExtents;\n\t\t\ttriangleBounds[ tri6 + el2 + 1 ] = halfExtents + ( Math.abs( min ) + halfExtents ) * FLOAT32_EPSILON;\n\n\t\t}\n\n\t}\n\n\treturn triangleBounds;\n\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAKO,SAAS,UAAW,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,cAAc;IAE/E,IAAI,OAAO;IACX,IAAI,OAAO;IACX,IAAI,OAAO;IACX,IAAI,OAAO,CAAE;IACb,IAAI,OAAO,CAAE;IACb,IAAI,OAAO,CAAE;IAEb,IAAI,QAAQ;IACZ,IAAI,QAAQ;IACZ,IAAI,QAAQ;IACZ,IAAI,QAAQ,CAAE;IACd,IAAI,QAAQ,CAAE;IACd,IAAI,QAAQ,CAAE;IAEd,IAAM,IAAI,IAAI,SAAS,GAAG,MAAM,CAAE,SAAS,KAAM,IAAI,GAAG,IAAI,KAAK,KAAK,EAAI;QAEzE,MAAM,KAAK,cAAc,CAAE,IAAI,EAAG;QAClC,MAAM,KAAK,cAAc,CAAE,IAAI,EAAG;QAClC,MAAM,KAAK,KAAK;QAChB,MAAM,KAAK,KAAK;QAChB,IAAK,KAAK,MAAO,OAAO;QACxB,IAAK,KAAK,MAAO,OAAO;QACxB,IAAK,KAAK,OAAQ,QAAQ;QAC1B,IAAK,KAAK,OAAQ,QAAQ;QAE1B,MAAM,KAAK,cAAc,CAAE,IAAI,EAAG;QAClC,MAAM,KAAK,cAAc,CAAE,IAAI,EAAG;QAClC,MAAM,KAAK,KAAK;QAChB,MAAM,KAAK,KAAK;QAChB,IAAK,KAAK,MAAO,OAAO;QACxB,IAAK,KAAK,MAAO,OAAO;QACxB,IAAK,KAAK,OAAQ,QAAQ;QAC1B,IAAK,KAAK,OAAQ,QAAQ;QAE1B,MAAM,KAAK,cAAc,CAAE,IAAI,EAAG;QAClC,MAAM,KAAK,cAAc,CAAE,IAAI,EAAG;QAClC,MAAM,KAAK,KAAK;QAChB,MAAM,KAAK,KAAK;QAChB,IAAK,KAAK,MAAO,OAAO;QACxB,IAAK,KAAK,MAAO,OAAO;QACxB,IAAK,KAAK,OAAQ,QAAQ;QAC1B,IAAK,KAAK,OAAQ,QAAQ;IAE3B;IAEA,MAAM,CAAE,EAAG,GAAG;IACd,MAAM,CAAE,EAAG,GAAG;IACd,MAAM,CAAE,EAAG,GAAG;IAEd,MAAM,CAAE,EAAG,GAAG;IACd,MAAM,CAAE,EAAG,GAAG;IACd,MAAM,CAAE,EAAG,GAAG;IAEd,cAAc,CAAE,EAAG,GAAG;IACtB,cAAc,CAAE,EAAG,GAAG;IACtB,cAAc,CAAE,EAAG,GAAG;IAEtB,cAAc,CAAE,EAAG,GAAG;IACtB,cAAc,CAAE,EAAG,GAAG;IACtB,cAAc,CAAE,EAAG,GAAG;AAEvB;AAMO,SAAS,sBAAuB,GAAG,EAAE,SAAS,IAAI,EAAE,SAAS,IAAI,EAAE,QAAQ,IAAI;IAErF,MAAM,UAAU,IAAI,UAAU,CAAC,QAAQ;IACvC,MAAM,QAAQ,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG;IAC5C,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAG;IAC9B,MAAM,aAAa,QAAQ,UAAU;IACrC,IAAI;IACJ,IAAK,WAAW,MAAO;QAEtB,iBAAiB,IAAI,aAAc,WAAW;QAC9C,SAAS;QACT,QAAQ;IAET,OAAO;QAEN,iBAAiB;QACjB,SAAS,UAAU;QACnB,QAAQ,SAAS;IAElB;IAEA,oCAAoC;IACpC,MAAM,SAAS,QAAQ,KAAK;IAE5B,6CAA6C;IAC7C,MAAM,eAAe,QAAQ,MAAM,IAAI;IACvC,IAAI,SAAS;IACb,IAAK,QAAQ,4BAA4B,EAAG;QAE3C,SAAS,QAAQ,IAAI,CAAC,MAAM;IAE7B;IAEA,gCAAgC;IAChC,MAAM,UAAU;QAAE;QAAQ;QAAQ;KAAQ;IAE1C,IAAM,IAAI,MAAM,QAAQ,MAAM,SAAS,OAAO,MAAS;QAEtD,MAAM,OAAO,MAAM;QACnB,MAAM,OAAO,MAAM;QAEnB,IAAI,KAAK,OAAO;QAChB,IAAI,KAAK,OAAO;QAChB,IAAI,KAAK,OAAO;QAEhB,IAAK,OAAQ;YAEZ,KAAK,KAAK,CAAE,GAAI;YAChB,KAAK,KAAK,CAAE,GAAI;YAChB,KAAK,KAAK,CAAE,GAAI;QAEjB;QAEA,uEAAuE;QACvE,oCAAoC;QACpC,IAAK,CAAE,YAAa;YAEnB,KAAK,KAAK,SAAS;YACnB,KAAK,KAAK,SAAS;YACnB,KAAK,KAAK,SAAS;QAEpB;QAEA,IAAM,IAAI,KAAK,GAAG,KAAK,GAAG,KAAQ;YAEjC,IAAI,GAAG,GAAG;YAEV,IAAK,YAAa;gBAEjB,IAAI,OAAO,CAAE,OAAO,CAAE,GAAI,CAAE,CAAE;gBAC9B,IAAI,OAAO,CAAE,OAAO,CAAE,GAAI,CAAE,CAAE;gBAC9B,IAAI,OAAO,CAAE,OAAO,CAAE,GAAI,CAAE,CAAE;YAE/B,OAAO;gBAEN,IAAI,MAAM,CAAE,KAAK,GAAI;gBACrB,IAAI,MAAM,CAAE,KAAK,GAAI;gBACrB,IAAI,MAAM,CAAE,KAAK,GAAI;YAEtB;YAEA,IAAI,MAAM;YACV,IAAK,IAAI,KAAM,MAAM;YACrB,IAAK,IAAI,KAAM,MAAM;YAErB,IAAI,MAAM;YACV,IAAK,IAAI,KAAM,MAAM;YACrB,IAAK,IAAI,KAAM,MAAM;YAErB,6EAA6E;YAC7E,iFAAiF;YACjF,eAAe;YACf,MAAM,cAAc,CAAE,MAAM,GAAI,IAAI;YACpC,MAAM,MAAM,KAAK;YACjB,cAAc,CAAE,OAAO,MAAM,EAAG,GAAG,MAAM;YACzC,cAAc,CAAE,OAAO,MAAM,EAAG,GAAG,cAAc,CAAE,KAAK,GAAG,CAAE,OAAQ,WAAY,IAAI,mKAAA,CAAA,kBAAe;QAErG;IAED;IAEA,OAAO;AAER", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/utils/ArrayBoxUtilities.js"], "sourcesContent": ["export function arrayToBox( nodeIndex32, array, target ) {\n\n\ttarget.min.x = array[ nodeIndex32 ];\n\ttarget.min.y = array[ nodeIndex32 + 1 ];\n\ttarget.min.z = array[ nodeIndex32 + 2 ];\n\n\ttarget.max.x = array[ nodeIndex32 + 3 ];\n\ttarget.max.y = array[ nodeIndex32 + 4 ];\n\ttarget.max.z = array[ nodeIndex32 + 5 ];\n\n\treturn target;\n\n}\n\nexport function makeEmptyBounds( target ) {\n\n\ttarget[ 0 ] = target[ 1 ] = target[ 2 ] = Infinity;\n\ttarget[ 3 ] = target[ 4 ] = target[ 5 ] = - Infinity;\n\n}\n\nexport function getLongestEdgeIndex( bounds ) {\n\n\tlet splitDimIdx = - 1;\n\tlet splitDist = - Infinity;\n\n\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\tconst dist = bounds[ i + 3 ] - bounds[ i ];\n\t\tif ( dist > splitDist ) {\n\n\t\t\tsplitDist = dist;\n\t\t\tsplitDimIdx = i;\n\n\t\t}\n\n\t}\n\n\treturn splitDimIdx;\n\n}\n\n// copies bounds a into bounds b\nexport function copyBounds( source, target ) {\n\n\ttarget.set( source );\n\n}\n\n// sets bounds target to the union of bounds a and b\nexport function unionBounds( a, b, target ) {\n\n\tlet aVal, bVal;\n\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\tconst d3 = d + 3;\n\n\t\t// set the minimum values\n\t\taVal = a[ d ];\n\t\tbVal = b[ d ];\n\t\ttarget[ d ] = aVal < bVal ? aVal : bVal;\n\n\t\t// set the max values\n\t\taVal = a[ d3 ];\n\t\tbVal = b[ d3 ];\n\t\ttarget[ d3 ] = aVal > bVal ? aVal : bVal;\n\n\t}\n\n}\n\n// expands the given bounds by the provided triangle bounds\nexport function expandByTriangleBounds( startIndex, triangleBounds, bounds ) {\n\n\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\tconst tCenter = triangleBounds[ startIndex + 2 * d ];\n\t\tconst tHalf = triangleBounds[ startIndex + 2 * d + 1 ];\n\n\t\tconst tMin = tCenter - tHalf;\n\t\tconst tMax = tCenter + tHalf;\n\n\t\tif ( tMin < bounds[ d ] ) {\n\n\t\t\tbounds[ d ] = tMin;\n\n\t\t}\n\n\t\tif ( tMax > bounds[ d + 3 ] ) {\n\n\t\t\tbounds[ d + 3 ] = tMax;\n\n\t\t}\n\n\t}\n\n}\n\n// compute bounds surface area\nexport function computeSurfaceArea( bounds ) {\n\n\tconst d0 = bounds[ 3 ] - bounds[ 0 ];\n\tconst d1 = bounds[ 4 ] - bounds[ 1 ];\n\tconst d2 = bounds[ 5 ] - bounds[ 2 ];\n\n\treturn 2 * ( d0 * d1 + d1 * d2 + d2 * d0 );\n\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAO,SAAS,WAAY,WAAW,EAAE,KAAK,EAAE,MAAM;IAErD,OAAO,GAAG,CAAC,CAAC,GAAG,KAAK,CAAE,YAAa;IACnC,OAAO,GAAG,CAAC,CAAC,GAAG,KAAK,CAAE,cAAc,EAAG;IACvC,OAAO,GAAG,CAAC,CAAC,GAAG,KAAK,CAAE,cAAc,EAAG;IAEvC,OAAO,GAAG,CAAC,CAAC,GAAG,KAAK,CAAE,cAAc,EAAG;IACvC,OAAO,GAAG,CAAC,CAAC,GAAG,KAAK,CAAE,cAAc,EAAG;IACvC,OAAO,GAAG,CAAC,CAAC,GAAG,KAAK,CAAE,cAAc,EAAG;IAEvC,OAAO;AAER;AAEO,SAAS,gBAAiB,MAAM;IAEtC,MAAM,CAAE,EAAG,GAAG,MAAM,CAAE,EAAG,GAAG,MAAM,CAAE,EAAG,GAAG;IAC1C,MAAM,CAAE,EAAG,GAAG,MAAM,CAAE,EAAG,GAAG,MAAM,CAAE,EAAG,GAAG,CAAE;AAE7C;AAEO,SAAS,oBAAqB,MAAM;IAE1C,IAAI,cAAc,CAAE;IACpB,IAAI,YAAY,CAAE;IAElB,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;QAE9B,MAAM,OAAO,MAAM,CAAE,IAAI,EAAG,GAAG,MAAM,CAAE,EAAG;QAC1C,IAAK,OAAO,WAAY;YAEvB,YAAY;YACZ,cAAc;QAEf;IAED;IAEA,OAAO;AAER;AAGO,SAAS,WAAY,MAAM,EAAE,MAAM;IAEzC,OAAO,GAAG,CAAE;AAEb;AAGO,SAAS,YAAa,CAAC,EAAE,CAAC,EAAE,MAAM;IAExC,IAAI,MAAM;IACV,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;QAE9B,MAAM,KAAK,IAAI;QAEf,yBAAyB;QACzB,OAAO,CAAC,CAAE,EAAG;QACb,OAAO,CAAC,CAAE,EAAG;QACb,MAAM,CAAE,EAAG,GAAG,OAAO,OAAO,OAAO;QAEnC,qBAAqB;QACrB,OAAO,CAAC,CAAE,GAAI;QACd,OAAO,CAAC,CAAE,GAAI;QACd,MAAM,CAAE,GAAI,GAAG,OAAO,OAAO,OAAO;IAErC;AAED;AAGO,SAAS,uBAAwB,UAAU,EAAE,cAAc,EAAE,MAAM;IAEzE,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;QAE9B,MAAM,UAAU,cAAc,CAAE,aAAa,IAAI,EAAG;QACpD,MAAM,QAAQ,cAAc,CAAE,aAAa,IAAI,IAAI,EAAG;QAEtD,MAAM,OAAO,UAAU;QACvB,MAAM,OAAO,UAAU;QAEvB,IAAK,OAAO,MAAM,CAAE,EAAG,EAAG;YAEzB,MAAM,CAAE,EAAG,GAAG;QAEf;QAEA,IAAK,OAAO,MAAM,CAAE,IAAI,EAAG,EAAG;YAE7B,MAAM,CAAE,IAAI,EAAG,GAAG;QAEnB;IAED;AAED;AAGO,SAAS,mBAAoB,MAAM;IAEzC,MAAM,KAAK,MAAM,CAAE,EAAG,GAAG,MAAM,CAAE,EAAG;IACpC,MAAM,KAAK,MAAM,CAAE,EAAG,GAAG,MAAM,CAAE,EAAG;IACpC,MAAM,KAAK,MAAM,CAAE,EAAG,GAAG,MAAM,CAAE,EAAG;IAEpC,OAAO,IAAI,CAAE,KAAK,KAAK,KAAK,KAAK,KAAK,EAAG;AAE1C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/build/splitUtils.js"], "sourcesContent": ["import { getLongestEdgeIndex, computeSurfaceArea, copyBounds, unionBounds, expandByTriangleBounds } from '../../utils/ArrayBoxUtilities.js';\nimport { CENTER, AVERAGE, SAH, TRIANGLE_INTERSECT_COST, TRAVERSAL_COST } from '../Constants.js';\n\nconst BIN_COUNT = 32;\nconst binsSort = ( a, b ) => a.candidate - b.candidate;\nconst sahBins = new Array( BIN_COUNT ).fill().map( () => {\n\n\treturn {\n\n\t\tcount: 0,\n\t\tbounds: new Float32Array( 6 ),\n\t\trightCacheBounds: new Float32Array( 6 ),\n\t\tleftCacheBounds: new Float32Array( 6 ),\n\t\tcandidate: 0,\n\n\t};\n\n} );\nconst leftBounds = new Float32Array( 6 );\n\nexport function getOptimalSplit( nodeBoundingData, centroidBoundingData, triangleBounds, offset, count, strategy ) {\n\n\tlet axis = - 1;\n\tlet pos = 0;\n\n\t// Center\n\tif ( strategy === CENTER ) {\n\n\t\taxis = getLongestEdgeIndex( centroidBoundingData );\n\t\tif ( axis !== - 1 ) {\n\n\t\t\tpos = ( centroidBoundingData[ axis ] + centroidBoundingData[ axis + 3 ] ) / 2;\n\n\t\t}\n\n\t} else if ( strategy === AVERAGE ) {\n\n\t\taxis = getLongestEdgeIndex( nodeBoundingData );\n\t\tif ( axis !== - 1 ) {\n\n\t\t\tpos = getAverage( triangleBounds, offset, count, axis );\n\n\t\t}\n\n\t} else if ( strategy === SAH ) {\n\n\t\tconst rootSurfaceArea = computeSurfaceArea( nodeBoundingData );\n\t\tlet bestCost = TRIANGLE_INTERSECT_COST * count;\n\n\t\t// iterate over all axes\n\t\tconst cStart = offset * 6;\n\t\tconst cEnd = ( offset + count ) * 6;\n\t\tfor ( let a = 0; a < 3; a ++ ) {\n\n\t\t\tconst axisLeft = centroidBoundingData[ a ];\n\t\t\tconst axisRight = centroidBoundingData[ a + 3 ];\n\t\t\tconst axisLength = axisRight - axisLeft;\n\t\t\tconst binWidth = axisLength / BIN_COUNT;\n\n\t\t\t// If we have fewer triangles than we're planning to split then just check all\n\t\t\t// the triangle positions because it will be faster.\n\t\t\tif ( count < BIN_COUNT / 4 ) {\n\n\t\t\t\t// initialize the bin candidates\n\t\t\t\tconst truncatedBins = [ ...sahBins ];\n\t\t\t\ttruncatedBins.length = count;\n\n\t\t\t\t// set the candidates\n\t\t\t\tlet b = 0;\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6, b ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ b ];\n\t\t\t\t\tbin.candidate = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tbin.count = 0;\n\n\t\t\t\t\tconst {\n\t\t\t\t\t\tbounds,\n\t\t\t\t\t\tleftCacheBounds,\n\t\t\t\t\t\trightCacheBounds,\n\t\t\t\t\t} = bin;\n\t\t\t\t\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\t\t\t\t\trightCacheBounds[ d ] = Infinity;\n\t\t\t\t\t\trightCacheBounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t\tleftCacheBounds[ d ] = Infinity;\n\t\t\t\t\t\tleftCacheBounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t\tbounds[ d ] = Infinity;\n\t\t\t\t\t\tbounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t}\n\n\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bounds );\n\n\t\t\t\t}\n\n\t\t\t\ttruncatedBins.sort( binsSort );\n\n\t\t\t\t// remove redundant splits\n\t\t\t\tlet splitCount = count;\n\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\twhile ( bi + 1 < splitCount && truncatedBins[ bi + 1 ].candidate === bin.candidate ) {\n\n\t\t\t\t\t\ttruncatedBins.splice( bi + 1, 1 );\n\t\t\t\t\t\tsplitCount --;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// find the appropriate bin for each triangle and expand the bounds.\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6 ) {\n\n\t\t\t\t\tconst center = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\t\tif ( center >= bin.candidate ) {\n\n\t\t\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.rightCacheBounds );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.leftCacheBounds );\n\t\t\t\t\t\t\tbin.count ++;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// expand all the bounds\n\t\t\t\tfor ( let bi = 0; bi < splitCount; bi ++ ) {\n\n\t\t\t\t\tconst bin = truncatedBins[ bi ];\n\t\t\t\t\tconst leftCount = bin.count;\n\t\t\t\t\tconst rightCount = count - bin.count;\n\n\t\t\t\t\t// check the cost of this split\n\t\t\t\t\tconst leftBounds = bin.leftCacheBounds;\n\t\t\t\t\tconst rightBounds = bin.rightCacheBounds;\n\n\t\t\t\t\tlet leftProb = 0;\n\t\t\t\t\tif ( leftCount !== 0 ) {\n\n\t\t\t\t\t\tleftProb = computeSurfaceArea( leftBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tlet rightProb = 0;\n\t\t\t\t\tif ( rightCount !== 0 ) {\n\n\t\t\t\t\t\trightProb = computeSurfaceArea( rightBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst cost = TRAVERSAL_COST + TRIANGLE_INTERSECT_COST * (\n\t\t\t\t\t\tleftProb * leftCount + rightProb * rightCount\n\t\t\t\t\t);\n\n\t\t\t\t\tif ( cost < bestCost ) {\n\n\t\t\t\t\t\taxis = a;\n\t\t\t\t\t\tbestCost = cost;\n\t\t\t\t\t\tpos = bin.candidate;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\t// reset the bins\n\t\t\t\tfor ( let i = 0; i < BIN_COUNT; i ++ ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tbin.count = 0;\n\t\t\t\t\tbin.candidate = axisLeft + binWidth + i * binWidth;\n\n\t\t\t\t\tconst bounds = bin.bounds;\n\t\t\t\t\tfor ( let d = 0; d < 3; d ++ ) {\n\n\t\t\t\t\t\tbounds[ d ] = Infinity;\n\t\t\t\t\t\tbounds[ d + 3 ] = - Infinity;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// iterate over all center positions\n\t\t\t\tfor ( let c = cStart; c < cEnd; c += 6 ) {\n\n\t\t\t\t\tconst triCenter = triangleBounds[ c + 2 * a ];\n\t\t\t\t\tconst relativeCenter = triCenter - axisLeft;\n\n\t\t\t\t\t// in the partition function if the centroid lies on the split plane then it is\n\t\t\t\t\t// considered to be on the right side of the split\n\t\t\t\t\tlet binIndex = ~ ~ ( relativeCenter / binWidth );\n\t\t\t\t\tif ( binIndex >= BIN_COUNT ) binIndex = BIN_COUNT - 1;\n\n\t\t\t\t\tconst bin = sahBins[ binIndex ];\n\t\t\t\t\tbin.count ++;\n\n\t\t\t\t\texpandByTriangleBounds( c, triangleBounds, bin.bounds );\n\n\t\t\t\t}\n\n\t\t\t\t// cache the unioned bounds from right to left so we don't have to regenerate them each time\n\t\t\t\tconst lastBin = sahBins[ BIN_COUNT - 1 ];\n\t\t\t\tcopyBounds( lastBin.bounds, lastBin.rightCacheBounds );\n\t\t\t\tfor ( let i = BIN_COUNT - 2; i >= 0; i -- ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tconst nextBin = sahBins[ i + 1 ];\n\t\t\t\t\tunionBounds( bin.bounds, nextBin.rightCacheBounds, bin.rightCacheBounds );\n\n\t\t\t\t}\n\n\t\t\t\tlet leftCount = 0;\n\t\t\t\tfor ( let i = 0; i < BIN_COUNT - 1; i ++ ) {\n\n\t\t\t\t\tconst bin = sahBins[ i ];\n\t\t\t\t\tconst binCount = bin.count;\n\t\t\t\t\tconst bounds = bin.bounds;\n\n\t\t\t\t\tconst nextBin = sahBins[ i + 1 ];\n\t\t\t\t\tconst rightBounds = nextBin.rightCacheBounds;\n\n\t\t\t\t\t// don't do anything with the bounds if the new bounds have no triangles\n\t\t\t\t\tif ( binCount !== 0 ) {\n\n\t\t\t\t\t\tif ( leftCount === 0 ) {\n\n\t\t\t\t\t\t\tcopyBounds( bounds, leftBounds );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\tunionBounds( bounds, leftBounds, leftBounds );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\tleftCount += binCount;\n\n\t\t\t\t\t// check the cost of this split\n\t\t\t\t\tlet leftProb = 0;\n\t\t\t\t\tlet rightProb = 0;\n\n\t\t\t\t\tif ( leftCount !== 0 ) {\n\n\t\t\t\t\t\tleftProb = computeSurfaceArea( leftBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst rightCount = count - leftCount;\n\t\t\t\t\tif ( rightCount !== 0 ) {\n\n\t\t\t\t\t\trightProb = computeSurfaceArea( rightBounds ) / rootSurfaceArea;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst cost = TRAVERSAL_COST + TRIANGLE_INTERSECT_COST * (\n\t\t\t\t\t\tleftProb * leftCount + rightProb * rightCount\n\t\t\t\t\t);\n\n\t\t\t\t\tif ( cost < bestCost ) {\n\n\t\t\t\t\t\taxis = a;\n\t\t\t\t\t\tbestCost = cost;\n\t\t\t\t\t\tpos = bin.candidate;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t} else {\n\n\t\tconsole.warn( `MeshBVH: Invalid build strategy value ${ strategy } used.` );\n\n\t}\n\n\treturn { axis, pos };\n\n}\n\n// returns the average coordinate on the specified axis of the all the provided triangles\nfunction getAverage( triangleBounds, offset, count, axis ) {\n\n\tlet avg = 0;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tavg += triangleBounds[ i * 6 + axis * 2 ];\n\n\t}\n\n\treturn avg / count;\n\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,YAAY;AAClB,MAAM,WAAW,CAAE,GAAG,IAAO,EAAE,SAAS,GAAG,EAAE,SAAS;AACtD,MAAM,UAAU,IAAI,MAAO,WAAY,IAAI,GAAG,GAAG,CAAE;IAElD,OAAO;QAEN,OAAO;QACP,QAAQ,IAAI,aAAc;QAC1B,kBAAkB,IAAI,aAAc;QACpC,iBAAiB,IAAI,aAAc;QACnC,WAAW;IAEZ;AAED;AACA,MAAM,aAAa,IAAI,aAAc;AAE9B,SAAS,gBAAiB,gBAAgB,EAAE,oBAAoB,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ;IAE/G,IAAI,OAAO,CAAE;IACb,IAAI,MAAM;IAEV,SAAS;IACT,IAAK,aAAa,mKAAA,CAAA,SAAM,EAAG;QAE1B,OAAO,CAAA,GAAA,4KAAA,CAAA,sBAAmB,AAAD,EAAG;QAC5B,IAAK,SAAS,CAAE,GAAI;YAEnB,MAAM,CAAE,oBAAoB,CAAE,KAAM,GAAG,oBAAoB,CAAE,OAAO,EAAG,AAAC,IAAI;QAE7E;IAED,OAAO,IAAK,aAAa,mKAAA,CAAA,UAAO,EAAG;QAElC,OAAO,CAAA,GAAA,4KAAA,CAAA,sBAAmB,AAAD,EAAG;QAC5B,IAAK,SAAS,CAAE,GAAI;YAEnB,MAAM,WAAY,gBAAgB,QAAQ,OAAO;QAElD;IAED,OAAO,IAAK,aAAa,mKAAA,CAAA,MAAG,EAAG;QAE9B,MAAM,kBAAkB,CAAA,GAAA,4KAAA,CAAA,qBAAkB,AAAD,EAAG;QAC5C,IAAI,WAAW,mKAAA,CAAA,0BAAuB,GAAG;QAEzC,wBAAwB;QACxB,MAAM,SAAS,SAAS;QACxB,MAAM,OAAO,CAAE,SAAS,KAAM,IAAI;QAClC,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;YAE9B,MAAM,WAAW,oBAAoB,CAAE,EAAG;YAC1C,MAAM,YAAY,oBAAoB,CAAE,IAAI,EAAG;YAC/C,MAAM,aAAa,YAAY;YAC/B,MAAM,WAAW,aAAa;YAE9B,8EAA8E;YAC9E,oDAAoD;YACpD,IAAK,QAAQ,YAAY,GAAI;gBAE5B,gCAAgC;gBAChC,MAAM,gBAAgB;uBAAK;iBAAS;gBACpC,cAAc,MAAM,GAAG;gBAEvB,qBAAqB;gBACrB,IAAI,IAAI;gBACR,IAAM,IAAI,IAAI,QAAQ,IAAI,MAAM,KAAK,GAAG,IAAO;oBAE9C,MAAM,MAAM,aAAa,CAAE,EAAG;oBAC9B,IAAI,SAAS,GAAG,cAAc,CAAE,IAAI,IAAI,EAAG;oBAC3C,IAAI,KAAK,GAAG;oBAEZ,MAAM,EACL,MAAM,EACN,eAAe,EACf,gBAAgB,EAChB,GAAG;oBACJ,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;wBAE9B,gBAAgB,CAAE,EAAG,GAAG;wBACxB,gBAAgB,CAAE,IAAI,EAAG,GAAG,CAAE;wBAE9B,eAAe,CAAE,EAAG,GAAG;wBACvB,eAAe,CAAE,IAAI,EAAG,GAAG,CAAE;wBAE7B,MAAM,CAAE,EAAG,GAAG;wBACd,MAAM,CAAE,IAAI,EAAG,GAAG,CAAE;oBAErB;oBAEA,CAAA,GAAA,4KAAA,CAAA,yBAAsB,AAAD,EAAG,GAAG,gBAAgB;gBAE5C;gBAEA,cAAc,IAAI,CAAE;gBAEpB,0BAA0B;gBAC1B,IAAI,aAAa;gBACjB,IAAM,IAAI,KAAK,GAAG,KAAK,YAAY,KAAQ;oBAE1C,MAAM,MAAM,aAAa,CAAE,GAAI;oBAC/B,MAAQ,KAAK,IAAI,cAAc,aAAa,CAAE,KAAK,EAAG,CAAC,SAAS,KAAK,IAAI,SAAS,CAAG;wBAEpF,cAAc,MAAM,CAAE,KAAK,GAAG;wBAC9B;oBAED;gBAED;gBAEA,oEAAoE;gBACpE,IAAM,IAAI,IAAI,QAAQ,IAAI,MAAM,KAAK,EAAI;oBAExC,MAAM,SAAS,cAAc,CAAE,IAAI,IAAI,EAAG;oBAC1C,IAAM,IAAI,KAAK,GAAG,KAAK,YAAY,KAAQ;wBAE1C,MAAM,MAAM,aAAa,CAAE,GAAI;wBAC/B,IAAK,UAAU,IAAI,SAAS,EAAG;4BAE9B,CAAA,GAAA,4KAAA,CAAA,yBAAsB,AAAD,EAAG,GAAG,gBAAgB,IAAI,gBAAgB;wBAEhE,OAAO;4BAEN,CAAA,GAAA,4KAAA,CAAA,yBAAsB,AAAD,EAAG,GAAG,gBAAgB,IAAI,eAAe;4BAC9D,IAAI,KAAK;wBAEV;oBAED;gBAED;gBAEA,wBAAwB;gBACxB,IAAM,IAAI,KAAK,GAAG,KAAK,YAAY,KAAQ;oBAE1C,MAAM,MAAM,aAAa,CAAE,GAAI;oBAC/B,MAAM,YAAY,IAAI,KAAK;oBAC3B,MAAM,aAAa,QAAQ,IAAI,KAAK;oBAEpC,+BAA+B;oBAC/B,MAAM,aAAa,IAAI,eAAe;oBACtC,MAAM,cAAc,IAAI,gBAAgB;oBAExC,IAAI,WAAW;oBACf,IAAK,cAAc,GAAI;wBAEtB,WAAW,CAAA,GAAA,4KAAA,CAAA,qBAAkB,AAAD,EAAG,cAAe;oBAE/C;oBAEA,IAAI,YAAY;oBAChB,IAAK,eAAe,GAAI;wBAEvB,YAAY,CAAA,GAAA,4KAAA,CAAA,qBAAkB,AAAD,EAAG,eAAgB;oBAEjD;oBAEA,MAAM,OAAO,mKAAA,CAAA,iBAAc,GAAG,mKAAA,CAAA,0BAAuB,GAAG,CACvD,WAAW,YAAY,YAAY,UACpC;oBAEA,IAAK,OAAO,UAAW;wBAEtB,OAAO;wBACP,WAAW;wBACX,MAAM,IAAI,SAAS;oBAEpB;gBAED;YAED,OAAO;gBAEN,iBAAiB;gBACjB,IAAM,IAAI,IAAI,GAAG,IAAI,WAAW,IAAO;oBAEtC,MAAM,MAAM,OAAO,CAAE,EAAG;oBACxB,IAAI,KAAK,GAAG;oBACZ,IAAI,SAAS,GAAG,WAAW,WAAW,IAAI;oBAE1C,MAAM,SAAS,IAAI,MAAM;oBACzB,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;wBAE9B,MAAM,CAAE,EAAG,GAAG;wBACd,MAAM,CAAE,IAAI,EAAG,GAAG,CAAE;oBAErB;gBAED;gBAEA,oCAAoC;gBACpC,IAAM,IAAI,IAAI,QAAQ,IAAI,MAAM,KAAK,EAAI;oBAExC,MAAM,YAAY,cAAc,CAAE,IAAI,IAAI,EAAG;oBAC7C,MAAM,iBAAiB,YAAY;oBAEnC,+EAA+E;oBAC/E,kDAAkD;oBAClD,IAAI,WAAW,CAAE,CAAE,CAAE,iBAAiB,QAAS;oBAC/C,IAAK,YAAY,WAAY,WAAW,YAAY;oBAEpD,MAAM,MAAM,OAAO,CAAE,SAAU;oBAC/B,IAAI,KAAK;oBAET,CAAA,GAAA,4KAAA,CAAA,yBAAsB,AAAD,EAAG,GAAG,gBAAgB,IAAI,MAAM;gBAEtD;gBAEA,4FAA4F;gBAC5F,MAAM,UAAU,OAAO,CAAE,YAAY,EAAG;gBACxC,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,QAAQ,MAAM,EAAE,QAAQ,gBAAgB;gBACpD,IAAM,IAAI,IAAI,YAAY,GAAG,KAAK,GAAG,IAAO;oBAE3C,MAAM,MAAM,OAAO,CAAE,EAAG;oBACxB,MAAM,UAAU,OAAO,CAAE,IAAI,EAAG;oBAChC,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,IAAI,MAAM,EAAE,QAAQ,gBAAgB,EAAE,IAAI,gBAAgB;gBAExE;gBAEA,IAAI,YAAY;gBAChB,IAAM,IAAI,IAAI,GAAG,IAAI,YAAY,GAAG,IAAO;oBAE1C,MAAM,MAAM,OAAO,CAAE,EAAG;oBACxB,MAAM,WAAW,IAAI,KAAK;oBAC1B,MAAM,SAAS,IAAI,MAAM;oBAEzB,MAAM,UAAU,OAAO,CAAE,IAAI,EAAG;oBAChC,MAAM,cAAc,QAAQ,gBAAgB;oBAE5C,wEAAwE;oBACxE,IAAK,aAAa,GAAI;wBAErB,IAAK,cAAc,GAAI;4BAEtB,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,QAAQ;wBAErB,OAAO;4BAEN,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,QAAQ,YAAY;wBAElC;oBAED;oBAEA,aAAa;oBAEb,+BAA+B;oBAC/B,IAAI,WAAW;oBACf,IAAI,YAAY;oBAEhB,IAAK,cAAc,GAAI;wBAEtB,WAAW,CAAA,GAAA,4KAAA,CAAA,qBAAkB,AAAD,EAAG,cAAe;oBAE/C;oBAEA,MAAM,aAAa,QAAQ;oBAC3B,IAAK,eAAe,GAAI;wBAEvB,YAAY,CAAA,GAAA,4KAAA,CAAA,qBAAkB,AAAD,EAAG,eAAgB;oBAEjD;oBAEA,MAAM,OAAO,mKAAA,CAAA,iBAAc,GAAG,mKAAA,CAAA,0BAAuB,GAAG,CACvD,WAAW,YAAY,YAAY,UACpC;oBAEA,IAAK,OAAO,UAAW;wBAEtB,OAAO;wBACP,WAAW;wBACX,MAAM,IAAI,SAAS;oBAEpB;gBAED;YAED;QAED;IAED,OAAO;QAEN,QAAQ,IAAI,CAAE,CAAC,sCAAsC,EAAG,SAAU,MAAM,CAAC;IAE1E;IAEA,OAAO;QAAE;QAAM;IAAI;AAEpB;AAEA,yFAAyF;AACzF,SAAS,WAAY,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI;IAEvD,IAAI,MAAM;IACV,IAAM,IAAI,IAAI,QAAQ,MAAM,SAAS,OAAO,IAAI,KAAK,IAAO;QAE3D,OAAO,cAAc,CAAE,IAAI,IAAI,OAAO,EAAG;IAE1C;IAEA,OAAO,MAAM;AAEd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/MeshBVHNode.js"], "sourcesContent": ["export class MeshBVHNode {\n\n\tconstructor() {\n\n\t\t// internal nodes have boundingData, left, right, and splitAxis\n\t\t// leaf nodes have offset and count (referring to primitives in the mesh geometry)\n\n\t\tthis.boundingData = new Float32Array( 6 );\n\n\t}\n\n}\n"], "names": [], "mappings": ";;;AAAO,MAAM;IAEZ,aAAc;QAEb,+DAA+D;QAC/D,kFAAkF;QAElF,IAAI,CAAC,YAAY,GAAG,IAAI,aAAc;IAEvC;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 589, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/build/sortUtils.generated.js"], "sourcesContent": ["/********************************************************/\n/* This file is generated from \"sortUtils.template.js\". */\n/********************************************************/\n// reorders `tris` such that for `count` elements after `offset`, elements on the left side of the split\n// will be on the left and elements on the right side of the split will be on the right. returns the index\n// of the first element on the right side, or offset + count if there are no elements on the right side.\nfunction partition( indirectBuffer, index, triangleBounds, offset, count, split ) {\n\n\tlet left = offset;\n\tlet right = offset + count - 1;\n\tconst pos = split.pos;\n\tconst axisOffset = split.axis * 2;\n\n\t// hoare partitioning, see e.g. https://en.wikipedia.org/wiki/Quicksort#Hoare_partition_scheme\n\twhile ( true ) {\n\n\t\twhile ( left <= right && triangleBounds[ left * 6 + axisOffset ] < pos ) {\n\n\t\t\tleft ++;\n\n\t\t}\n\n\t\t// if a triangle center lies on the partition plane it is considered to be on the right side\n\t\twhile ( left <= right && triangleBounds[ right * 6 + axisOffset ] >= pos ) {\n\n\t\t\tright --;\n\n\t\t}\n\n\t\tif ( left < right ) {\n\n\t\t\t// we need to swap all of the information associated with the triangles at index\n\t\t\t// left and right; that's the verts in the geometry index, the bounds,\n\t\t\t// and perhaps the SAH planes\n\n\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\tlet t0 = index[ left * 3 + i ];\n\t\t\t\tindex[ left * 3 + i ] = index[ right * 3 + i ];\n\t\t\t\tindex[ right * 3 + i ] = t0;\n\n\t\t\t}\n\n\n\t\t\t// swap bounds\n\t\t\tfor ( let i = 0; i < 6; i ++ ) {\n\n\t\t\t\tlet tb = triangleBounds[ left * 6 + i ];\n\t\t\t\ttriangleBounds[ left * 6 + i ] = triangleBounds[ right * 6 + i ];\n\t\t\t\ttriangleBounds[ right * 6 + i ] = tb;\n\n\t\t\t}\n\n\t\t\tleft ++;\n\t\t\tright --;\n\n\t\t} else {\n\n\t\t\treturn left;\n\n\t\t}\n\n\t}\n\n}\n\nexport { partition };\n"], "names": [], "mappings": "AAAA,wDAAwD,GACxD,wDAAwD,GACxD,wDAAwD,GACxD,wGAAwG;AACxG,0GAA0G;AAC1G,wGAAwG;;;;AACxG,SAAS,UAAW,cAAc,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK;IAE9E,IAAI,OAAO;IACX,IAAI,QAAQ,SAAS,QAAQ;IAC7B,MAAM,MAAM,MAAM,GAAG;IACrB,MAAM,aAAa,MAAM,IAAI,GAAG;IAEhC,8FAA8F;IAC9F,MAAQ,KAAO;QAEd,MAAQ,QAAQ,SAAS,cAAc,CAAE,OAAO,IAAI,WAAY,GAAG,IAAM;YAExE;QAED;QAEA,4FAA4F;QAC5F,MAAQ,QAAQ,SAAS,cAAc,CAAE,QAAQ,IAAI,WAAY,IAAI,IAAM;YAE1E;QAED;QAEA,IAAK,OAAO,OAAQ;YAEnB,gFAAgF;YAChF,sEAAsE;YACtE,6BAA6B;YAE7B,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;gBAE9B,IAAI,KAAK,KAAK,CAAE,OAAO,IAAI,EAAG;gBAC9B,KAAK,CAAE,OAAO,IAAI,EAAG,GAAG,KAAK,CAAE,QAAQ,IAAI,EAAG;gBAC9C,KAAK,CAAE,QAAQ,IAAI,EAAG,GAAG;YAE1B;YAGA,cAAc;YACd,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;gBAE9B,IAAI,KAAK,cAAc,CAAE,OAAO,IAAI,EAAG;gBACvC,cAAc,CAAE,OAAO,IAAI,EAAG,GAAG,cAAc,CAAE,QAAQ,IAAI,EAAG;gBAChE,cAAc,CAAE,QAAQ,IAAI,EAAG,GAAG;YAEnC;YAEA;YACA;QAED,OAAO;YAEN,OAAO;QAER;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/build/sortUtils_indirect.generated.js"], "sourcesContent": ["/********************************************************/\n/* This file is generated from \"sortUtils.template.js\". */\n/********************************************************/\n// reorders `tris` such that for `count` elements after `offset`, elements on the left side of the split\n// will be on the left and elements on the right side of the split will be on the right. returns the index\n// of the first element on the right side, or offset + count if there are no elements on the right side.\nfunction partition_indirect( indirectBuffer, index, triangleBounds, offset, count, split ) {\n\n\tlet left = offset;\n\tlet right = offset + count - 1;\n\tconst pos = split.pos;\n\tconst axisOffset = split.axis * 2;\n\n\t// hoare partitioning, see e.g. https://en.wikipedia.org/wiki/Quicksort#Hoare_partition_scheme\n\twhile ( true ) {\n\n\t\twhile ( left <= right && triangleBounds[ left * 6 + axisOffset ] < pos ) {\n\n\t\t\tleft ++;\n\n\t\t}\n\n\t\t// if a triangle center lies on the partition plane it is considered to be on the right side\n\t\twhile ( left <= right && triangleBounds[ right * 6 + axisOffset ] >= pos ) {\n\n\t\t\tright --;\n\n\t\t}\n\n\t\tif ( left < right ) {\n\n\t\t\t// we need to swap all of the information associated with the triangles at index\n\t\t\t// left and right; that's the verts in the geometry index, the bounds,\n\t\t\t// and perhaps the SAH planes\n\t\t\tlet t = indirectBuffer[ left ];\n\t\t\tindirectBuffer[ left ] = indirectBuffer[ right ];\n\t\t\tindirectBuffer[ right ] = t;\n\n\n\t\t\t// swap bounds\n\t\t\tfor ( let i = 0; i < 6; i ++ ) {\n\n\t\t\t\tlet tb = triangleBounds[ left * 6 + i ];\n\t\t\t\ttriangleBounds[ left * 6 + i ] = triangleBounds[ right * 6 + i ];\n\t\t\t\ttriangleBounds[ right * 6 + i ] = tb;\n\n\t\t\t}\n\n\t\t\tleft ++;\n\t\t\tright --;\n\n\t\t} else {\n\n\t\t\treturn left;\n\n\t\t}\n\n\t}\n\n}\n\nexport { partition_indirect };\n"], "names": [], "mappings": "AAAA,wDAAwD,GACxD,wDAAwD,GACxD,wDAAwD,GACxD,wGAAwG;AACxG,0GAA0G;AAC1G,wGAAwG;;;;AACxG,SAAS,mBAAoB,cAAc,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK;IAEvF,IAAI,OAAO;IACX,IAAI,QAAQ,SAAS,QAAQ;IAC7B,MAAM,MAAM,MAAM,GAAG;IACrB,MAAM,aAAa,MAAM,IAAI,GAAG;IAEhC,8FAA8F;IAC9F,MAAQ,KAAO;QAEd,MAAQ,QAAQ,SAAS,cAAc,CAAE,OAAO,IAAI,WAAY,GAAG,IAAM;YAExE;QAED;QAEA,4FAA4F;QAC5F,MAAQ,QAAQ,SAAS,cAAc,CAAE,QAAQ,IAAI,WAAY,IAAI,IAAM;YAE1E;QAED;QAEA,IAAK,OAAO,OAAQ;YAEnB,gFAAgF;YAChF,sEAAsE;YACtE,6BAA6B;YAC7B,IAAI,IAAI,cAAc,CAAE,KAAM;YAC9B,cAAc,CAAE,KAAM,GAAG,cAAc,CAAE,MAAO;YAChD,cAAc,CAAE,MAAO,GAAG;YAG1B,cAAc;YACd,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;gBAE9B,IAAI,KAAK,cAAc,CAAE,OAAO,IAAI,EAAG;gBACvC,cAAc,CAAE,OAAO,IAAI,EAAG,GAAG,cAAc,CAAE,QAAQ,IAAI,EAAG;gBAChE,cAAc,CAAE,QAAQ,IAAI,EAAG,GAAG;YAEnC;YAEA;YACA;QAED,OAAO;YAEN,OAAO;QAER;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/utils/nodeBufferUtils.js"], "sourcesContent": ["export function IS_LEAF( n16, uint16Array ) {\n\n\treturn uint16Array[ n16 + 15 ] === 0xFFFF;\n\n}\n\nexport function OFFSET( n32, uint32Array ) {\n\n\treturn uint32Array[ n32 + 6 ];\n\n}\n\nexport function COUNT( n16, uint16Array ) {\n\n\treturn uint16Array[ n16 + 14 ];\n\n}\n\nexport function LEFT_NODE( n32 ) {\n\n\treturn n32 + 8;\n\n}\n\nexport function RIGHT_NODE( n32, uint32Array ) {\n\n\treturn uint32Array[ n32 + 6 ];\n\n}\n\nexport function SPLIT_AXIS( n32, uint32Array ) {\n\n\treturn uint32Array[ n32 + 7 ];\n\n}\n\nexport function BOUNDING_DATA_INDEX( n32 ) {\n\n\treturn n32;\n\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAO,SAAS,QAAS,GAAG,EAAE,WAAW;IAExC,OAAO,WAAW,CAAE,MAAM,GAAI,KAAK;AAEpC;AAEO,SAAS,OAAQ,GAAG,EAAE,WAAW;IAEvC,OAAO,WAAW,CAAE,MAAM,EAAG;AAE9B;AAEO,SAAS,MAAO,GAAG,EAAE,WAAW;IAEtC,OAAO,WAAW,CAAE,MAAM,GAAI;AAE/B;AAEO,SAAS,UAAW,GAAG;IAE7B,OAAO,MAAM;AAEd;AAEO,SAAS,WAAY,GAAG,EAAE,WAAW;IAE3C,OAAO,WAAW,CAAE,MAAM,EAAG;AAE9B;AAEO,SAAS,WAAY,GAAG,EAAE,WAAW;IAE3C,OAAO,WAAW,CAAE,MAAM,EAAG;AAE9B;AAEO,SAAS,oBAAqB,GAAG;IAEvC,OAAO;AAER", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/build/buildUtils.js"], "sourcesContent": ["import { BYTES_PER_NODE, IS_LEAFNODE_FLAG } from '../Constants.js';\nimport { IS_LEAF } from '../utils/nodeBufferUtils.js';\n\nlet float32Array, uint32Array, uint16Array, uint8Array;\nconst MAX_POINTER = Math.pow( 2, 32 );\n\nexport function countNodes( node ) {\n\n\tif ( 'count' in node ) {\n\n\t\treturn 1;\n\n\t} else {\n\n\t\treturn 1 + countNodes( node.left ) + countNodes( node.right );\n\n\t}\n\n}\n\nexport function populateBuffer( byteOffset, node, buffer ) {\n\n\tfloat32Array = new Float32Array( buffer );\n\tuint32Array = new Uint32Array( buffer );\n\tuint16Array = new Uint16Array( buffer );\n\tuint8Array = new Uint8Array( buffer );\n\n\treturn _populateBuffer( byteOffset, node );\n\n}\n\n// pack structure\n// boundingData  \t\t\t\t: 6 float32\n// right / offset \t\t\t\t: 1 uint32\n// splitAxis / isLeaf + count \t: 1 uint32 / 2 uint16\nfunction _populateBuffer( byteOffset, node ) {\n\n\tconst stride4Offset = byteOffset / 4;\n\tconst stride2Offset = byteOffset / 2;\n\tconst isLeaf = 'count' in node;\n\tconst boundingData = node.boundingData;\n\tfor ( let i = 0; i < 6; i ++ ) {\n\n\t\tfloat32Array[ stride4Offset + i ] = boundingData[ i ];\n\n\t}\n\n\tif ( isLeaf ) {\n\n\t\tif ( node.buffer ) {\n\n\t\t\tconst buffer = node.buffer;\n\t\t\tuint8Array.set( new Uint8Array( buffer ), byteOffset );\n\n\t\t\tfor ( let offset = byteOffset, l = byteOffset + buffer.byteLength; offset < l; offset += BYTES_PER_NODE ) {\n\n\t\t\t\tconst offset2 = offset / 2;\n\t\t\t\tif ( ! IS_LEAF( offset2, uint16Array ) ) {\n\n\t\t\t\t\tuint32Array[ ( offset / 4 ) + 6 ] += stride4Offset;\n\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn byteOffset + buffer.byteLength;\n\n\t\t} else {\n\n\t\t\tconst offset = node.offset;\n\t\t\tconst count = node.count;\n\t\t\tuint32Array[ stride4Offset + 6 ] = offset;\n\t\t\tuint16Array[ stride2Offset + 14 ] = count;\n\t\t\tuint16Array[ stride2Offset + 15 ] = IS_LEAFNODE_FLAG;\n\t\t\treturn byteOffset + BYTES_PER_NODE;\n\n\t\t}\n\n\t} else {\n\n\t\tconst left = node.left;\n\t\tconst right = node.right;\n\t\tconst splitAxis = node.splitAxis;\n\n\t\tlet nextUnusedPointer;\n\t\tnextUnusedPointer = _populateBuffer( byteOffset + BYTES_PER_NODE, left );\n\n\t\tif ( ( nextUnusedPointer / 4 ) > MAX_POINTER ) {\n\n\t\t\tthrow new Error( 'MeshBVH: Cannot store child pointer greater than 32 bits.' );\n\n\t\t}\n\n\t\tuint32Array[ stride4Offset + 6 ] = nextUnusedPointer / 4;\n\t\tnextUnusedPointer = _populateBuffer( nextUnusedPointer, right );\n\n\t\tuint32Array[ stride4Offset + 7 ] = splitAxis;\n\t\treturn nextUnusedPointer;\n\n\t}\n\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,IAAI,cAAc,aAAa,aAAa;AAC5C,MAAM,cAAc,KAAK,GAAG,CAAE,GAAG;AAE1B,SAAS,WAAY,IAAI;IAE/B,IAAK,WAAW,MAAO;QAEtB,OAAO;IAER,OAAO;QAEN,OAAO,IAAI,WAAY,KAAK,IAAI,IAAK,WAAY,KAAK,KAAK;IAE5D;AAED;AAEO,SAAS,eAAgB,UAAU,EAAE,IAAI,EAAE,MAAM;IAEvD,eAAe,IAAI,aAAc;IACjC,cAAc,IAAI,YAAa;IAC/B,cAAc,IAAI,YAAa;IAC/B,aAAa,IAAI,WAAY;IAE7B,OAAO,gBAAiB,YAAY;AAErC;AAEA,iBAAiB;AACjB,gCAAgC;AAChC,gCAAgC;AAChC,oDAAoD;AACpD,SAAS,gBAAiB,UAAU,EAAE,IAAI;IAEzC,MAAM,gBAAgB,aAAa;IACnC,MAAM,gBAAgB,aAAa;IACnC,MAAM,SAAS,WAAW;IAC1B,MAAM,eAAe,KAAK,YAAY;IACtC,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;QAE9B,YAAY,CAAE,gBAAgB,EAAG,GAAG,YAAY,CAAE,EAAG;IAEtD;IAEA,IAAK,QAAS;QAEb,IAAK,KAAK,MAAM,EAAG;YAElB,MAAM,SAAS,KAAK,MAAM;YAC1B,WAAW,GAAG,CAAE,IAAI,WAAY,SAAU;YAE1C,IAAM,IAAI,SAAS,YAAY,IAAI,aAAa,OAAO,UAAU,EAAE,SAAS,GAAG,UAAU,mKAAA,CAAA,iBAAc,CAAG;gBAEzG,MAAM,UAAU,SAAS;gBACzB,IAAK,CAAE,CAAA,GAAA,kLAAA,CAAA,UAAO,AAAD,EAAG,SAAS,cAAgB;oBAExC,WAAW,CAAE,AAAE,SAAS,IAAM,EAAG,IAAI;gBAGtC;YAED;YAEA,OAAO,aAAa,OAAO,UAAU;QAEtC,OAAO;YAEN,MAAM,SAAS,KAAK,MAAM;YAC1B,MAAM,QAAQ,KAAK,KAAK;YACxB,WAAW,CAAE,gBAAgB,EAAG,GAAG;YACnC,WAAW,CAAE,gBAAgB,GAAI,GAAG;YACpC,WAAW,CAAE,gBAAgB,GAAI,GAAG,mKAAA,CAAA,mBAAgB;YACpD,OAAO,aAAa,mKAAA,CAAA,iBAAc;QAEnC;IAED,OAAO;QAEN,MAAM,OAAO,KAAK,IAAI;QACtB,MAAM,QAAQ,KAAK,KAAK;QACxB,MAAM,YAAY,KAAK,SAAS;QAEhC,IAAI;QACJ,oBAAoB,gBAAiB,aAAa,mKAAA,CAAA,iBAAc,EAAE;QAElE,IAAK,AAAE,oBAAoB,IAAM,aAAc;YAE9C,MAAM,IAAI,MAAO;QAElB;QAEA,WAAW,CAAE,gBAAgB,EAAG,GAAG,oBAAoB;QACvD,oBAAoB,gBAAiB,mBAAmB;QAExD,WAAW,CAAE,gBAAgB,EAAG,GAAG;QACnC,OAAO;IAER;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/build/buildTree.js"], "sourcesContent": ["import { ensureIndex, getFullGeometryRange, getRootIndexRanges, getTriCount, hasGroupGaps, } from './geometryUtils.js';\nimport { getBounds, computeTriangleBounds } from './computeBoundsUtils.js';\nimport { getOptimalSplit } from './splitUtils.js';\nimport { MeshBVHNode } from '../MeshBVHNode.js';\nimport { BYTES_PER_NODE } from '../Constants.js';\n\nimport { partition } from './sortUtils.generated.js';\nimport { partition_indirect } from './sortUtils_indirect.generated.js';\nimport { countNodes, populateBuffer } from './buildUtils.js';\n\nexport function generateIndirectBuffer( geometry, useSharedArrayBuffer ) {\n\n\tconst triCount = ( geometry.index ? geometry.index.count : geometry.attributes.position.count ) / 3;\n\tconst useUint32 = triCount > 2 ** 16;\n\tconst byteCount = useUint32 ? 4 : 2;\n\n\tconst buffer = useSharedArrayBuffer ? new SharedArrayBuffer( triCount * byteCount ) : new ArrayBuffer( triCount * byteCount );\n\tconst indirectBuffer = useUint32 ? new Uint32Array( buffer ) : new Uint16Array( buffer );\n\tfor ( let i = 0, l = indirectBuffer.length; i < l; i ++ ) {\n\n\t\tindirectBuffer[ i ] = i;\n\n\t}\n\n\treturn indirectBuffer;\n\n}\n\nexport function buildTree( bvh, triangleBounds, offset, count, options ) {\n\n\t// epxand variables\n\tconst {\n\t\tmaxDepth,\n\t\tverbose,\n\t\tmaxLeafTris,\n\t\tstrategy,\n\t\tonProgress,\n\t\tindirect,\n\t} = options;\n\tconst indirectBuffer = bvh._indirectBuffer;\n\tconst geometry = bvh.geometry;\n\tconst indexArray = geometry.index ? geometry.index.array : null;\n\tconst partionFunc = indirect ? partition_indirect : partition;\n\n\t// generate intermediate variables\n\tconst totalTriangles = getTriCount( geometry );\n\tconst cacheCentroidBoundingData = new Float32Array( 6 );\n\tlet reachedMaxDepth = false;\n\n\tconst root = new MeshBVHNode();\n\tgetBounds( triangleBounds, offset, count, root.boundingData, cacheCentroidBoundingData );\n\tsplitNode( root, offset, count, cacheCentroidBoundingData );\n\treturn root;\n\n\tfunction triggerProgress( trianglesProcessed ) {\n\n\t\tif ( onProgress ) {\n\n\t\t\tonProgress( trianglesProcessed / totalTriangles );\n\n\t\t}\n\n\t}\n\n\t// either recursively splits the given node, creating left and right subtrees for it, or makes it a leaf node,\n\t// recording the offset and count of its triangles and writing them into the reordered geometry index.\n\tfunction splitNode( node, offset, count, centroidBoundingData = null, depth = 0 ) {\n\n\t\tif ( ! reachedMaxDepth && depth >= maxDepth ) {\n\n\t\t\treachedMaxDepth = true;\n\t\t\tif ( verbose ) {\n\n\t\t\t\tconsole.warn( `MeshBVH: Max depth of ${ maxDepth } reached when generating BVH. Consider increasing maxDepth.` );\n\t\t\t\tconsole.warn( geometry );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// early out if we've met our capacity\n\t\tif ( count <= maxLeafTris || depth >= maxDepth ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\t\t\treturn node;\n\n\t\t}\n\n\t\t// Find where to split the volume\n\t\tconst split = getOptimalSplit( node.boundingData, centroidBoundingData, triangleBounds, offset, count, strategy );\n\t\tif ( split.axis === - 1 ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\t\t\treturn node;\n\n\t\t}\n\n\t\tconst splitOffset = partionFunc( indirectBuffer, indexArray, triangleBounds, offset, count, split );\n\n\t\t// create the two new child nodes\n\t\tif ( splitOffset === offset || splitOffset === offset + count ) {\n\n\t\t\ttriggerProgress( offset + count );\n\t\t\tnode.offset = offset;\n\t\t\tnode.count = count;\n\n\t\t} else {\n\n\t\t\tnode.splitAxis = split.axis;\n\n\t\t\t// create the left child and compute its bounding box\n\t\t\tconst left = new MeshBVHNode();\n\t\t\tconst lstart = offset;\n\t\t\tconst lcount = splitOffset - offset;\n\t\t\tnode.left = left;\n\n\t\t\tgetBounds( triangleBounds, lstart, lcount, left.boundingData, cacheCentroidBoundingData );\n\t\t\tsplitNode( left, lstart, lcount, cacheCentroidBoundingData, depth + 1 );\n\n\t\t\t// repeat for right\n\t\t\tconst right = new MeshBVHNode();\n\t\t\tconst rstart = splitOffset;\n\t\t\tconst rcount = count - lcount;\n\t\t\tnode.right = right;\n\n\t\t\tgetBounds( triangleBounds, rstart, rcount, right.boundingData, cacheCentroidBoundingData );\n\t\t\tsplitNode( right, rstart, rcount, cacheCentroidBoundingData, depth + 1 );\n\n\t\t}\n\n\t\treturn node;\n\n\t}\n\n}\n\nexport function buildPackedTree( bvh, options ) {\n\n\tconst geometry = bvh.geometry;\n\tif ( options.indirect ) {\n\n\t\tbvh._indirectBuffer = generateIndirectBuffer( geometry, options.useSharedArrayBuffer );\n\n\t\tif ( hasGroupGaps( geometry, options.range ) && ! options.verbose ) {\n\n\t\t\tconsole.warn(\n\t\t\t\t'MeshBVH: Provided geometry contains groups or a range that do not fully span the vertex contents while using the \"indirect\" option. ' +\n\t\t\t\t'BVH may incorrectly report intersections on unrendered portions of the geometry.'\n\t\t\t);\n\n\t\t}\n\n\t}\n\n\tif ( ! bvh._indirectBuffer ) {\n\n\t\tensureIndex( geometry, options );\n\n\t}\n\n\tconst BufferConstructor = options.useSharedArrayBuffer ? SharedArrayBuffer : ArrayBuffer;\n\n\tconst triangleBounds = computeTriangleBounds( geometry );\n\tconst geometryRanges = options.indirect ? getFullGeometryRange( geometry, options.range ) : getRootIndexRanges( geometry, options.range );\n\tbvh._roots = geometryRanges.map( range => {\n\n\t\tconst root = buildTree( bvh, triangleBounds, range.offset, range.count, options );\n\t\tconst nodeCount = countNodes( root );\n\t\tconst buffer = new BufferConstructor( BYTES_PER_NODE * nodeCount );\n\t\tpopulateBuffer( 0, root, buffer );\n\t\treturn buffer;\n\n\t} );\n\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;AAEO,SAAS,uBAAwB,QAAQ,EAAE,oBAAoB;IAErE,MAAM,WAAW,CAAE,SAAS,KAAK,GAAG,SAAS,KAAK,CAAC,KAAK,GAAG,SAAS,UAAU,CAAC,QAAQ,CAAC,KAAK,AAAC,IAAI;IAClG,MAAM,YAAY,WAAW,KAAK;IAClC,MAAM,YAAY,YAAY,IAAI;IAElC,MAAM,SAAS,uBAAuB,IAAI,kBAAmB,WAAW,aAAc,IAAI,YAAa,WAAW;IAClH,MAAM,iBAAiB,YAAY,IAAI,YAAa,UAAW,IAAI,YAAa;IAChF,IAAM,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAI,GAAG,IAAO;QAEzD,cAAc,CAAE,EAAG,GAAG;IAEvB;IAEA,OAAO;AAER;AAEO,SAAS,UAAW,GAAG,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO;IAErE,mBAAmB;IACnB,MAAM,EACL,QAAQ,EACR,OAAO,EACP,WAAW,EACX,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,GAAG;IACJ,MAAM,iBAAiB,IAAI,eAAe;IAC1C,MAAM,WAAW,IAAI,QAAQ;IAC7B,MAAM,aAAa,SAAS,KAAK,GAAG,SAAS,KAAK,CAAC,KAAK,GAAG;IAC3D,MAAM,cAAc,WAAW,kMAAA,CAAA,qBAAkB,GAAG,yLAAA,CAAA,YAAS;IAE7D,kCAAkC;IAClC,MAAM,iBAAiB,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAG;IACpC,MAAM,4BAA4B,IAAI,aAAc;IACpD,IAAI,kBAAkB;IAEtB,MAAM,OAAO,IAAI,qKAAA,CAAA,cAAW;IAC5B,CAAA,GAAA,qLAAA,CAAA,YAAS,AAAD,EAAG,gBAAgB,QAAQ,OAAO,KAAK,YAAY,EAAE;IAC7D,UAAW,MAAM,QAAQ,OAAO;IAChC,OAAO;;IAEP,SAAS,gBAAiB,kBAAkB;QAE3C,IAAK,YAAa;YAEjB,WAAY,qBAAqB;QAElC;IAED;IAEA,8GAA8G;IAC9G,sGAAsG;IACtG,SAAS,UAAW,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,uBAAuB,IAAI,EAAE,QAAQ,CAAC;QAE9E,IAAK,CAAE,mBAAmB,SAAS,UAAW;YAE7C,kBAAkB;YAClB,IAAK,SAAU;gBAEd,QAAQ,IAAI,CAAE,CAAC,sBAAsB,EAAG,SAAU,2DAA2D,CAAC;gBAC9G,QAAQ,IAAI,CAAE;YAEf;QAED;QAEA,sCAAsC;QACtC,IAAK,SAAS,eAAe,SAAS,UAAW;YAEhD,gBAAiB,SAAS;YAC1B,KAAK,MAAM,GAAG;YACd,KAAK,KAAK,GAAG;YACb,OAAO;QAER;QAEA,iCAAiC;QACjC,MAAM,QAAQ,CAAA,GAAA,6KAAA,CAAA,kBAAe,AAAD,EAAG,KAAK,YAAY,EAAE,sBAAsB,gBAAgB,QAAQ,OAAO;QACvG,IAAK,MAAM,IAAI,KAAK,CAAE,GAAI;YAEzB,gBAAiB,SAAS;YAC1B,KAAK,MAAM,GAAG;YACd,KAAK,KAAK,GAAG;YACb,OAAO;QAER;QAEA,MAAM,cAAc,YAAa,gBAAgB,YAAY,gBAAgB,QAAQ,OAAO;QAE5F,iCAAiC;QACjC,IAAK,gBAAgB,UAAU,gBAAgB,SAAS,OAAQ;YAE/D,gBAAiB,SAAS;YAC1B,KAAK,MAAM,GAAG;YACd,KAAK,KAAK,GAAG;QAEd,OAAO;YAEN,KAAK,SAAS,GAAG,MAAM,IAAI;YAE3B,qDAAqD;YACrD,MAAM,OAAO,IAAI,qKAAA,CAAA,cAAW;YAC5B,MAAM,SAAS;YACf,MAAM,SAAS,cAAc;YAC7B,KAAK,IAAI,GAAG;YAEZ,CAAA,GAAA,qLAAA,CAAA,YAAS,AAAD,EAAG,gBAAgB,QAAQ,QAAQ,KAAK,YAAY,EAAE;YAC9D,UAAW,MAAM,QAAQ,QAAQ,2BAA2B,QAAQ;YAEpE,mBAAmB;YACnB,MAAM,QAAQ,IAAI,qKAAA,CAAA,cAAW;YAC7B,MAAM,SAAS;YACf,MAAM,SAAS,QAAQ;YACvB,KAAK,KAAK,GAAG;YAEb,CAAA,GAAA,qLAAA,CAAA,YAAS,AAAD,EAAG,gBAAgB,QAAQ,QAAQ,MAAM,YAAY,EAAE;YAC/D,UAAW,OAAO,QAAQ,QAAQ,2BAA2B,QAAQ;QAEtE;QAEA,OAAO;IAER;AAED;AAEO,SAAS,gBAAiB,GAAG,EAAE,OAAO;IAE5C,MAAM,WAAW,IAAI,QAAQ;IAC7B,IAAK,QAAQ,QAAQ,EAAG;QAEvB,IAAI,eAAe,GAAG,uBAAwB,UAAU,QAAQ,oBAAoB;QAEpF,IAAK,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAG,UAAU,QAAQ,KAAK,KAAM,CAAE,QAAQ,OAAO,EAAG;YAEnE,QAAQ,IAAI,CACX,yIACA;QAGF;IAED;IAEA,IAAK,CAAE,IAAI,eAAe,EAAG;QAE5B,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAG,UAAU;IAExB;IAEA,MAAM,oBAAoB,QAAQ,oBAAoB,GAAG,oBAAoB;IAE7E,MAAM,iBAAiB,CAAA,GAAA,qLAAA,CAAA,wBAAqB,AAAD,EAAG;IAC9C,MAAM,iBAAiB,QAAQ,QAAQ,GAAG,CAAA,GAAA,gLAAA,CAAA,uBAAoB,AAAD,EAAG,UAAU,QAAQ,KAAK,IAAK,CAAA,GAAA,gLAAA,CAAA,qBAAkB,AAAD,EAAG,UAAU,QAAQ,KAAK;IACvI,IAAI,MAAM,GAAG,eAAe,GAAG,CAAE,CAAA;QAEhC,MAAM,OAAO,UAAW,KAAK,gBAAgB,MAAM,MAAM,EAAE,MAAM,KAAK,EAAE;QACxE,MAAM,YAAY,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAG;QAC9B,MAAM,SAAS,IAAI,kBAAmB,mKAAA,CAAA,iBAAc,GAAG;QACvD,CAAA,GAAA,6KAAA,CAAA,iBAAc,AAAD,EAAG,GAAG,MAAM;QACzB,OAAO;IAER;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/math/SeparatingAxisBounds.js"], "sourcesContent": ["import { Vector3 } from 'three';\n\nexport class SeparatingAxisBounds {\n\n\tconstructor() {\n\n\t\tthis.min = Infinity;\n\t\tthis.max = - Infinity;\n\n\t}\n\n\tsetFromPointsField( points, field ) {\n\n\t\tlet min = Infinity;\n\t\tlet max = - Infinity;\n\t\tfor ( let i = 0, l = points.length; i < l; i ++ ) {\n\n\t\t\tconst p = points[ i ];\n\t\t\tconst val = p[ field ];\n\t\t\tmin = val < min ? val : min;\n\t\t\tmax = val > max ? val : max;\n\n\t\t}\n\n\t\tthis.min = min;\n\t\tthis.max = max;\n\n\t}\n\n\tsetFromPoints( axis, points ) {\n\n\t\tlet min = Infinity;\n\t\tlet max = - Infinity;\n\t\tfor ( let i = 0, l = points.length; i < l; i ++ ) {\n\n\t\t\tconst p = points[ i ];\n\t\t\tconst val = axis.dot( p );\n\t\t\tmin = val < min ? val : min;\n\t\t\tmax = val > max ? val : max;\n\n\t\t}\n\n\t\tthis.min = min;\n\t\tthis.max = max;\n\n\t}\n\n\tisSeparated( other ) {\n\n\t\treturn this.min > other.max || other.min > this.max;\n\n\t}\n\n}\n\nSeparatingAxisBounds.prototype.setFromBox = ( function () {\n\n\tconst p = new Vector3();\n\treturn function setFromBox( axis, box ) {\n\n\t\tconst boxMin = box.min;\n\t\tconst boxMax = box.max;\n\t\tlet min = Infinity;\n\t\tlet max = - Infinity;\n\t\tfor ( let x = 0; x <= 1; x ++ ) {\n\n\t\t\tfor ( let y = 0; y <= 1; y ++ ) {\n\n\t\t\t\tfor ( let z = 0; z <= 1; z ++ ) {\n\n\t\t\t\t\tp.x = boxMin.x * x + boxMax.x * ( 1 - x );\n\t\t\t\t\tp.y = boxMin.y * y + boxMax.y * ( 1 - y );\n\t\t\t\t\tp.z = boxMin.z * z + boxMax.z * ( 1 - z );\n\n\t\t\t\t\tconst val = axis.dot( p );\n\t\t\t\t\tmin = Math.min( val, min );\n\t\t\t\t\tmax = Math.max( val, max );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tthis.min = min;\n\t\tthis.max = max;\n\n\t};\n\n} )();\n\nexport const areIntersecting = ( function () {\n\n\tconst cacheSatBounds = new SeparatingAxisBounds();\n\treturn function areIntersecting( shape1, shape2 ) {\n\n\t\tconst points1 = shape1.points;\n\t\tconst satAxes1 = shape1.satAxes;\n\t\tconst satBounds1 = shape1.satBounds;\n\n\t\tconst points2 = shape2.points;\n\t\tconst satAxes2 = shape2.satAxes;\n\t\tconst satBounds2 = shape2.satBounds;\n\n\t\t// check axes of the first shape\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = satBounds1[ i ];\n\t\t\tconst sa = satAxes1[ i ];\n\t\t\tcacheSatBounds.setFromPoints( sa, points2 );\n\t\t\tif ( sb.isSeparated( cacheSatBounds ) ) return false;\n\n\t\t}\n\n\t\t// check axes of the second shape\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = satBounds2[ i ];\n\t\t\tconst sa = satAxes2[ i ];\n\t\t\tcacheSatBounds.setFromPoints( sa, points1 );\n\t\t\tif ( sb.isSeparated( cacheSatBounds ) ) return false;\n\n\t\t}\n\n\t};\n\n} )();\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM;IAEZ,aAAc;QAEb,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG,CAAE;IAEd;IAEA,mBAAoB,MAAM,EAAE,KAAK,EAAG;QAEnC,IAAI,MAAM;QACV,IAAI,MAAM,CAAE;QACZ,IAAM,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAI,GAAG,IAAO;YAEjD,MAAM,IAAI,MAAM,CAAE,EAAG;YACrB,MAAM,MAAM,CAAC,CAAE,MAAO;YACtB,MAAM,MAAM,MAAM,MAAM;YACxB,MAAM,MAAM,MAAM,MAAM;QAEzB;QAEA,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG;IAEZ;IAEA,cAAe,IAAI,EAAE,MAAM,EAAG;QAE7B,IAAI,MAAM;QACV,IAAI,MAAM,CAAE;QACZ,IAAM,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAI,GAAG,IAAO;YAEjD,MAAM,IAAI,MAAM,CAAE,EAAG;YACrB,MAAM,MAAM,KAAK,GAAG,CAAE;YACtB,MAAM,MAAM,MAAM,MAAM;YACxB,MAAM,MAAM,MAAM,MAAM;QAEzB;QAEA,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG;IAEZ;IAEA,YAAa,KAAK,EAAG;QAEpB,OAAO,IAAI,CAAC,GAAG,GAAG,MAAM,GAAG,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG;IAEpD;AAED;AAEA,qBAAqB,SAAS,CAAC,UAAU,GAAG,AAAE;IAE7C,MAAM,IAAI,IAAI,kJAAA,CAAA,UAAO;IACrB,OAAO,SAAS,WAAY,IAAI,EAAE,GAAG;QAEpC,MAAM,SAAS,IAAI,GAAG;QACtB,MAAM,SAAS,IAAI,GAAG;QACtB,IAAI,MAAM;QACV,IAAI,MAAM,CAAE;QACZ,IAAM,IAAI,IAAI,GAAG,KAAK,GAAG,IAAO;YAE/B,IAAM,IAAI,IAAI,GAAG,KAAK,GAAG,IAAO;gBAE/B,IAAM,IAAI,IAAI,GAAG,KAAK,GAAG,IAAO;oBAE/B,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAE,IAAI,CAAE;oBACxC,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAE,IAAI,CAAE;oBACxC,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAE,IAAI,CAAE;oBAExC,MAAM,MAAM,KAAK,GAAG,CAAE;oBACtB,MAAM,KAAK,GAAG,CAAE,KAAK;oBACrB,MAAM,KAAK,GAAG,CAAE,KAAK;gBAEtB;YAED;QAED;QAEA,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,GAAG,GAAG;IAEZ;AAED;AAEO,MAAM,kBAAkB,AAAE;IAEhC,MAAM,iBAAiB,IAAI;IAC3B,OAAO,SAAS,gBAAiB,MAAM,EAAE,MAAM;QAE9C,MAAM,UAAU,OAAO,MAAM;QAC7B,MAAM,WAAW,OAAO,OAAO;QAC/B,MAAM,aAAa,OAAO,SAAS;QAEnC,MAAM,UAAU,OAAO,MAAM;QAC7B,MAAM,WAAW,OAAO,OAAO;QAC/B,MAAM,aAAa,OAAO,SAAS;QAEnC,gCAAgC;QAChC,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;YAE9B,MAAM,KAAK,UAAU,CAAE,EAAG;YAC1B,MAAM,KAAK,QAAQ,CAAE,EAAG;YACxB,eAAe,aAAa,CAAE,IAAI;YAClC,IAAK,GAAG,WAAW,CAAE,iBAAmB,OAAO;QAEhD;QAEA,iCAAiC;QACjC,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;YAE9B,MAAM,KAAK,UAAU,CAAE,EAAG;YAC1B,MAAM,KAAK,QAAQ,CAAE,EAAG;YACxB,eAAe,aAAa,CAAE,IAAI;YAClC,IAAK,GAAG,WAAW,CAAE,iBAAmB,OAAO;QAEhD;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/math/MathUtilities.js"], "sourcesContent": ["import { Vector3, Vector2, Plane, Line3 } from 'three';\n\nexport const closestPointLineToLine = ( function () {\n\n\t// https://github.com/juj/MathGeoLib/blob/master/src/Geometry/Line.cpp#L56\n\tconst dir1 = new Vector3();\n\tconst dir2 = new Vector3();\n\tconst v02 = new Vector3();\n\treturn function closestPointLineToLine( l1, l2, result ) {\n\n\t\tconst v0 = l1.start;\n\t\tconst v10 = dir1;\n\t\tconst v2 = l2.start;\n\t\tconst v32 = dir2;\n\n\t\tv02.subVectors( v0, v2 );\n\t\tdir1.subVectors( l1.end, l1.start );\n\t\tdir2.subVectors( l2.end, l2.start );\n\n\t\t// float d0232 = v02.Dot(v32);\n\t\tconst d0232 = v02.dot( v32 );\n\n\t\t// float d3210 = v32.Dot(v10);\n\t\tconst d3210 = v32.dot( v10 );\n\n\t\t// float d3232 = v32.Dot(v32);\n\t\tconst d3232 = v32.dot( v32 );\n\n\t\t// float d0210 = v02.Dot(v10);\n\t\tconst d0210 = v02.dot( v10 );\n\n\t\t// float d1010 = v10.Dot(v10);\n\t\tconst d1010 = v10.dot( v10 );\n\n\t\t// float denom = d1010*d3232 - d3210*d3210;\n\t\tconst denom = d1010 * d3232 - d3210 * d3210;\n\n\t\tlet d, d2;\n\t\tif ( denom !== 0 ) {\n\n\t\t\td = ( d0232 * d3210 - d0210 * d3232 ) / denom;\n\n\t\t} else {\n\n\t\t\td = 0;\n\n\t\t}\n\n\t\td2 = ( d0232 + d * d3210 ) / d3232;\n\n\t\tresult.x = d;\n\t\tresult.y = d2;\n\n\t};\n\n} )();\n\nexport const closestPointsSegmentToSegment = ( function () {\n\n\t// https://github.com/juj/MathGeoLib/blob/master/src/Geometry/LineSegment.cpp#L187\n\tconst paramResult = new Vector2();\n\tconst temp1 = new Vector3();\n\tconst temp2 = new Vector3();\n\treturn function closestPointsSegmentToSegment( l1, l2, target1, target2 ) {\n\n\t\tclosestPointLineToLine( l1, l2, paramResult );\n\n\t\tlet d = paramResult.x;\n\t\tlet d2 = paramResult.y;\n\t\tif ( d >= 0 && d <= 1 && d2 >= 0 && d2 <= 1 ) {\n\n\t\t\tl1.at( d, target1 );\n\t\t\tl2.at( d2, target2 );\n\n\t\t\treturn;\n\n\t\t} else if ( d >= 0 && d <= 1 ) {\n\n\t\t\t// Only d2 is out of bounds.\n\t\t\tif ( d2 < 0 ) {\n\n\t\t\t\tl2.at( 0, target2 );\n\n\t\t\t} else {\n\n\t\t\t\tl2.at( 1, target2 );\n\n\t\t\t}\n\n\t\t\tl1.closestPointToPoint( target2, true, target1 );\n\t\t\treturn;\n\n\t\t} else if ( d2 >= 0 && d2 <= 1 ) {\n\n\t\t\t// Only d is out of bounds.\n\t\t\tif ( d < 0 ) {\n\n\t\t\t\tl1.at( 0, target1 );\n\n\t\t\t} else {\n\n\t\t\t\tl1.at( 1, target1 );\n\n\t\t\t}\n\n\t\t\tl2.closestPointToPoint( target1, true, target2 );\n\t\t\treturn;\n\n\t\t} else {\n\n\t\t\t// Both u and u2 are out of bounds.\n\t\t\tlet p;\n\t\t\tif ( d < 0 ) {\n\n\t\t\t\tp = l1.start;\n\n\t\t\t} else {\n\n\t\t\t\tp = l1.end;\n\n\t\t\t}\n\n\t\t\tlet p2;\n\t\t\tif ( d2 < 0 ) {\n\n\t\t\t\tp2 = l2.start;\n\n\t\t\t} else {\n\n\t\t\t\tp2 = l2.end;\n\n\t\t\t}\n\n\t\t\tconst closestPoint = temp1;\n\t\t\tconst closestPoint2 = temp2;\n\t\t\tl1.closestPointToPoint( p2, true, temp1 );\n\t\t\tl2.closestPointToPoint( p, true, temp2 );\n\n\t\t\tif ( closestPoint.distanceToSquared( p2 ) <= closestPoint2.distanceToSquared( p ) ) {\n\n\t\t\t\ttarget1.copy( closestPoint );\n\t\t\t\ttarget2.copy( p2 );\n\t\t\t\treturn;\n\n\t\t\t} else {\n\n\t\t\t\ttarget1.copy( p );\n\t\t\t\ttarget2.copy( closestPoint2 );\n\t\t\t\treturn;\n\n\t\t\t}\n\n\t\t}\n\n\t};\n\n} )();\n\n\nexport const sphereIntersectTriangle = ( function () {\n\n\t// https://stackoverflow.com/questions/34043955/detect-collision-between-sphere-and-triangle-in-three-js\n\tconst closestPointTemp = new Vector3();\n\tconst projectedPointTemp = new Vector3();\n\tconst planeTemp = new Plane();\n\tconst lineTemp = new Line3();\n\treturn function sphereIntersectTriangle( sphere, triangle ) {\n\n\t\tconst { radius, center } = sphere;\n\t\tconst { a, b, c } = triangle;\n\n\t\t// phase 1\n\t\tlineTemp.start = a;\n\t\tlineTemp.end = b;\n\t\tconst closestPoint1 = lineTemp.closestPointToPoint( center, true, closestPointTemp );\n\t\tif ( closestPoint1.distanceTo( center ) <= radius ) return true;\n\n\t\tlineTemp.start = a;\n\t\tlineTemp.end = c;\n\t\tconst closestPoint2 = lineTemp.closestPointToPoint( center, true, closestPointTemp );\n\t\tif ( closestPoint2.distanceTo( center ) <= radius ) return true;\n\n\t\tlineTemp.start = b;\n\t\tlineTemp.end = c;\n\t\tconst closestPoint3 = lineTemp.closestPointToPoint( center, true, closestPointTemp );\n\t\tif ( closestPoint3.distanceTo( center ) <= radius ) return true;\n\n\t\t// phase 2\n\t\tconst plane = triangle.getPlane( planeTemp );\n\t\tconst dp = Math.abs( plane.distanceToPoint( center ) );\n\t\tif ( dp <= radius ) {\n\n\t\t\tconst pp = plane.projectPoint( center, projectedPointTemp );\n\t\t\tconst cp = triangle.containsPoint( pp );\n\t\t\tif ( cp ) return true;\n\n\t\t}\n\n\t\treturn false;\n\n\t};\n\n} )();\n"], "names": [], "mappings": ";;;;;AAAA;;AAEO,MAAM,yBAAyB,AAAE;IAEvC,0EAA0E;IAC1E,MAAM,OAAO,IAAI,kJAAA,CAAA,UAAO;IACxB,MAAM,OAAO,IAAI,kJAAA,CAAA,UAAO;IACxB,MAAM,MAAM,IAAI,kJAAA,CAAA,UAAO;IACvB,OAAO,SAAS,uBAAwB,EAAE,EAAE,EAAE,EAAE,MAAM;QAErD,MAAM,KAAK,GAAG,KAAK;QACnB,MAAM,MAAM;QACZ,MAAM,KAAK,GAAG,KAAK;QACnB,MAAM,MAAM;QAEZ,IAAI,UAAU,CAAE,IAAI;QACpB,KAAK,UAAU,CAAE,GAAG,GAAG,EAAE,GAAG,KAAK;QACjC,KAAK,UAAU,CAAE,GAAG,GAAG,EAAE,GAAG,KAAK;QAEjC,8BAA8B;QAC9B,MAAM,QAAQ,IAAI,GAAG,CAAE;QAEvB,8BAA8B;QAC9B,MAAM,QAAQ,IAAI,GAAG,CAAE;QAEvB,8BAA8B;QAC9B,MAAM,QAAQ,IAAI,GAAG,CAAE;QAEvB,8BAA8B;QAC9B,MAAM,QAAQ,IAAI,GAAG,CAAE;QAEvB,8BAA8B;QAC9B,MAAM,QAAQ,IAAI,GAAG,CAAE;QAEvB,2CAA2C;QAC3C,MAAM,QAAQ,QAAQ,QAAQ,QAAQ;QAEtC,IAAI,GAAG;QACP,IAAK,UAAU,GAAI;YAElB,IAAI,CAAE,QAAQ,QAAQ,QAAQ,KAAM,IAAI;QAEzC,OAAO;YAEN,IAAI;QAEL;QAEA,KAAK,CAAE,QAAQ,IAAI,KAAM,IAAI;QAE7B,OAAO,CAAC,GAAG;QACX,OAAO,CAAC,GAAG;IAEZ;AAED;AAEO,MAAM,gCAAgC,AAAE;IAE9C,kFAAkF;IAClF,MAAM,cAAc,IAAI,kJAAA,CAAA,UAAO;IAC/B,MAAM,QAAQ,IAAI,kJAAA,CAAA,UAAO;IACzB,MAAM,QAAQ,IAAI,kJAAA,CAAA,UAAO;IACzB,OAAO,SAAS,8BAA+B,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO;QAEtE,uBAAwB,IAAI,IAAI;QAEhC,IAAI,IAAI,YAAY,CAAC;QACrB,IAAI,KAAK,YAAY,CAAC;QACtB,IAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM,GAAI;YAE7C,GAAG,EAAE,CAAE,GAAG;YACV,GAAG,EAAE,CAAE,IAAI;YAEX;QAED,OAAO,IAAK,KAAK,KAAK,KAAK,GAAI;YAE9B,4BAA4B;YAC5B,IAAK,KAAK,GAAI;gBAEb,GAAG,EAAE,CAAE,GAAG;YAEX,OAAO;gBAEN,GAAG,EAAE,CAAE,GAAG;YAEX;YAEA,GAAG,mBAAmB,CAAE,SAAS,MAAM;YACvC;QAED,OAAO,IAAK,MAAM,KAAK,MAAM,GAAI;YAEhC,2BAA2B;YAC3B,IAAK,IAAI,GAAI;gBAEZ,GAAG,EAAE,CAAE,GAAG;YAEX,OAAO;gBAEN,GAAG,EAAE,CAAE,GAAG;YAEX;YAEA,GAAG,mBAAmB,CAAE,SAAS,MAAM;YACvC;QAED,OAAO;YAEN,mCAAmC;YACnC,IAAI;YACJ,IAAK,IAAI,GAAI;gBAEZ,IAAI,GAAG,KAAK;YAEb,OAAO;gBAEN,IAAI,GAAG,GAAG;YAEX;YAEA,IAAI;YACJ,IAAK,KAAK,GAAI;gBAEb,KAAK,GAAG,KAAK;YAEd,OAAO;gBAEN,KAAK,GAAG,GAAG;YAEZ;YAEA,MAAM,eAAe;YACrB,MAAM,gBAAgB;YACtB,GAAG,mBAAmB,CAAE,IAAI,MAAM;YAClC,GAAG,mBAAmB,CAAE,GAAG,MAAM;YAEjC,IAAK,aAAa,iBAAiB,CAAE,OAAQ,cAAc,iBAAiB,CAAE,IAAM;gBAEnF,QAAQ,IAAI,CAAE;gBACd,QAAQ,IAAI,CAAE;gBACd;YAED,OAAO;gBAEN,QAAQ,IAAI,CAAE;gBACd,QAAQ,IAAI,CAAE;gBACd;YAED;QAED;IAED;AAED;AAGO,MAAM,0BAA0B,AAAE;IAExC,wGAAwG;IACxG,MAAM,mBAAmB,IAAI,kJAAA,CAAA,UAAO;IACpC,MAAM,qBAAqB,IAAI,kJAAA,CAAA,UAAO;IACtC,MAAM,YAAY,IAAI,kJAAA,CAAA,QAAK;IAC3B,MAAM,WAAW,IAAI,kJAAA,CAAA,QAAK;IAC1B,OAAO,SAAS,wBAAyB,MAAM,EAAE,QAAQ;QAExD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG;QAC3B,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG;QAEpB,UAAU;QACV,SAAS,KAAK,GAAG;QACjB,SAAS,GAAG,GAAG;QACf,MAAM,gBAAgB,SAAS,mBAAmB,CAAE,QAAQ,MAAM;QAClE,IAAK,cAAc,UAAU,CAAE,WAAY,QAAS,OAAO;QAE3D,SAAS,KAAK,GAAG;QACjB,SAAS,GAAG,GAAG;QACf,MAAM,gBAAgB,SAAS,mBAAmB,CAAE,QAAQ,MAAM;QAClE,IAAK,cAAc,UAAU,CAAE,WAAY,QAAS,OAAO;QAE3D,SAAS,KAAK,GAAG;QACjB,SAAS,GAAG,GAAG;QACf,MAAM,gBAAgB,SAAS,mBAAmB,CAAE,QAAQ,MAAM;QAClE,IAAK,cAAc,UAAU,CAAE,WAAY,QAAS,OAAO;QAE3D,UAAU;QACV,MAAM,QAAQ,SAAS,QAAQ,CAAE;QACjC,MAAM,KAAK,KAAK,GAAG,CAAE,MAAM,eAAe,CAAE;QAC5C,IAAK,MAAM,QAAS;YAEnB,MAAM,KAAK,MAAM,YAAY,CAAE,QAAQ;YACvC,MAAM,KAAK,SAAS,aAAa,CAAE;YACnC,IAAK,IAAK,OAAO;QAElB;QAEA,OAAO;IAER;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/math/ExtendedTriangle.js"], "sourcesContent": ["import { Triangle, Vector3, Line3, Sphere, Plane } from 'three';\nimport { SeparatingAxisBounds } from './SeparatingAxisBounds.js';\nimport { closestPointsSegmentToSegment, sphereIntersectTriangle } from './MathUtilities.js';\n\nconst ZERO_EPSILON = 1e-15;\nfunction isNearZero( value ) {\n\n\treturn Math.abs( value ) < ZERO_EPSILON;\n\n}\n\nexport class ExtendedTriangle extends Triangle {\n\n\tconstructor( ...args ) {\n\n\t\tsuper( ...args );\n\n\t\tthis.isExtendedTriangle = true;\n\t\tthis.satAxes = new Array( 4 ).fill().map( () => new Vector3() );\n\t\tthis.satBounds = new Array( 4 ).fill().map( () => new SeparatingAxisBounds() );\n\t\tthis.points = [ this.a, this.b, this.c ];\n\t\tthis.sphere = new Sphere();\n\t\tthis.plane = new Plane();\n\t\tthis.needsUpdate = true;\n\n\t}\n\n\tintersectsSphere( sphere ) {\n\n\t\treturn sphereIntersectTriangle( sphere, this );\n\n\t}\n\n\tupdate() {\n\n\t\tconst a = this.a;\n\t\tconst b = this.b;\n\t\tconst c = this.c;\n\t\tconst points = this.points;\n\n\t\tconst satAxes = this.satAxes;\n\t\tconst satBounds = this.satBounds;\n\n\t\tconst axis0 = satAxes[ 0 ];\n\t\tconst sab0 = satBounds[ 0 ];\n\t\tthis.getNormal( axis0 );\n\t\tsab0.setFromPoints( axis0, points );\n\n\t\tconst axis1 = satAxes[ 1 ];\n\t\tconst sab1 = satBounds[ 1 ];\n\t\taxis1.subVectors( a, b );\n\t\tsab1.setFromPoints( axis1, points );\n\n\t\tconst axis2 = satAxes[ 2 ];\n\t\tconst sab2 = satBounds[ 2 ];\n\t\taxis2.subVectors( b, c );\n\t\tsab2.setFromPoints( axis2, points );\n\n\t\tconst axis3 = satAxes[ 3 ];\n\t\tconst sab3 = satBounds[ 3 ];\n\t\taxis3.subVectors( c, a );\n\t\tsab3.setFromPoints( axis3, points );\n\n\t\tthis.sphere.setFromPoints( this.points );\n\t\tthis.plane.setFromNormalAndCoplanarPoint( axis0, a );\n\t\tthis.needsUpdate = false;\n\n\t}\n\n}\n\nExtendedTriangle.prototype.closestPointToSegment = ( function () {\n\n\tconst point1 = new Vector3();\n\tconst point2 = new Vector3();\n\tconst edge = new Line3();\n\n\treturn function distanceToSegment( segment, target1 = null, target2 = null ) {\n\n\t\tconst { start, end } = segment;\n\t\tconst points = this.points;\n\t\tlet distSq;\n\t\tlet closestDistanceSq = Infinity;\n\n\t\t// check the triangle edges\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst nexti = ( i + 1 ) % 3;\n\t\t\tedge.start.copy( points[ i ] );\n\t\t\tedge.end.copy( points[ nexti ] );\n\n\t\t\tclosestPointsSegmentToSegment( edge, segment, point1, point2 );\n\n\t\t\tdistSq = point1.distanceToSquared( point2 );\n\t\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = distSq;\n\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// check end points\n\t\tthis.closestPointToPoint( start, point1 );\n\t\tdistSq = start.distanceToSquared( point1 );\n\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\tclosestDistanceSq = distSq;\n\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\tif ( target2 ) target2.copy( start );\n\n\t\t}\n\n\t\tthis.closestPointToPoint( end, point1 );\n\t\tdistSq = end.distanceToSquared( point1 );\n\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\tclosestDistanceSq = distSq;\n\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\tif ( target2 ) target2.copy( end );\n\n\t\t}\n\n\t\treturn Math.sqrt( closestDistanceSq );\n\n\t};\n\n} )();\n\nExtendedTriangle.prototype.intersectsTriangle = ( function () {\n\n\tconst saTri2 = new ExtendedTriangle();\n\tconst arr1 = new Array( 3 );\n\tconst arr2 = new Array( 3 );\n\tconst cachedSatBounds = new SeparatingAxisBounds();\n\tconst cachedSatBounds2 = new SeparatingAxisBounds();\n\tconst cachedAxis = new Vector3();\n\tconst dir = new Vector3();\n\tconst dir1 = new Vector3();\n\tconst dir2 = new Vector3();\n\tconst tempDir = new Vector3();\n\tconst edge = new Line3();\n\tconst edge1 = new Line3();\n\tconst edge2 = new Line3();\n\tconst tempPoint = new Vector3();\n\n\tfunction triIntersectPlane( tri, plane, targetEdge ) {\n\n\t\t// find the edge that intersects the other triangle plane\n\t\tconst points = tri.points;\n\t\tlet count = 0;\n\t\tlet startPointIntersection = - 1;\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst { start, end } = edge;\n\t\t\tstart.copy( points[ i ] );\n\t\t\tend.copy( points[ ( i + 1 ) % 3 ] );\n\t\t\tedge.delta( dir );\n\n\t\t\tconst startIntersects = isNearZero( plane.distanceToPoint( start ) );\n\t\t\tif ( isNearZero( plane.normal.dot( dir ) ) && startIntersects ) {\n\n\t\t\t\t// if the edge lies on the plane then take the line\n\t\t\t\ttargetEdge.copy( edge );\n\t\t\t\tcount = 2;\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\t// check if the start point is near the plane because \"intersectLine\" is not robust to that case\n\t\t\tconst doesIntersect = plane.intersectLine( edge, tempPoint );\n\t\t\tif ( ! doesIntersect && startIntersects ) {\n\n\t\t\t\ttempPoint.copy( start );\n\n\t\t\t}\n\n\t\t\t// ignore the end point\n\t\t\tif ( ( doesIntersect || startIntersects ) && ! isNearZero( tempPoint.distanceTo( end ) ) ) {\n\n\t\t\t\tif ( count <= 1 ) {\n\n\t\t\t\t\t// assign to the start or end point and save which index was snapped to\n\t\t\t\t\t// the start point if necessary\n\t\t\t\t\tconst point = count === 1 ? targetEdge.start : targetEdge.end;\n\t\t\t\t\tpoint.copy( tempPoint );\n\t\t\t\t\tif ( startIntersects ) {\n\n\t\t\t\t\t\tstartPointIntersection = count;\n\n\t\t\t\t\t}\n\n\t\t\t\t} else if ( count >= 2 ) {\n\n\t\t\t\t\t// if we're here that means that there must have been one point that had\n\t\t\t\t\t// snapped to the start point so replace it here\n\t\t\t\t\tconst point = startPointIntersection === 1 ? targetEdge.start : targetEdge.end;\n\t\t\t\t\tpoint.copy( tempPoint );\n\t\t\t\t\tcount = 2;\n\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t\tcount ++;\n\t\t\t\tif ( count === 2 && startPointIntersection === - 1 ) {\n\n\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn count;\n\n\t}\n\n\t// TODO: If the triangles are coplanar and intersecting the target is nonsensical. It should at least\n\t// be a line contained by both triangles if not a different special case somehow represented in the return result.\n\treturn function intersectsTriangle( other, target = null, suppressLog = false ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tif ( ! other.isExtendedTriangle ) {\n\n\t\t\tsaTri2.copy( other );\n\t\t\tsaTri2.update();\n\t\t\tother = saTri2;\n\n\t\t} else if ( other.needsUpdate ) {\n\n\t\t\tother.update();\n\n\t\t}\n\n\t\tconst plane1 = this.plane;\n\t\tconst plane2 = other.plane;\n\n\t\tif ( Math.abs( plane1.normal.dot( plane2.normal ) ) > 1.0 - 1e-10 ) {\n\n\t\t\t// perform separating axis intersection test only for coplanar triangles\n\t\t\tconst satBounds1 = this.satBounds;\n\t\t\tconst satAxes1 = this.satAxes;\n\t\t\tarr2[ 0 ] = other.a;\n\t\t\tarr2[ 1 ] = other.b;\n\t\t\tarr2[ 2 ] = other.c;\n\t\t\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\t\t\tconst sb = satBounds1[ i ];\n\t\t\t\tconst sa = satAxes1[ i ];\n\t\t\t\tcachedSatBounds.setFromPoints( sa, arr2 );\n\t\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t\t}\n\n\t\t\tconst satBounds2 = other.satBounds;\n\t\t\tconst satAxes2 = other.satAxes;\n\t\t\tarr1[ 0 ] = this.a;\n\t\t\tarr1[ 1 ] = this.b;\n\t\t\tarr1[ 2 ] = this.c;\n\t\t\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\t\t\tconst sb = satBounds2[ i ];\n\t\t\t\tconst sa = satAxes2[ i ];\n\t\t\t\tcachedSatBounds.setFromPoints( sa, arr1 );\n\t\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t\t}\n\n\t\t\t// check crossed axes\n\t\t\tfor ( let i = 0; i < 4; i ++ ) {\n\n\t\t\t\tconst sa1 = satAxes1[ i ];\n\t\t\t\tfor ( let i2 = 0; i2 < 4; i2 ++ ) {\n\n\t\t\t\t\tconst sa2 = satAxes2[ i2 ];\n\t\t\t\t\tcachedAxis.crossVectors( sa1, sa2 );\n\t\t\t\t\tcachedSatBounds.setFromPoints( cachedAxis, arr1 );\n\t\t\t\t\tcachedSatBounds2.setFromPoints( cachedAxis, arr2 );\n\t\t\t\t\tif ( cachedSatBounds.isSeparated( cachedSatBounds2 ) ) return false;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( target ) {\n\n\t\t\t\t// TODO find two points that intersect on the edges and make that the result\n\t\t\t\tif ( ! suppressLog ) {\n\n\t\t\t\t\tconsole.warn( 'ExtendedTriangle.intersectsTriangle: Triangles are coplanar which does not support an output edge. Setting edge to 0, 0, 0.' );\n\n\t\t\t\t}\n\n\t\t\t\ttarget.start.set( 0, 0, 0 );\n\t\t\t\ttarget.end.set( 0, 0, 0 );\n\n\t\t\t}\n\n\t\t\treturn true;\n\n\t\t} else {\n\n\t\t\t// find the edge that intersects the other triangle plane\n\t\t\tconst count1 = triIntersectPlane( this, plane2, edge1 );\n\t\t\tif ( count1 === 1 && other.containsPoint( edge1.end ) ) {\n\n\t\t\t\tif ( target ) {\n\n\t\t\t\t\ttarget.start.copy( edge1.end );\n\t\t\t\t\ttarget.end.copy( edge1.end );\n\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\n\t\t\t} else if ( count1 !== 2 ) {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\t// find the other triangles edge that intersects this plane\n\t\t\tconst count2 = triIntersectPlane( other, plane1, edge2 );\n\t\t\tif ( count2 === 1 && this.containsPoint( edge2.end ) ) {\n\n\t\t\t\tif ( target ) {\n\n\t\t\t\t\ttarget.start.copy( edge2.end );\n\t\t\t\t\ttarget.end.copy( edge2.end );\n\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\n\t\t\t} else if ( count2 !== 2 ) {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\t// find swap the second edge so both lines are running the same direction\n\t\t\tedge1.delta( dir1 );\n\t\t\tedge2.delta( dir2 );\n\n\t\t\tif ( dir1.dot( dir2 ) < 0 ) {\n\n\t\t\t\tlet tmp = edge2.start;\n\t\t\t\tedge2.start = edge2.end;\n\t\t\t\tedge2.end = tmp;\n\n\t\t\t}\n\n\t\t\t// check if the edges are overlapping\n\t\t\tconst s1 = edge1.start.dot( dir1 );\n\t\t\tconst e1 = edge1.end.dot( dir1 );\n\t\t\tconst s2 = edge2.start.dot( dir1 );\n\t\t\tconst e2 = edge2.end.dot( dir1 );\n\t\t\tconst separated1 = e1 < s2;\n\t\t\tconst separated2 = s1 < e2;\n\n\t\t\tif ( s1 !== e2 && s2 !== e1 && separated1 === separated2 ) {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t\t// assign the target output\n\t\t\tif ( target ) {\n\n\t\t\t\ttempDir.subVectors( edge1.start, edge2.start );\n\t\t\t\tif ( tempDir.dot( dir1 ) > 0 ) {\n\n\t\t\t\t\ttarget.start.copy( edge1.start );\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttarget.start.copy( edge2.start );\n\n\t\t\t\t}\n\n\t\t\t\ttempDir.subVectors( edge1.end, edge2.end );\n\t\t\t\tif ( tempDir.dot( dir1 ) < 0 ) {\n\n\t\t\t\t\ttarget.end.copy( edge1.end );\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttarget.end.copy( edge2.end );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn true;\n\n\t\t}\n\n\t};\n\n} )();\n\n\nExtendedTriangle.prototype.distanceToPoint = ( function () {\n\n\tconst target = new Vector3();\n\treturn function distanceToPoint( point ) {\n\n\t\tthis.closestPointToPoint( point, target );\n\t\treturn point.distanceTo( target );\n\n\t};\n\n} )();\n\n\nExtendedTriangle.prototype.distanceToTriangle = ( function () {\n\n\tconst point = new Vector3();\n\tconst point2 = new Vector3();\n\tconst cornerFields = [ 'a', 'b', 'c' ];\n\tconst line1 = new Line3();\n\tconst line2 = new Line3();\n\n\treturn function distanceToTriangle( other, target1 = null, target2 = null ) {\n\n\t\tconst lineTarget = target1 || target2 ? line1 : null;\n\t\tif ( this.intersectsTriangle( other, lineTarget ) ) {\n\n\t\t\tif ( target1 || target2 ) {\n\n\t\t\t\tif ( target1 ) lineTarget.getCenter( target1 );\n\t\t\t\tif ( target2 ) lineTarget.getCenter( target2 );\n\n\t\t\t}\n\n\t\t\treturn 0;\n\n\t\t}\n\n\t\tlet closestDistanceSq = Infinity;\n\n\t\t// check all point distances\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tlet dist;\n\t\t\tconst field = cornerFields[ i ];\n\t\t\tconst otherVec = other[ field ];\n\t\t\tthis.closestPointToPoint( otherVec, point );\n\n\t\t\tdist = otherVec.distanceToSquared( point );\n\n\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\tif ( target1 ) target1.copy( point );\n\t\t\t\tif ( target2 ) target2.copy( otherVec );\n\n\t\t\t}\n\n\n\t\t\tconst thisVec = this[ field ];\n\t\t\tother.closestPointToPoint( thisVec, point );\n\n\t\t\tdist = thisVec.distanceToSquared( point );\n\n\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\tif ( target1 ) target1.copy( thisVec );\n\t\t\t\tif ( target2 ) target2.copy( point );\n\n\t\t\t}\n\n\t\t}\n\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst f11 = cornerFields[ i ];\n\t\t\tconst f12 = cornerFields[ ( i + 1 ) % 3 ];\n\t\t\tline1.set( this[ f11 ], this[ f12 ] );\n\t\t\tfor ( let i2 = 0; i2 < 3; i2 ++ ) {\n\n\t\t\t\tconst f21 = cornerFields[ i2 ];\n\t\t\t\tconst f22 = cornerFields[ ( i2 + 1 ) % 3 ];\n\t\t\t\tline2.set( other[ f21 ], other[ f22 ] );\n\n\t\t\t\tclosestPointsSegmentToSegment( line1, line2, point, point2 );\n\n\t\t\t\tconst dist = point.distanceToSquared( point2 );\n\t\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\t\tif ( target1 ) target1.copy( point );\n\t\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Math.sqrt( closestDistanceSq );\n\n\t};\n\n} )();\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,eAAe;AACrB,SAAS,WAAY,KAAK;IAEzB,OAAO,KAAK,GAAG,CAAE,SAAU;AAE5B;AAEO,MAAM,yBAAyB,kJAAA,CAAA,WAAQ;IAE7C,YAAa,GAAG,IAAI,CAAG;QAEtB,KAAK,IAAK;QAEV,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,MAAO,GAAI,IAAI,GAAG,GAAG,CAAE,IAAM,IAAI,kJAAA,CAAA,UAAO;QAC3D,IAAI,CAAC,SAAS,GAAG,IAAI,MAAO,GAAI,IAAI,GAAG,GAAG,CAAE,IAAM,IAAI,8KAAA,CAAA,uBAAoB;QAC1E,IAAI,CAAC,MAAM,GAAG;YAAE,IAAI,CAAC,CAAC;YAAE,IAAI,CAAC,CAAC;YAAE,IAAI,CAAC,CAAC;SAAE;QACxC,IAAI,CAAC,MAAM,GAAG,IAAI,kJAAA,CAAA,SAAM;QACxB,IAAI,CAAC,KAAK,GAAG,IAAI,kJAAA,CAAA,QAAK;QACtB,IAAI,CAAC,WAAW,GAAG;IAEpB;IAEA,iBAAkB,MAAM,EAAG;QAE1B,OAAO,CAAA,GAAA,uKAAA,CAAA,0BAAuB,AAAD,EAAG,QAAQ,IAAI;IAE7C;IAEA,SAAS;QAER,MAAM,IAAI,IAAI,CAAC,CAAC;QAChB,MAAM,IAAI,IAAI,CAAC,CAAC;QAChB,MAAM,IAAI,IAAI,CAAC,CAAC;QAChB,MAAM,SAAS,IAAI,CAAC,MAAM;QAE1B,MAAM,UAAU,IAAI,CAAC,OAAO;QAC5B,MAAM,YAAY,IAAI,CAAC,SAAS;QAEhC,MAAM,QAAQ,OAAO,CAAE,EAAG;QAC1B,MAAM,OAAO,SAAS,CAAE,EAAG;QAC3B,IAAI,CAAC,SAAS,CAAE;QAChB,KAAK,aAAa,CAAE,OAAO;QAE3B,MAAM,QAAQ,OAAO,CAAE,EAAG;QAC1B,MAAM,OAAO,SAAS,CAAE,EAAG;QAC3B,MAAM,UAAU,CAAE,GAAG;QACrB,KAAK,aAAa,CAAE,OAAO;QAE3B,MAAM,QAAQ,OAAO,CAAE,EAAG;QAC1B,MAAM,OAAO,SAAS,CAAE,EAAG;QAC3B,MAAM,UAAU,CAAE,GAAG;QACrB,KAAK,aAAa,CAAE,OAAO;QAE3B,MAAM,QAAQ,OAAO,CAAE,EAAG;QAC1B,MAAM,OAAO,SAAS,CAAE,EAAG;QAC3B,MAAM,UAAU,CAAE,GAAG;QACrB,KAAK,aAAa,CAAE,OAAO;QAE3B,IAAI,CAAC,MAAM,CAAC,aAAa,CAAE,IAAI,CAAC,MAAM;QACtC,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAE,OAAO;QACjD,IAAI,CAAC,WAAW,GAAG;IAEpB;AAED;AAEA,iBAAiB,SAAS,CAAC,qBAAqB,GAAG,AAAE;IAEpD,MAAM,SAAS,IAAI,kJAAA,CAAA,UAAO;IAC1B,MAAM,SAAS,IAAI,kJAAA,CAAA,UAAO;IAC1B,MAAM,OAAO,IAAI,kJAAA,CAAA,QAAK;IAEtB,OAAO,SAAS,kBAAmB,OAAO,EAAE,UAAU,IAAI,EAAE,UAAU,IAAI;QAEzE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG;QACvB,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,IAAI;QACJ,IAAI,oBAAoB;QAExB,2BAA2B;QAC3B,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;YAE9B,MAAM,QAAQ,CAAE,IAAI,CAAE,IAAI;YAC1B,KAAK,KAAK,CAAC,IAAI,CAAE,MAAM,CAAE,EAAG;YAC5B,KAAK,GAAG,CAAC,IAAI,CAAE,MAAM,CAAE,MAAO;YAE9B,CAAA,GAAA,uKAAA,CAAA,gCAA6B,AAAD,EAAG,MAAM,SAAS,QAAQ;YAEtD,SAAS,OAAO,iBAAiB,CAAE;YACnC,IAAK,SAAS,mBAAoB;gBAEjC,oBAAoB;gBACpB,IAAK,SAAU,QAAQ,IAAI,CAAE;gBAC7B,IAAK,SAAU,QAAQ,IAAI,CAAE;YAE9B;QAED;QAEA,mBAAmB;QACnB,IAAI,CAAC,mBAAmB,CAAE,OAAO;QACjC,SAAS,MAAM,iBAAiB,CAAE;QAClC,IAAK,SAAS,mBAAoB;YAEjC,oBAAoB;YACpB,IAAK,SAAU,QAAQ,IAAI,CAAE;YAC7B,IAAK,SAAU,QAAQ,IAAI,CAAE;QAE9B;QAEA,IAAI,CAAC,mBAAmB,CAAE,KAAK;QAC/B,SAAS,IAAI,iBAAiB,CAAE;QAChC,IAAK,SAAS,mBAAoB;YAEjC,oBAAoB;YACpB,IAAK,SAAU,QAAQ,IAAI,CAAE;YAC7B,IAAK,SAAU,QAAQ,IAAI,CAAE;QAE9B;QAEA,OAAO,KAAK,IAAI,CAAE;IAEnB;AAED;AAEA,iBAAiB,SAAS,CAAC,kBAAkB,GAAG,AAAE;IAEjD,MAAM,SAAS,IAAI;IACnB,MAAM,OAAO,IAAI,MAAO;IACxB,MAAM,OAAO,IAAI,MAAO;IACxB,MAAM,kBAAkB,IAAI,8KAAA,CAAA,uBAAoB;IAChD,MAAM,mBAAmB,IAAI,8KAAA,CAAA,uBAAoB;IACjD,MAAM,aAAa,IAAI,kJAAA,CAAA,UAAO;IAC9B,MAAM,MAAM,IAAI,kJAAA,CAAA,UAAO;IACvB,MAAM,OAAO,IAAI,kJAAA,CAAA,UAAO;IACxB,MAAM,OAAO,IAAI,kJAAA,CAAA,UAAO;IACxB,MAAM,UAAU,IAAI,kJAAA,CAAA,UAAO;IAC3B,MAAM,OAAO,IAAI,kJAAA,CAAA,QAAK;IACtB,MAAM,QAAQ,IAAI,kJAAA,CAAA,QAAK;IACvB,MAAM,QAAQ,IAAI,kJAAA,CAAA,QAAK;IACvB,MAAM,YAAY,IAAI,kJAAA,CAAA,UAAO;IAE7B,SAAS,kBAAmB,GAAG,EAAE,KAAK,EAAE,UAAU;QAEjD,yDAAyD;QACzD,MAAM,SAAS,IAAI,MAAM;QACzB,IAAI,QAAQ;QACZ,IAAI,yBAAyB,CAAE;QAC/B,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;YAE9B,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG;YACvB,MAAM,IAAI,CAAE,MAAM,CAAE,EAAG;YACvB,IAAI,IAAI,CAAE,MAAM,CAAE,CAAE,IAAI,CAAE,IAAI,EAAG;YACjC,KAAK,KAAK,CAAE;YAEZ,MAAM,kBAAkB,WAAY,MAAM,eAAe,CAAE;YAC3D,IAAK,WAAY,MAAM,MAAM,CAAC,GAAG,CAAE,SAAW,iBAAkB;gBAE/D,mDAAmD;gBACnD,WAAW,IAAI,CAAE;gBACjB,QAAQ;gBACR;YAED;YAEA,gGAAgG;YAChG,MAAM,gBAAgB,MAAM,aAAa,CAAE,MAAM;YACjD,IAAK,CAAE,iBAAiB,iBAAkB;gBAEzC,UAAU,IAAI,CAAE;YAEjB;YAEA,uBAAuB;YACvB,IAAK,CAAE,iBAAiB,eAAgB,KAAK,CAAE,WAAY,UAAU,UAAU,CAAE,OAAU;gBAE1F,IAAK,SAAS,GAAI;oBAEjB,uEAAuE;oBACvE,+BAA+B;oBAC/B,MAAM,QAAQ,UAAU,IAAI,WAAW,KAAK,GAAG,WAAW,GAAG;oBAC7D,MAAM,IAAI,CAAE;oBACZ,IAAK,iBAAkB;wBAEtB,yBAAyB;oBAE1B;gBAED,OAAO,IAAK,SAAS,GAAI;oBAExB,wEAAwE;oBACxE,gDAAgD;oBAChD,MAAM,QAAQ,2BAA2B,IAAI,WAAW,KAAK,GAAG,WAAW,GAAG;oBAC9E,MAAM,IAAI,CAAE;oBACZ,QAAQ;oBACR;gBAED;gBAEA;gBACA,IAAK,UAAU,KAAK,2BAA2B,CAAE,GAAI;oBAEpD;gBAED;YAED;QAED;QAEA,OAAO;IAER;IAEA,qGAAqG;IACrG,kHAAkH;IAClH,OAAO,SAAS,mBAAoB,KAAK,EAAE,SAAS,IAAI,EAAE,cAAc,KAAK;QAE5E,IAAK,IAAI,CAAC,WAAW,EAAG;YAEvB,IAAI,CAAC,MAAM;QAEZ;QAEA,IAAK,CAAE,MAAM,kBAAkB,EAAG;YAEjC,OAAO,IAAI,CAAE;YACb,OAAO,MAAM;YACb,QAAQ;QAET,OAAO,IAAK,MAAM,WAAW,EAAG;YAE/B,MAAM,MAAM;QAEb;QAEA,MAAM,SAAS,IAAI,CAAC,KAAK;QACzB,MAAM,SAAS,MAAM,KAAK;QAE1B,IAAK,KAAK,GAAG,CAAE,OAAO,MAAM,CAAC,GAAG,CAAE,OAAO,MAAM,KAAO,MAAM,OAAQ;YAEnE,wEAAwE;YACxE,MAAM,aAAa,IAAI,CAAC,SAAS;YACjC,MAAM,WAAW,IAAI,CAAC,OAAO;YAC7B,IAAI,CAAE,EAAG,GAAG,MAAM,CAAC;YACnB,IAAI,CAAE,EAAG,GAAG,MAAM,CAAC;YACnB,IAAI,CAAE,EAAG,GAAG,MAAM,CAAC;YACnB,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;gBAE9B,MAAM,KAAK,UAAU,CAAE,EAAG;gBAC1B,MAAM,KAAK,QAAQ,CAAE,EAAG;gBACxB,gBAAgB,aAAa,CAAE,IAAI;gBACnC,IAAK,GAAG,WAAW,CAAE,kBAAoB,OAAO;YAEjD;YAEA,MAAM,aAAa,MAAM,SAAS;YAClC,MAAM,WAAW,MAAM,OAAO;YAC9B,IAAI,CAAE,EAAG,GAAG,IAAI,CAAC,CAAC;YAClB,IAAI,CAAE,EAAG,GAAG,IAAI,CAAC,CAAC;YAClB,IAAI,CAAE,EAAG,GAAG,IAAI,CAAC,CAAC;YAClB,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;gBAE9B,MAAM,KAAK,UAAU,CAAE,EAAG;gBAC1B,MAAM,KAAK,QAAQ,CAAE,EAAG;gBACxB,gBAAgB,aAAa,CAAE,IAAI;gBACnC,IAAK,GAAG,WAAW,CAAE,kBAAoB,OAAO;YAEjD;YAEA,qBAAqB;YACrB,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;gBAE9B,MAAM,MAAM,QAAQ,CAAE,EAAG;gBACzB,IAAM,IAAI,KAAK,GAAG,KAAK,GAAG,KAAQ;oBAEjC,MAAM,MAAM,QAAQ,CAAE,GAAI;oBAC1B,WAAW,YAAY,CAAE,KAAK;oBAC9B,gBAAgB,aAAa,CAAE,YAAY;oBAC3C,iBAAiB,aAAa,CAAE,YAAY;oBAC5C,IAAK,gBAAgB,WAAW,CAAE,mBAAqB,OAAO;gBAE/D;YAED;YAEA,IAAK,QAAS;gBAEb,4EAA4E;gBAC5E,IAAK,CAAE,aAAc;oBAEpB,QAAQ,IAAI,CAAE;gBAEf;gBAEA,OAAO,KAAK,CAAC,GAAG,CAAE,GAAG,GAAG;gBACxB,OAAO,GAAG,CAAC,GAAG,CAAE,GAAG,GAAG;YAEvB;YAEA,OAAO;QAER,OAAO;YAEN,yDAAyD;YACzD,MAAM,SAAS,kBAAmB,IAAI,EAAE,QAAQ;YAChD,IAAK,WAAW,KAAK,MAAM,aAAa,CAAE,MAAM,GAAG,GAAK;gBAEvD,IAAK,QAAS;oBAEb,OAAO,KAAK,CAAC,IAAI,CAAE,MAAM,GAAG;oBAC5B,OAAO,GAAG,CAAC,IAAI,CAAE,MAAM,GAAG;gBAE3B;gBAEA,OAAO;YAER,OAAO,IAAK,WAAW,GAAI;gBAE1B,OAAO;YAER;YAEA,2DAA2D;YAC3D,MAAM,SAAS,kBAAmB,OAAO,QAAQ;YACjD,IAAK,WAAW,KAAK,IAAI,CAAC,aAAa,CAAE,MAAM,GAAG,GAAK;gBAEtD,IAAK,QAAS;oBAEb,OAAO,KAAK,CAAC,IAAI,CAAE,MAAM,GAAG;oBAC5B,OAAO,GAAG,CAAC,IAAI,CAAE,MAAM,GAAG;gBAE3B;gBAEA,OAAO;YAER,OAAO,IAAK,WAAW,GAAI;gBAE1B,OAAO;YAER;YAEA,yEAAyE;YACzE,MAAM,KAAK,CAAE;YACb,MAAM,KAAK,CAAE;YAEb,IAAK,KAAK,GAAG,CAAE,QAAS,GAAI;gBAE3B,IAAI,MAAM,MAAM,KAAK;gBACrB,MAAM,KAAK,GAAG,MAAM,GAAG;gBACvB,MAAM,GAAG,GAAG;YAEb;YAEA,qCAAqC;YACrC,MAAM,KAAK,MAAM,KAAK,CAAC,GAAG,CAAE;YAC5B,MAAM,KAAK,MAAM,GAAG,CAAC,GAAG,CAAE;YAC1B,MAAM,KAAK,MAAM,KAAK,CAAC,GAAG,CAAE;YAC5B,MAAM,KAAK,MAAM,GAAG,CAAC,GAAG,CAAE;YAC1B,MAAM,aAAa,KAAK;YACxB,MAAM,aAAa,KAAK;YAExB,IAAK,OAAO,MAAM,OAAO,MAAM,eAAe,YAAa;gBAE1D,OAAO;YAER;YAEA,2BAA2B;YAC3B,IAAK,QAAS;gBAEb,QAAQ,UAAU,CAAE,MAAM,KAAK,EAAE,MAAM,KAAK;gBAC5C,IAAK,QAAQ,GAAG,CAAE,QAAS,GAAI;oBAE9B,OAAO,KAAK,CAAC,IAAI,CAAE,MAAM,KAAK;gBAE/B,OAAO;oBAEN,OAAO,KAAK,CAAC,IAAI,CAAE,MAAM,KAAK;gBAE/B;gBAEA,QAAQ,UAAU,CAAE,MAAM,GAAG,EAAE,MAAM,GAAG;gBACxC,IAAK,QAAQ,GAAG,CAAE,QAAS,GAAI;oBAE9B,OAAO,GAAG,CAAC,IAAI,CAAE,MAAM,GAAG;gBAE3B,OAAO;oBAEN,OAAO,GAAG,CAAC,IAAI,CAAE,MAAM,GAAG;gBAE3B;YAED;YAEA,OAAO;QAER;IAED;AAED;AAGA,iBAAiB,SAAS,CAAC,eAAe,GAAG,AAAE;IAE9C,MAAM,SAAS,IAAI,kJAAA,CAAA,UAAO;IAC1B,OAAO,SAAS,gBAAiB,KAAK;QAErC,IAAI,CAAC,mBAAmB,CAAE,OAAO;QACjC,OAAO,MAAM,UAAU,CAAE;IAE1B;AAED;AAGA,iBAAiB,SAAS,CAAC,kBAAkB,GAAG,AAAE;IAEjD,MAAM,QAAQ,IAAI,kJAAA,CAAA,UAAO;IACzB,MAAM,SAAS,IAAI,kJAAA,CAAA,UAAO;IAC1B,MAAM,eAAe;QAAE;QAAK;QAAK;KAAK;IACtC,MAAM,QAAQ,IAAI,kJAAA,CAAA,QAAK;IACvB,MAAM,QAAQ,IAAI,kJAAA,CAAA,QAAK;IAEvB,OAAO,SAAS,mBAAoB,KAAK,EAAE,UAAU,IAAI,EAAE,UAAU,IAAI;QAExE,MAAM,aAAa,WAAW,UAAU,QAAQ;QAChD,IAAK,IAAI,CAAC,kBAAkB,CAAE,OAAO,aAAe;YAEnD,IAAK,WAAW,SAAU;gBAEzB,IAAK,SAAU,WAAW,SAAS,CAAE;gBACrC,IAAK,SAAU,WAAW,SAAS,CAAE;YAEtC;YAEA,OAAO;QAER;QAEA,IAAI,oBAAoB;QAExB,4BAA4B;QAC5B,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;YAE9B,IAAI;YACJ,MAAM,QAAQ,YAAY,CAAE,EAAG;YAC/B,MAAM,WAAW,KAAK,CAAE,MAAO;YAC/B,IAAI,CAAC,mBAAmB,CAAE,UAAU;YAEpC,OAAO,SAAS,iBAAiB,CAAE;YAEnC,IAAK,OAAO,mBAAoB;gBAE/B,oBAAoB;gBACpB,IAAK,SAAU,QAAQ,IAAI,CAAE;gBAC7B,IAAK,SAAU,QAAQ,IAAI,CAAE;YAE9B;YAGA,MAAM,UAAU,IAAI,CAAE,MAAO;YAC7B,MAAM,mBAAmB,CAAE,SAAS;YAEpC,OAAO,QAAQ,iBAAiB,CAAE;YAElC,IAAK,OAAO,mBAAoB;gBAE/B,oBAAoB;gBACpB,IAAK,SAAU,QAAQ,IAAI,CAAE;gBAC7B,IAAK,SAAU,QAAQ,IAAI,CAAE;YAE9B;QAED;QAEA,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;YAE9B,MAAM,MAAM,YAAY,CAAE,EAAG;YAC7B,MAAM,MAAM,YAAY,CAAE,CAAE,IAAI,CAAE,IAAI,EAAG;YACzC,MAAM,GAAG,CAAE,IAAI,CAAE,IAAK,EAAE,IAAI,CAAE,IAAK;YACnC,IAAM,IAAI,KAAK,GAAG,KAAK,GAAG,KAAQ;gBAEjC,MAAM,MAAM,YAAY,CAAE,GAAI;gBAC9B,MAAM,MAAM,YAAY,CAAE,CAAE,KAAK,CAAE,IAAI,EAAG;gBAC1C,MAAM,GAAG,CAAE,KAAK,CAAE,IAAK,EAAE,KAAK,CAAE,IAAK;gBAErC,CAAA,GAAA,uKAAA,CAAA,gCAA6B,AAAD,EAAG,OAAO,OAAO,OAAO;gBAEpD,MAAM,OAAO,MAAM,iBAAiB,CAAE;gBACtC,IAAK,OAAO,mBAAoB;oBAE/B,oBAAoB;oBACpB,IAAK,SAAU,QAAQ,IAAI,CAAE;oBAC7B,IAAK,SAAU,QAAQ,IAAI,CAAE;gBAE9B;YAED;QAED;QAEA,OAAO,KAAK,IAAI,CAAE;IAEnB;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/math/OrientedBox.js"], "sourcesContent": ["import { Vector3, Matrix4, Line3 } from 'three';\nimport { SeparatingAxisBounds } from './SeparatingAxisBounds.js';\nimport { ExtendedTriangle } from './ExtendedTriangle.js';\nimport { closestPointsSegmentToSegment } from './MathUtilities.js';\n\nexport class OrientedBox {\n\n\tconstructor( min, max, matrix ) {\n\n\t\tthis.isOrientedBox = true;\n\t\tthis.min = new Vector3();\n\t\tthis.max = new Vector3();\n\t\tthis.matrix = new Matrix4();\n\t\tthis.invMatrix = new Matrix4();\n\t\tthis.points = new Array( 8 ).fill().map( () => new Vector3() );\n\t\tthis.satAxes = new Array( 3 ).fill().map( () => new Vector3() );\n\t\tthis.satBounds = new Array( 3 ).fill().map( () => new SeparatingAxisBounds() );\n\t\tthis.alignedSatBounds = new Array( 3 ).fill().map( () => new SeparatingAxisBounds() );\n\t\tthis.needsUpdate = false;\n\n\t\tif ( min ) this.min.copy( min );\n\t\tif ( max ) this.max.copy( max );\n\t\tif ( matrix ) this.matrix.copy( matrix );\n\n\t}\n\n\tset( min, max, matrix ) {\n\n\t\tthis.min.copy( min );\n\t\tthis.max.copy( max );\n\t\tthis.matrix.copy( matrix );\n\t\tthis.needsUpdate = true;\n\n\t}\n\n\tcopy( other ) {\n\n\t\tthis.min.copy( other.min );\n\t\tthis.max.copy( other.max );\n\t\tthis.matrix.copy( other.matrix );\n\t\tthis.needsUpdate = true;\n\n\t}\n\n}\n\nOrientedBox.prototype.update = ( function () {\n\n\treturn function update() {\n\n\t\tconst matrix = this.matrix;\n\t\tconst min = this.min;\n\t\tconst max = this.max;\n\n\t\tconst points = this.points;\n\t\tfor ( let x = 0; x <= 1; x ++ ) {\n\n\t\t\tfor ( let y = 0; y <= 1; y ++ ) {\n\n\t\t\t\tfor ( let z = 0; z <= 1; z ++ ) {\n\n\t\t\t\t\tconst i = ( ( 1 << 0 ) * x ) | ( ( 1 << 1 ) * y ) | ( ( 1 << 2 ) * z );\n\t\t\t\t\tconst v = points[ i ];\n\t\t\t\t\tv.x = x ? max.x : min.x;\n\t\t\t\t\tv.y = y ? max.y : min.y;\n\t\t\t\t\tv.z = z ? max.z : min.z;\n\n\t\t\t\t\tv.applyMatrix4( matrix );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tconst satBounds = this.satBounds;\n\t\tconst satAxes = this.satAxes;\n\t\tconst minVec = points[ 0 ];\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst axis = satAxes[ i ];\n\t\t\tconst sb = satBounds[ i ];\n\t\t\tconst index = 1 << i;\n\t\t\tconst pi = points[ index ];\n\n\t\t\taxis.subVectors( minVec, pi );\n\t\t\tsb.setFromPoints( axis, points );\n\n\t\t}\n\n\t\tconst alignedSatBounds = this.alignedSatBounds;\n\t\talignedSatBounds[ 0 ].setFromPointsField( points, 'x' );\n\t\talignedSatBounds[ 1 ].setFromPointsField( points, 'y' );\n\t\talignedSatBounds[ 2 ].setFromPointsField( points, 'z' );\n\n\t\tthis.invMatrix.copy( this.matrix ).invert();\n\t\tthis.needsUpdate = false;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.intersectsBox = ( function () {\n\n\tconst aabbBounds = new SeparatingAxisBounds();\n\treturn function intersectsBox( box ) {\n\n\t\t// TODO: should this be doing SAT against the AABB?\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tconst min = box.min;\n\t\tconst max = box.max;\n\t\tconst satBounds = this.satBounds;\n\t\tconst satAxes = this.satAxes;\n\t\tconst alignedSatBounds = this.alignedSatBounds;\n\n\t\taabbBounds.min = min.x;\n\t\taabbBounds.max = max.x;\n\t\tif ( alignedSatBounds[ 0 ].isSeparated( aabbBounds ) ) return false;\n\n\t\taabbBounds.min = min.y;\n\t\taabbBounds.max = max.y;\n\t\tif ( alignedSatBounds[ 1 ].isSeparated( aabbBounds ) ) return false;\n\n\t\taabbBounds.min = min.z;\n\t\taabbBounds.max = max.z;\n\t\tif ( alignedSatBounds[ 2 ].isSeparated( aabbBounds ) ) return false;\n\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst axis = satAxes[ i ];\n\t\t\tconst sb = satBounds[ i ];\n\t\t\taabbBounds.setFromBox( axis, box );\n\t\t\tif ( sb.isSeparated( aabbBounds ) ) return false;\n\n\t\t}\n\n\t\treturn true;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.intersectsTriangle = ( function () {\n\n\tconst saTri = new ExtendedTriangle();\n\tconst pointsArr = new Array( 3 );\n\tconst cachedSatBounds = new SeparatingAxisBounds();\n\tconst cachedSatBounds2 = new SeparatingAxisBounds();\n\tconst cachedAxis = new Vector3();\n\treturn function intersectsTriangle( triangle ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tif ( ! triangle.isExtendedTriangle ) {\n\n\t\t\tsaTri.copy( triangle );\n\t\t\tsaTri.update();\n\t\t\ttriangle = saTri;\n\n\t\t} else if ( triangle.needsUpdate ) {\n\n\t\t\ttriangle.update();\n\n\t\t}\n\n\t\tconst satBounds = this.satBounds;\n\t\tconst satAxes = this.satAxes;\n\n\t\tpointsArr[ 0 ] = triangle.a;\n\t\tpointsArr[ 1 ] = triangle.b;\n\t\tpointsArr[ 2 ] = triangle.c;\n\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = satBounds[ i ];\n\t\t\tconst sa = satAxes[ i ];\n\t\t\tcachedSatBounds.setFromPoints( sa, pointsArr );\n\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t}\n\n\t\tconst triSatBounds = triangle.satBounds;\n\t\tconst triSatAxes = triangle.satAxes;\n\t\tconst points = this.points;\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sb = triSatBounds[ i ];\n\t\t\tconst sa = triSatAxes[ i ];\n\t\t\tcachedSatBounds.setFromPoints( sa, points );\n\t\t\tif ( sb.isSeparated( cachedSatBounds ) ) return false;\n\n\t\t}\n\n\t\t// check crossed axes\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tconst sa1 = satAxes[ i ];\n\t\t\tfor ( let i2 = 0; i2 < 4; i2 ++ ) {\n\n\t\t\t\tconst sa2 = triSatAxes[ i2 ];\n\t\t\t\tcachedAxis.crossVectors( sa1, sa2 );\n\t\t\t\tcachedSatBounds.setFromPoints( cachedAxis, pointsArr );\n\t\t\t\tcachedSatBounds2.setFromPoints( cachedAxis, points );\n\t\t\t\tif ( cachedSatBounds.isSeparated( cachedSatBounds2 ) ) return false;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn true;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.closestPointToPoint = ( function () {\n\n\treturn function closestPointToPoint( point, target1 ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\ttarget1\n\t\t\t.copy( point )\n\t\t\t.applyMatrix4( this.invMatrix )\n\t\t\t.clamp( this.min, this.max )\n\t\t\t.applyMatrix4( this.matrix );\n\n\t\treturn target1;\n\n\t};\n\n} )();\n\nOrientedBox.prototype.distanceToPoint = ( function () {\n\n\tconst target = new Vector3();\n\treturn function distanceToPoint( point ) {\n\n\t\tthis.closestPointToPoint( point, target );\n\t\treturn point.distanceTo( target );\n\n\t};\n\n} )();\n\nOrientedBox.prototype.distanceToBox = ( function () {\n\n\tconst xyzFields = [ 'x', 'y', 'z' ];\n\tconst segments1 = new Array( 12 ).fill().map( () => new Line3() );\n\tconst segments2 = new Array( 12 ).fill().map( () => new Line3() );\n\n\tconst point1 = new Vector3();\n\tconst point2 = new Vector3();\n\n\t// early out if we find a value below threshold\n\treturn function distanceToBox( box, threshold = 0, target1 = null, target2 = null ) {\n\n\t\tif ( this.needsUpdate ) {\n\n\t\t\tthis.update();\n\n\t\t}\n\n\t\tif ( this.intersectsBox( box ) ) {\n\n\t\t\tif ( target1 || target2 ) {\n\n\t\t\t\tbox.getCenter( point2 );\n\t\t\t\tthis.closestPointToPoint( point2, point1 );\n\t\t\t\tbox.closestPointToPoint( point1, point2 );\n\n\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t}\n\n\t\t\treturn 0;\n\n\t\t}\n\n\t\tconst threshold2 = threshold * threshold;\n\t\tconst min = box.min;\n\t\tconst max = box.max;\n\t\tconst points = this.points;\n\n\n\t\t// iterate over every edge and compare distances\n\t\tlet closestDistanceSq = Infinity;\n\n\t\t// check over all these points\n\t\tfor ( let i = 0; i < 8; i ++ ) {\n\n\t\t\tconst p = points[ i ];\n\t\t\tpoint2.copy( p ).clamp( min, max );\n\n\t\t\tconst dist = p.distanceToSquared( point2 );\n\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\tif ( target1 ) target1.copy( p );\n\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\tif ( dist < threshold2 ) return Math.sqrt( dist );\n\n\t\t\t}\n\n\t\t}\n\n\t\t// generate and check all line segment distances\n\t\tlet count = 0;\n\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\tfor ( let i1 = 0; i1 <= 1; i1 ++ ) {\n\n\t\t\t\tfor ( let i2 = 0; i2 <= 1; i2 ++ ) {\n\n\t\t\t\t\tconst nextIndex = ( i + 1 ) % 3;\n\t\t\t\t\tconst nextIndex2 = ( i + 2 ) % 3;\n\n\t\t\t\t\t// get obb line segments\n\t\t\t\t\tconst index = i1 << nextIndex | i2 << nextIndex2;\n\t\t\t\t\tconst index2 = 1 << i | i1 << nextIndex | i2 << nextIndex2;\n\t\t\t\t\tconst p1 = points[ index ];\n\t\t\t\t\tconst p2 = points[ index2 ];\n\t\t\t\t\tconst line1 = segments1[ count ];\n\t\t\t\t\tline1.set( p1, p2 );\n\n\n\t\t\t\t\t// get aabb line segments\n\t\t\t\t\tconst f1 = xyzFields[ i ];\n\t\t\t\t\tconst f2 = xyzFields[ nextIndex ];\n\t\t\t\t\tconst f3 = xyzFields[ nextIndex2 ];\n\t\t\t\t\tconst line2 = segments2[ count ];\n\t\t\t\t\tconst start = line2.start;\n\t\t\t\t\tconst end = line2.end;\n\n\t\t\t\t\tstart[ f1 ] = min[ f1 ];\n\t\t\t\t\tstart[ f2 ] = i1 ? min[ f2 ] : max[ f2 ];\n\t\t\t\t\tstart[ f3 ] = i2 ? min[ f3 ] : max[ f2 ];\n\n\t\t\t\t\tend[ f1 ] = max[ f1 ];\n\t\t\t\t\tend[ f2 ] = i1 ? min[ f2 ] : max[ f2 ];\n\t\t\t\t\tend[ f3 ] = i2 ? min[ f3 ] : max[ f2 ];\n\n\t\t\t\t\tcount ++;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t// check all the other boxes point\n\t\tfor ( let x = 0; x <= 1; x ++ ) {\n\n\t\t\tfor ( let y = 0; y <= 1; y ++ ) {\n\n\t\t\t\tfor ( let z = 0; z <= 1; z ++ ) {\n\n\t\t\t\t\tpoint2.x = x ? max.x : min.x;\n\t\t\t\t\tpoint2.y = y ? max.y : min.y;\n\t\t\t\t\tpoint2.z = z ? max.z : min.z;\n\n\t\t\t\t\tthis.closestPointToPoint( point2, point1 );\n\t\t\t\t\tconst dist = point2.distanceToSquared( point1 );\n\t\t\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\t\t\tif ( dist < threshold2 ) return Math.sqrt( dist );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfor ( let i = 0; i < 12; i ++ ) {\n\n\t\t\tconst l1 = segments1[ i ];\n\t\t\tfor ( let i2 = 0; i2 < 12; i2 ++ ) {\n\n\t\t\t\tconst l2 = segments2[ i2 ];\n\t\t\t\tclosestPointsSegmentToSegment( l1, l2, point1, point2 );\n\t\t\t\tconst dist = point1.distanceToSquared( point2 );\n\t\t\t\tif ( dist < closestDistanceSq ) {\n\n\t\t\t\t\tclosestDistanceSq = dist;\n\t\t\t\t\tif ( target1 ) target1.copy( point1 );\n\t\t\t\t\tif ( target2 ) target2.copy( point2 );\n\n\t\t\t\t\tif ( dist < threshold2 ) return Math.sqrt( dist );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn Math.sqrt( closestDistanceSq );\n\n\t};\n\n} )();\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM;IAEZ,YAAa,GAAG,EAAE,GAAG,EAAE,MAAM,CAAG;QAE/B,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,GAAG,GAAG,IAAI,kJAAA,CAAA,UAAO;QACtB,IAAI,CAAC,GAAG,GAAG,IAAI,kJAAA,CAAA,UAAO;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,kJAAA,CAAA,UAAO;QACzB,IAAI,CAAC,SAAS,GAAG,IAAI,kJAAA,CAAA,UAAO;QAC5B,IAAI,CAAC,MAAM,GAAG,IAAI,MAAO,GAAI,IAAI,GAAG,GAAG,CAAE,IAAM,IAAI,kJAAA,CAAA,UAAO;QAC1D,IAAI,CAAC,OAAO,GAAG,IAAI,MAAO,GAAI,IAAI,GAAG,GAAG,CAAE,IAAM,IAAI,kJAAA,CAAA,UAAO;QAC3D,IAAI,CAAC,SAAS,GAAG,IAAI,MAAO,GAAI,IAAI,GAAG,GAAG,CAAE,IAAM,IAAI,8KAAA,CAAA,uBAAoB;QAC1E,IAAI,CAAC,gBAAgB,GAAG,IAAI,MAAO,GAAI,IAAI,GAAG,GAAG,CAAE,IAAM,IAAI,8KAAA,CAAA,uBAAoB;QACjF,IAAI,CAAC,WAAW,GAAG;QAEnB,IAAK,KAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAE;QAC1B,IAAK,KAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAE;QAC1B,IAAK,QAAS,IAAI,CAAC,MAAM,CAAC,IAAI,CAAE;IAEjC;IAEA,IAAK,GAAG,EAAE,GAAG,EAAE,MAAM,EAAG;QAEvB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAE;QACf,IAAI,CAAC,GAAG,CAAC,IAAI,CAAE;QACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAE;QAClB,IAAI,CAAC,WAAW,GAAG;IAEpB;IAEA,KAAM,KAAK,EAAG;QAEb,IAAI,CAAC,GAAG,CAAC,IAAI,CAAE,MAAM,GAAG;QACxB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAE,MAAM,GAAG;QACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAE,MAAM,MAAM;QAC9B,IAAI,CAAC,WAAW,GAAG;IAEpB;AAED;AAEA,YAAY,SAAS,CAAC,MAAM,GAAG,AAAE;IAEhC,OAAO,SAAS;QAEf,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,MAAM,MAAM,IAAI,CAAC,GAAG;QACpB,MAAM,MAAM,IAAI,CAAC,GAAG;QAEpB,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,IAAM,IAAI,IAAI,GAAG,KAAK,GAAG,IAAO;YAE/B,IAAM,IAAI,IAAI,GAAG,KAAK,GAAG,IAAO;gBAE/B,IAAM,IAAI,IAAI,GAAG,KAAK,GAAG,IAAO;oBAE/B,MAAM,IAAI,AAAE,CAAE,KAAK,CAAE,IAAI,IAAQ,CAAE,KAAK,CAAE,IAAI,IAAQ,CAAE,KAAK,CAAE,IAAI;oBACnE,MAAM,IAAI,MAAM,CAAE,EAAG;oBACrB,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC;oBACvB,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC;oBACvB,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC;oBAEvB,EAAE,YAAY,CAAE;gBAEjB;YAED;QAED;QAEA,MAAM,YAAY,IAAI,CAAC,SAAS;QAChC,MAAM,UAAU,IAAI,CAAC,OAAO;QAC5B,MAAM,SAAS,MAAM,CAAE,EAAG;QAC1B,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;YAE9B,MAAM,OAAO,OAAO,CAAE,EAAG;YACzB,MAAM,KAAK,SAAS,CAAE,EAAG;YACzB,MAAM,QAAQ,KAAK;YACnB,MAAM,KAAK,MAAM,CAAE,MAAO;YAE1B,KAAK,UAAU,CAAE,QAAQ;YACzB,GAAG,aAAa,CAAE,MAAM;QAEzB;QAEA,MAAM,mBAAmB,IAAI,CAAC,gBAAgB;QAC9C,gBAAgB,CAAE,EAAG,CAAC,kBAAkB,CAAE,QAAQ;QAClD,gBAAgB,CAAE,EAAG,CAAC,kBAAkB,CAAE,QAAQ;QAClD,gBAAgB,CAAE,EAAG,CAAC,kBAAkB,CAAE,QAAQ;QAElD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAE,IAAI,CAAC,MAAM,EAAG,MAAM;QACzC,IAAI,CAAC,WAAW,GAAG;IAEpB;AAED;AAEA,YAAY,SAAS,CAAC,aAAa,GAAG,AAAE;IAEvC,MAAM,aAAa,IAAI,8KAAA,CAAA,uBAAoB;IAC3C,OAAO,SAAS,cAAe,GAAG;QAEjC,mDAAmD;QACnD,IAAK,IAAI,CAAC,WAAW,EAAG;YAEvB,IAAI,CAAC,MAAM;QAEZ;QAEA,MAAM,MAAM,IAAI,GAAG;QACnB,MAAM,MAAM,IAAI,GAAG;QACnB,MAAM,YAAY,IAAI,CAAC,SAAS;QAChC,MAAM,UAAU,IAAI,CAAC,OAAO;QAC5B,MAAM,mBAAmB,IAAI,CAAC,gBAAgB;QAE9C,WAAW,GAAG,GAAG,IAAI,CAAC;QACtB,WAAW,GAAG,GAAG,IAAI,CAAC;QACtB,IAAK,gBAAgB,CAAE,EAAG,CAAC,WAAW,CAAE,aAAe,OAAO;QAE9D,WAAW,GAAG,GAAG,IAAI,CAAC;QACtB,WAAW,GAAG,GAAG,IAAI,CAAC;QACtB,IAAK,gBAAgB,CAAE,EAAG,CAAC,WAAW,CAAE,aAAe,OAAO;QAE9D,WAAW,GAAG,GAAG,IAAI,CAAC;QACtB,WAAW,GAAG,GAAG,IAAI,CAAC;QACtB,IAAK,gBAAgB,CAAE,EAAG,CAAC,WAAW,CAAE,aAAe,OAAO;QAE9D,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;YAE9B,MAAM,OAAO,OAAO,CAAE,EAAG;YACzB,MAAM,KAAK,SAAS,CAAE,EAAG;YACzB,WAAW,UAAU,CAAE,MAAM;YAC7B,IAAK,GAAG,WAAW,CAAE,aAAe,OAAO;QAE5C;QAEA,OAAO;IAER;AAED;AAEA,YAAY,SAAS,CAAC,kBAAkB,GAAG,AAAE;IAE5C,MAAM,QAAQ,IAAI,0KAAA,CAAA,mBAAgB;IAClC,MAAM,YAAY,IAAI,MAAO;IAC7B,MAAM,kBAAkB,IAAI,8KAAA,CAAA,uBAAoB;IAChD,MAAM,mBAAmB,IAAI,8KAAA,CAAA,uBAAoB;IACjD,MAAM,aAAa,IAAI,kJAAA,CAAA,UAAO;IAC9B,OAAO,SAAS,mBAAoB,QAAQ;QAE3C,IAAK,IAAI,CAAC,WAAW,EAAG;YAEvB,IAAI,CAAC,MAAM;QAEZ;QAEA,IAAK,CAAE,SAAS,kBAAkB,EAAG;YAEpC,MAAM,IAAI,CAAE;YACZ,MAAM,MAAM;YACZ,WAAW;QAEZ,OAAO,IAAK,SAAS,WAAW,EAAG;YAElC,SAAS,MAAM;QAEhB;QAEA,MAAM,YAAY,IAAI,CAAC,SAAS;QAChC,MAAM,UAAU,IAAI,CAAC,OAAO;QAE5B,SAAS,CAAE,EAAG,GAAG,SAAS,CAAC;QAC3B,SAAS,CAAE,EAAG,GAAG,SAAS,CAAC;QAC3B,SAAS,CAAE,EAAG,GAAG,SAAS,CAAC;QAE3B,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;YAE9B,MAAM,KAAK,SAAS,CAAE,EAAG;YACzB,MAAM,KAAK,OAAO,CAAE,EAAG;YACvB,gBAAgB,aAAa,CAAE,IAAI;YACnC,IAAK,GAAG,WAAW,CAAE,kBAAoB,OAAO;QAEjD;QAEA,MAAM,eAAe,SAAS,SAAS;QACvC,MAAM,aAAa,SAAS,OAAO;QACnC,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;YAE9B,MAAM,KAAK,YAAY,CAAE,EAAG;YAC5B,MAAM,KAAK,UAAU,CAAE,EAAG;YAC1B,gBAAgB,aAAa,CAAE,IAAI;YACnC,IAAK,GAAG,WAAW,CAAE,kBAAoB,OAAO;QAEjD;QAEA,qBAAqB;QACrB,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;YAE9B,MAAM,MAAM,OAAO,CAAE,EAAG;YACxB,IAAM,IAAI,KAAK,GAAG,KAAK,GAAG,KAAQ;gBAEjC,MAAM,MAAM,UAAU,CAAE,GAAI;gBAC5B,WAAW,YAAY,CAAE,KAAK;gBAC9B,gBAAgB,aAAa,CAAE,YAAY;gBAC3C,iBAAiB,aAAa,CAAE,YAAY;gBAC5C,IAAK,gBAAgB,WAAW,CAAE,mBAAqB,OAAO;YAE/D;QAED;QAEA,OAAO;IAER;AAED;AAEA,YAAY,SAAS,CAAC,mBAAmB,GAAG,AAAE;IAE7C,OAAO,SAAS,oBAAqB,KAAK,EAAE,OAAO;QAElD,IAAK,IAAI,CAAC,WAAW,EAAG;YAEvB,IAAI,CAAC,MAAM;QAEZ;QAEA,QACE,IAAI,CAAE,OACN,YAAY,CAAE,IAAI,CAAC,SAAS,EAC5B,KAAK,CAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EACzB,YAAY,CAAE,IAAI,CAAC,MAAM;QAE3B,OAAO;IAER;AAED;AAEA,YAAY,SAAS,CAAC,eAAe,GAAG,AAAE;IAEzC,MAAM,SAAS,IAAI,kJAAA,CAAA,UAAO;IAC1B,OAAO,SAAS,gBAAiB,KAAK;QAErC,IAAI,CAAC,mBAAmB,CAAE,OAAO;QACjC,OAAO,MAAM,UAAU,CAAE;IAE1B;AAED;AAEA,YAAY,SAAS,CAAC,aAAa,GAAG,AAAE;IAEvC,MAAM,YAAY;QAAE;QAAK;QAAK;KAAK;IACnC,MAAM,YAAY,IAAI,MAAO,IAAK,IAAI,GAAG,GAAG,CAAE,IAAM,IAAI,kJAAA,CAAA,QAAK;IAC7D,MAAM,YAAY,IAAI,MAAO,IAAK,IAAI,GAAG,GAAG,CAAE,IAAM,IAAI,kJAAA,CAAA,QAAK;IAE7D,MAAM,SAAS,IAAI,kJAAA,CAAA,UAAO;IAC1B,MAAM,SAAS,IAAI,kJAAA,CAAA,UAAO;IAE1B,+CAA+C;IAC/C,OAAO,SAAS,cAAe,GAAG,EAAE,YAAY,CAAC,EAAE,UAAU,IAAI,EAAE,UAAU,IAAI;QAEhF,IAAK,IAAI,CAAC,WAAW,EAAG;YAEvB,IAAI,CAAC,MAAM;QAEZ;QAEA,IAAK,IAAI,CAAC,aAAa,CAAE,MAAQ;YAEhC,IAAK,WAAW,SAAU;gBAEzB,IAAI,SAAS,CAAE;gBACf,IAAI,CAAC,mBAAmB,CAAE,QAAQ;gBAClC,IAAI,mBAAmB,CAAE,QAAQ;gBAEjC,IAAK,SAAU,QAAQ,IAAI,CAAE;gBAC7B,IAAK,SAAU,QAAQ,IAAI,CAAE;YAE9B;YAEA,OAAO;QAER;QAEA,MAAM,aAAa,YAAY;QAC/B,MAAM,MAAM,IAAI,GAAG;QACnB,MAAM,MAAM,IAAI,GAAG;QACnB,MAAM,SAAS,IAAI,CAAC,MAAM;QAG1B,gDAAgD;QAChD,IAAI,oBAAoB;QAExB,8BAA8B;QAC9B,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;YAE9B,MAAM,IAAI,MAAM,CAAE,EAAG;YACrB,OAAO,IAAI,CAAE,GAAI,KAAK,CAAE,KAAK;YAE7B,MAAM,OAAO,EAAE,iBAAiB,CAAE;YAClC,IAAK,OAAO,mBAAoB;gBAE/B,oBAAoB;gBACpB,IAAK,SAAU,QAAQ,IAAI,CAAE;gBAC7B,IAAK,SAAU,QAAQ,IAAI,CAAE;gBAE7B,IAAK,OAAO,YAAa,OAAO,KAAK,IAAI,CAAE;YAE5C;QAED;QAEA,gDAAgD;QAChD,IAAI,QAAQ;QACZ,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;YAE9B,IAAM,IAAI,KAAK,GAAG,MAAM,GAAG,KAAQ;gBAElC,IAAM,IAAI,KAAK,GAAG,MAAM,GAAG,KAAQ;oBAElC,MAAM,YAAY,CAAE,IAAI,CAAE,IAAI;oBAC9B,MAAM,aAAa,CAAE,IAAI,CAAE,IAAI;oBAE/B,wBAAwB;oBACxB,MAAM,QAAQ,MAAM,YAAY,MAAM;oBACtC,MAAM,SAAS,KAAK,IAAI,MAAM,YAAY,MAAM;oBAChD,MAAM,KAAK,MAAM,CAAE,MAAO;oBAC1B,MAAM,KAAK,MAAM,CAAE,OAAQ;oBAC3B,MAAM,QAAQ,SAAS,CAAE,MAAO;oBAChC,MAAM,GAAG,CAAE,IAAI;oBAGf,yBAAyB;oBACzB,MAAM,KAAK,SAAS,CAAE,EAAG;oBACzB,MAAM,KAAK,SAAS,CAAE,UAAW;oBACjC,MAAM,KAAK,SAAS,CAAE,WAAY;oBAClC,MAAM,QAAQ,SAAS,CAAE,MAAO;oBAChC,MAAM,QAAQ,MAAM,KAAK;oBACzB,MAAM,MAAM,MAAM,GAAG;oBAErB,KAAK,CAAE,GAAI,GAAG,GAAG,CAAE,GAAI;oBACvB,KAAK,CAAE,GAAI,GAAG,KAAK,GAAG,CAAE,GAAI,GAAG,GAAG,CAAE,GAAI;oBACxC,KAAK,CAAE,GAAI,GAAG,KAAK,GAAG,CAAE,GAAI,GAAG,GAAG,CAAE,GAAI;oBAExC,GAAG,CAAE,GAAI,GAAG,GAAG,CAAE,GAAI;oBACrB,GAAG,CAAE,GAAI,GAAG,KAAK,GAAG,CAAE,GAAI,GAAG,GAAG,CAAE,GAAI;oBACtC,GAAG,CAAE,GAAI,GAAG,KAAK,GAAG,CAAE,GAAI,GAAG,GAAG,CAAE,GAAI;oBAEtC;gBAED;YAED;QAED;QAEA,kCAAkC;QAClC,IAAM,IAAI,IAAI,GAAG,KAAK,GAAG,IAAO;YAE/B,IAAM,IAAI,IAAI,GAAG,KAAK,GAAG,IAAO;gBAE/B,IAAM,IAAI,IAAI,GAAG,KAAK,GAAG,IAAO;oBAE/B,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC;oBAC5B,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC;oBAC5B,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC;oBAE5B,IAAI,CAAC,mBAAmB,CAAE,QAAQ;oBAClC,MAAM,OAAO,OAAO,iBAAiB,CAAE;oBACvC,IAAK,OAAO,mBAAoB;wBAE/B,oBAAoB;wBACpB,IAAK,SAAU,QAAQ,IAAI,CAAE;wBAC7B,IAAK,SAAU,QAAQ,IAAI,CAAE;wBAE7B,IAAK,OAAO,YAAa,OAAO,KAAK,IAAI,CAAE;oBAE5C;gBAED;YAED;QAED;QAEA,IAAM,IAAI,IAAI,GAAG,IAAI,IAAI,IAAO;YAE/B,MAAM,KAAK,SAAS,CAAE,EAAG;YACzB,IAAM,IAAI,KAAK,GAAG,KAAK,IAAI,KAAQ;gBAElC,MAAM,KAAK,SAAS,CAAE,GAAI;gBAC1B,CAAA,GAAA,uKAAA,CAAA,gCAA6B,AAAD,EAAG,IAAI,IAAI,QAAQ;gBAC/C,MAAM,OAAO,OAAO,iBAAiB,CAAE;gBACvC,IAAK,OAAO,mBAAoB;oBAE/B,oBAAoB;oBACpB,IAAK,SAAU,QAAQ,IAAI,CAAE;oBAC7B,IAAK,SAAU,QAAQ,IAAI,CAAE;oBAE7B,IAAK,OAAO,YAAa,OAAO,KAAK,IAAI,CAAE;gBAE5C;YAED;QAED;QAEA,OAAO,KAAK,IAAI,CAAE;IAEnB;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1809, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/utils/PrimitivePool.js"], "sourcesContent": ["export class PrimitivePool {\n\n\tconstructor( getNewPrimitive ) {\n\n\t\tthis._getNewPrimitive = getNewPrimitive;\n\t\tthis._primitives = [];\n\n\t}\n\n\tgetPrimitive() {\n\n\t\tconst primitives = this._primitives;\n\t\tif ( primitives.length === 0 ) {\n\n\t\t\treturn this._getNewPrimitive();\n\n\t\t} else {\n\n\t\t\treturn primitives.pop();\n\n\t\t}\n\n\t}\n\n\treleasePrimitive( primitive ) {\n\n\t\tthis._primitives.push( primitive );\n\n\t}\n\n}\n"], "names": [], "mappings": ";;;AAAO,MAAM;IAEZ,YAAa,eAAe,CAAG;QAE9B,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,WAAW,GAAG,EAAE;IAEtB;IAEA,eAAe;QAEd,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,IAAK,WAAW,MAAM,KAAK,GAAI;YAE9B,OAAO,IAAI,CAAC,gBAAgB;QAE7B,OAAO;YAEN,OAAO,WAAW,GAAG;QAEtB;IAED;IAEA,iBAAkB,SAAS,EAAG;QAE7B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAE;IAExB;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1835, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/utils/ExtendedTrianglePool.js"], "sourcesContent": ["import { ExtendedTriangle } from '../math/ExtendedTriangle.js';\nimport { PrimitivePool } from './PrimitivePool.js';\n\nclass ExtendedTrianglePoolBase extends PrimitivePool {\n\n\tconstructor() {\n\n\t\tsuper( () => new ExtendedTriangle() );\n\n\t}\n\n}\n\nexport const ExtendedTrianglePool = /* @__PURE__ */ new ExtendedTrianglePoolBase();\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,iCAAiC,wKAAA,CAAA,gBAAa;IAEnD,aAAc;QAEb,KAAK,CAAE,IAAM,IAAI,0KAAA,CAAA,mBAAgB;IAElC;AAED;AAEO,MAAM,uBAAuB,aAAa,GAAG,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1854, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/utils/BufferStack.js"], "sourcesContent": ["class _BufferStack {\n\n\tconstructor() {\n\n\t\tthis.float32Array = null;\n\t\tthis.uint16Array = null;\n\t\tthis.uint32Array = null;\n\n\t\tconst stack = [];\n\t\tlet prevBuffer = null;\n\t\tthis.setBuffer = buffer => {\n\n\t\t\tif ( prevBuffer ) {\n\n\t\t\t\tstack.push( prevBuffer );\n\n\t\t\t}\n\n\t\t\tprevBuffer = buffer;\n\t\t\tthis.float32Array = new Float32Array( buffer );\n\t\t\tthis.uint16Array = new Uint16Array( buffer );\n\t\t\tthis.uint32Array = new Uint32Array( buffer );\n\n\t\t};\n\n\t\tthis.clearBuffer = () => {\n\n\t\t\tprevBuffer = null;\n\t\t\tthis.float32Array = null;\n\t\t\tthis.uint16Array = null;\n\t\t\tthis.uint32Array = null;\n\n\t\t\tif ( stack.length !== 0 ) {\n\n\t\t\t\tthis.setBuffer( stack.pop() );\n\n\t\t\t}\n\n\t\t};\n\n\t}\n\n}\n\nexport const BufferStack = new _BufferStack();\n"], "names": [], "mappings": ";;;AAAA,MAAM;IAEL,aAAc;QAEb,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,WAAW,GAAG;QAEnB,MAAM,QAAQ,EAAE;QAChB,IAAI,aAAa;QACjB,IAAI,CAAC,SAAS,GAAG,CAAA;YAEhB,IAAK,YAAa;gBAEjB,MAAM,IAAI,CAAE;YAEb;YAEA,aAAa;YACb,IAAI,CAAC,YAAY,GAAG,IAAI,aAAc;YACtC,IAAI,CAAC,WAAW,GAAG,IAAI,YAAa;YACpC,IAAI,CAAC,WAAW,GAAG,IAAI,YAAa;QAErC;QAEA,IAAI,CAAC,WAAW,GAAG;YAElB,aAAa;YACb,IAAI,CAAC,YAAY,GAAG;YACpB,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,WAAW,GAAG;YAEnB,IAAK,MAAM,MAAM,KAAK,GAAI;gBAEzB,IAAI,CAAC,SAAS,CAAE,MAAM,GAAG;YAE1B;QAED;IAED;AAED;AAEO,MAAM,cAAc,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1891, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/cast/shapecast.js"], "sourcesContent": ["import { Box3 } from 'three';\nimport { CONTAINED } from '../Constants.js';\nimport { arrayToBox } from '../../utils/ArrayBoxUtilities.js';\nimport { PrimitivePool } from '../../utils/PrimitivePool.js';\nimport { COUNT, OFFSET, LEFT_NODE, RIGHT_NODE, IS_LEAF, BOUNDING_DATA_INDEX } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\n\nlet _box1, _box2;\nconst boxStack = [];\nconst boxPool = /* @__PURE__ */ new PrimitivePool( () => new Box3() );\n\nexport function shapecast( bvh, root, intersectsBounds, intersectsRange, boundsTraverseOrder, byteOffset ) {\n\n\t// setup\n\t_box1 = boxPool.getPrimitive();\n\t_box2 = boxPool.getPrimitive();\n\tboxStack.push( _box1, _box2 );\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\n\tconst result = shapecastTraverse( 0, bvh.geometry, intersectsBounds, intersectsRange, boundsTraverseOrder, byteOffset );\n\n\t// cleanup\n\tBufferStack.clearBuffer();\n\tboxPool.releasePrimitive( _box1 );\n\tboxPool.releasePrimitive( _box2 );\n\tboxStack.pop();\n\tboxStack.pop();\n\n\tconst length = boxStack.length;\n\tif ( length > 0 ) {\n\n\t\t_box2 = boxStack[ length - 1 ];\n\t\t_box1 = boxStack[ length - 2 ];\n\n\t}\n\n\treturn result;\n\n}\n\nfunction shapecastTraverse(\n\tnodeIndex32,\n\tgeometry,\n\tintersectsBoundsFunc,\n\tintersectsRangeFunc,\n\tnodeScoreFunc = null,\n\tnodeIndexByteOffset = 0, // offset for unique node identifier\n\tdepth = 0\n) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\t\tarrayToBox( BOUNDING_DATA_INDEX( nodeIndex32 ), float32Array, _box1 );\n\t\treturn intersectsRangeFunc( offset, count, false, depth, nodeIndexByteOffset + nodeIndex32, _box1 );\n\n\t} else {\n\n\t\tconst left = LEFT_NODE( nodeIndex32 );\n\t\tconst right = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\tlet c1 = left;\n\t\tlet c2 = right;\n\n\t\tlet score1, score2;\n\t\tlet box1, box2;\n\t\tif ( nodeScoreFunc ) {\n\n\t\t\tbox1 = _box1;\n\t\t\tbox2 = _box2;\n\n\t\t\t// bounding data is not offset\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c1 ), float32Array, box1 );\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c2 ), float32Array, box2 );\n\n\t\t\tscore1 = nodeScoreFunc( box1 );\n\t\t\tscore2 = nodeScoreFunc( box2 );\n\n\t\t\tif ( score2 < score1 ) {\n\n\t\t\t\tc1 = right;\n\t\t\t\tc2 = left;\n\n\t\t\t\tconst temp = score1;\n\t\t\t\tscore1 = score2;\n\t\t\t\tscore2 = temp;\n\n\t\t\t\tbox1 = box2;\n\t\t\t\t// box2 is always set before use below\n\n\t\t\t}\n\n\t\t}\n\n\t\t// Check box 1 intersection\n\t\tif ( ! box1 ) {\n\n\t\t\tbox1 = _box1;\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( c1 ), float32Array, box1 );\n\n\t\t}\n\n\t\tconst isC1Leaf = IS_LEAF( c1 * 2, uint16Array );\n\t\tconst c1Intersection = intersectsBoundsFunc( box1, isC1Leaf, score1, depth + 1, nodeIndexByteOffset + c1 );\n\n\t\tlet c1StopTraversal;\n\t\tif ( c1Intersection === CONTAINED ) {\n\n\t\t\tconst offset = getLeftOffset( c1 );\n\t\t\tconst end = getRightEndOffset( c1 );\n\t\t\tconst count = end - offset;\n\n\t\t\tc1StopTraversal = intersectsRangeFunc( offset, count, true, depth + 1, nodeIndexByteOffset + c1, box1 );\n\n\t\t} else {\n\n\t\t\tc1StopTraversal =\n\t\t\t\tc1Intersection &&\n\t\t\t\tshapecastTraverse(\n\t\t\t\t\tc1,\n\t\t\t\t\tgeometry,\n\t\t\t\t\tintersectsBoundsFunc,\n\t\t\t\t\tintersectsRangeFunc,\n\t\t\t\t\tnodeScoreFunc,\n\t\t\t\t\tnodeIndexByteOffset,\n\t\t\t\t\tdepth + 1\n\t\t\t\t);\n\n\t\t}\n\n\t\tif ( c1StopTraversal ) return true;\n\n\t\t// Check box 2 intersection\n\t\t// cached box2 will have been overwritten by previous traversal\n\t\tbox2 = _box2;\n\t\tarrayToBox( BOUNDING_DATA_INDEX( c2 ), float32Array, box2 );\n\n\t\tconst isC2Leaf = IS_LEAF( c2 * 2, uint16Array );\n\t\tconst c2Intersection = intersectsBoundsFunc( box2, isC2Leaf, score2, depth + 1, nodeIndexByteOffset + c2 );\n\n\t\tlet c2StopTraversal;\n\t\tif ( c2Intersection === CONTAINED ) {\n\n\t\t\tconst offset = getLeftOffset( c2 );\n\t\t\tconst end = getRightEndOffset( c2 );\n\t\t\tconst count = end - offset;\n\n\t\t\tc2StopTraversal = intersectsRangeFunc( offset, count, true, depth + 1, nodeIndexByteOffset + c2, box2 );\n\n\t\t} else {\n\n\t\t\tc2StopTraversal =\n\t\t\t\tc2Intersection &&\n\t\t\t\tshapecastTraverse(\n\t\t\t\t\tc2,\n\t\t\t\t\tgeometry,\n\t\t\t\t\tintersectsBoundsFunc,\n\t\t\t\t\tintersectsRangeFunc,\n\t\t\t\t\tnodeScoreFunc,\n\t\t\t\t\tnodeIndexByteOffset,\n\t\t\t\t\tdepth + 1\n\t\t\t\t);\n\n\t\t}\n\n\t\tif ( c2StopTraversal ) return true;\n\n\t\treturn false;\n\n\t\t// Define these inside the function so it has access to the local variables needed\n\t\t// when converting to the buffer equivalents\n\t\tfunction getLeftOffset( nodeIndex32 ) {\n\n\t\t\tconst { uint16Array, uint32Array } = BufferStack;\n\t\t\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t// traverse until we find a leaf\n\t\t\twhile ( ! IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\t\tnodeIndex32 = LEFT_NODE( nodeIndex32 );\n\t\t\t\tnodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t}\n\n\t\t\treturn OFFSET( nodeIndex32, uint32Array );\n\n\t\t}\n\n\t\tfunction getRightEndOffset( nodeIndex32 ) {\n\n\t\t\tconst { uint16Array, uint32Array } = BufferStack;\n\t\t\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t// traverse until we find a leaf\n\t\t\twhile ( ! IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\t\t// adjust offset to point to the right node\n\t\t\t\tnodeIndex32 = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\t\tnodeIndex16 = nodeIndex32 * 2;\n\n\t\t\t}\n\n\t\t\t// return the end offset of the triangle range\n\t\t\treturn OFFSET( nodeIndex32, uint32Array ) + COUNT( nodeIndex16, uint16Array );\n\n\t\t}\n\n\t}\n\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,IAAI,OAAO;AACX,MAAM,WAAW,EAAE;AACnB,MAAM,UAAU,aAAa,GAAG,IAAI,wKAAA,CAAA,gBAAa,CAAE,IAAM,IAAI,kJAAA,CAAA,OAAI;AAE1D,SAAS,UAAW,GAAG,EAAE,IAAI,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,EAAE,UAAU;IAEvG,QAAQ;IACR,QAAQ,QAAQ,YAAY;IAC5B,QAAQ,QAAQ,YAAY;IAC5B,SAAS,IAAI,CAAE,OAAO;IACtB,8KAAA,CAAA,cAAW,CAAC,SAAS,CAAE,IAAI,MAAM,CAAE,KAAM;IAEzC,MAAM,SAAS,kBAAmB,GAAG,IAAI,QAAQ,EAAE,kBAAkB,iBAAiB,qBAAqB;IAE3G,UAAU;IACV,8KAAA,CAAA,cAAW,CAAC,WAAW;IACvB,QAAQ,gBAAgB,CAAE;IAC1B,QAAQ,gBAAgB,CAAE;IAC1B,SAAS,GAAG;IACZ,SAAS,GAAG;IAEZ,MAAM,SAAS,SAAS,MAAM;IAC9B,IAAK,SAAS,GAAI;QAEjB,QAAQ,QAAQ,CAAE,SAAS,EAAG;QAC9B,QAAQ,QAAQ,CAAE,SAAS,EAAG;IAE/B;IAEA,OAAO;AAER;AAEA,SAAS,kBACR,WAAW,EACX,QAAQ,EACR,oBAAoB,EACpB,mBAAmB,EACnB,gBAAgB,IAAI,EACpB,sBAAsB,CAAC,EACvB,QAAQ,CAAC;IAGT,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,8KAAA,CAAA,cAAW;IAC9D,IAAI,cAAc,cAAc;IAEhC,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,UAAO,AAAD,EAAG,aAAa;IACrC,IAAK,QAAS;QAEb,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAG,aAAa;QACpC,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,QAAK,AAAD,EAAG,aAAa;QAClC,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,cAAe,cAAc;QAC9D,OAAO,oBAAqB,QAAQ,OAAO,OAAO,OAAO,sBAAsB,aAAa;IAE7F,OAAO;QAEN,MAAM,OAAO,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAG;QACxB,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAG,aAAa;QACvC,IAAI,KAAK;QACT,IAAI,KAAK;QAET,IAAI,QAAQ;QACZ,IAAI,MAAM;QACV,IAAK,eAAgB;YAEpB,OAAO;YACP,OAAO;YAEP,8BAA8B;YAC9B,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,KAAM,cAAc;YACrD,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,KAAM,cAAc;YAErD,SAAS,cAAe;YACxB,SAAS,cAAe;YAExB,IAAK,SAAS,QAAS;gBAEtB,KAAK;gBACL,KAAK;gBAEL,MAAM,OAAO;gBACb,SAAS;gBACT,SAAS;gBAET,OAAO;YACP,sCAAsC;YAEvC;QAED;QAEA,2BAA2B;QAC3B,IAAK,CAAE,MAAO;YAEb,OAAO;YACP,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,KAAM,cAAc;QAEtD;QAEA,MAAM,WAAW,CAAA,GAAA,kLAAA,CAAA,UAAO,AAAD,EAAG,KAAK,GAAG;QAClC,MAAM,iBAAiB,qBAAsB,MAAM,UAAU,QAAQ,QAAQ,GAAG,sBAAsB;QAEtG,IAAI;QACJ,IAAK,mBAAmB,mKAAA,CAAA,YAAS,EAAG;YAEnC,MAAM,SAAS,cAAe;YAC9B,MAAM,MAAM,kBAAmB;YAC/B,MAAM,QAAQ,MAAM;YAEpB,kBAAkB,oBAAqB,QAAQ,OAAO,MAAM,QAAQ,GAAG,sBAAsB,IAAI;QAElG,OAAO;YAEN,kBACC,kBACA,kBACC,IACA,UACA,sBACA,qBACA,eACA,qBACA,QAAQ;QAGX;QAEA,IAAK,iBAAkB,OAAO;QAE9B,2BAA2B;QAC3B,+DAA+D;QAC/D,OAAO;QACP,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,KAAM,cAAc;QAErD,MAAM,WAAW,CAAA,GAAA,kLAAA,CAAA,UAAO,AAAD,EAAG,KAAK,GAAG;QAClC,MAAM,iBAAiB,qBAAsB,MAAM,UAAU,QAAQ,QAAQ,GAAG,sBAAsB;QAEtG,IAAI;QACJ,IAAK,mBAAmB,mKAAA,CAAA,YAAS,EAAG;YAEnC,MAAM,SAAS,cAAe;YAC9B,MAAM,MAAM,kBAAmB;YAC/B,MAAM,QAAQ,MAAM;YAEpB,kBAAkB,oBAAqB,QAAQ,OAAO,MAAM,QAAQ,GAAG,sBAAsB,IAAI;QAElG,OAAO;YAEN,kBACC,kBACA,kBACC,IACA,UACA,sBACA,qBACA,eACA,qBACA,QAAQ;QAGX;QAEA,IAAK,iBAAkB,OAAO;QAE9B,OAAO;;QAEP,kFAAkF;QAClF,4CAA4C;QAC5C,SAAS,cAAe,WAAW;YAElC,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,8KAAA,CAAA,cAAW;YAChD,IAAI,cAAc,cAAc;YAEhC,gCAAgC;YAChC,MAAQ,CAAE,CAAA,GAAA,kLAAA,CAAA,UAAO,AAAD,EAAG,aAAa,aAAgB;gBAE/C,cAAc,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAG;gBACzB,cAAc,cAAc;YAE7B;YAEA,OAAO,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAG,aAAa;QAE7B;QAEA,SAAS,kBAAmB,WAAW;YAEtC,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,8KAAA,CAAA,cAAW;YAChD,IAAI,cAAc,cAAc;YAEhC,gCAAgC;YAChC,MAAQ,CAAE,CAAA,GAAA,kLAAA,CAAA,UAAO,AAAD,EAAG,aAAa,aAAgB;gBAE/C,2CAA2C;gBAC3C,cAAc,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAG,aAAa;gBACvC,cAAc,cAAc;YAE7B;YAEA,8CAA8C;YAC9C,OAAO,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAG,aAAa,eAAgB,CAAA,GAAA,kLAAA,CAAA,QAAK,AAAD,EAAG,aAAa;QAEjE;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2030, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/cast/closestPointToPoint.js"], "sourcesContent": ["import { Vector3 } from 'three';\n\nconst temp = /* @__PURE__ */ new Vector3();\nconst temp1 = /* @__PURE__ */ new Vector3();\n\nexport function closestPointToPoint(\n\tbvh,\n\tpoint,\n\ttarget = { },\n\tminThreshold = 0,\n\tmaxThreshold = Infinity,\n) {\n\n\t// early out if under minThreshold\n\t// skip checking if over maxThreshold\n\t// set minThreshold = maxThreshold to quickly check if a point is within a threshold\n\t// returns Infinity if no value found\n\tconst minThresholdSq = minThreshold * minThreshold;\n\tconst maxThresholdSq = maxThreshold * maxThreshold;\n\tlet closestDistanceSq = Infinity;\n\tlet closestDistanceTriIndex = null;\n\tbvh.shapecast(\n\n\t\t{\n\n\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\ttemp.copy( point ).clamp( box.min, box.max );\n\t\t\t\treturn temp.distanceToSquared( point );\n\n\t\t\t},\n\n\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\treturn score < closestDistanceSq && score < maxThresholdSq;\n\n\t\t\t},\n\n\t\t\tintersectsTriangle: ( tri, triIndex ) => {\n\n\t\t\t\ttri.closestPointToPoint( point, temp );\n\t\t\t\tconst distSq = point.distanceToSquared( temp );\n\t\t\t\tif ( distSq < closestDistanceSq ) {\n\n\t\t\t\t\ttemp1.copy( temp );\n\t\t\t\t\tclosestDistanceSq = distSq;\n\t\t\t\t\tclosestDistanceTriIndex = triIndex;\n\n\t\t\t\t}\n\n\t\t\t\tif ( distSq < minThresholdSq ) {\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t} else {\n\n\t\t\t\t\treturn false;\n\n\t\t\t\t}\n\n\t\t\t},\n\n\t\t}\n\n\t);\n\n\tif ( closestDistanceSq === Infinity ) return null;\n\n\tconst closestDistance = Math.sqrt( closestDistanceSq );\n\n\tif ( ! target.point ) target.point = temp1.clone();\n\telse target.point.copy( temp1 );\n\ttarget.distance = closestDistance,\n\ttarget.faceIndex = closestDistanceTriIndex;\n\n\treturn target;\n\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,OAAO,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AACxC,MAAM,QAAQ,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAElC,SAAS,oBACf,GAAG,EACH,KAAK,EACL,SAAS,CAAE,CAAC,EACZ,eAAe,CAAC,EAChB,eAAe,QAAQ;IAGvB,kCAAkC;IAClC,qCAAqC;IACrC,oFAAoF;IACpF,qCAAqC;IACrC,MAAM,iBAAiB,eAAe;IACtC,MAAM,iBAAiB,eAAe;IACtC,IAAI,oBAAoB;IACxB,IAAI,0BAA0B;IAC9B,IAAI,SAAS,CAEZ;QAEC,qBAAqB,CAAA;YAEpB,KAAK,IAAI,CAAE,OAAQ,KAAK,CAAE,IAAI,GAAG,EAAE,IAAI,GAAG;YAC1C,OAAO,KAAK,iBAAiB,CAAE;QAEhC;QAEA,kBAAkB,CAAE,KAAK,QAAQ;YAEhC,OAAO,QAAQ,qBAAqB,QAAQ;QAE7C;QAEA,oBAAoB,CAAE,KAAK;YAE1B,IAAI,mBAAmB,CAAE,OAAO;YAChC,MAAM,SAAS,MAAM,iBAAiB,CAAE;YACxC,IAAK,SAAS,mBAAoB;gBAEjC,MAAM,IAAI,CAAE;gBACZ,oBAAoB;gBACpB,0BAA0B;YAE3B;YAEA,IAAK,SAAS,gBAAiB;gBAE9B,OAAO;YAER,OAAO;gBAEN,OAAO;YAER;QAED;IAED;IAID,IAAK,sBAAsB,UAAW,OAAO;IAE7C,MAAM,kBAAkB,KAAK,IAAI,CAAE;IAEnC,IAAK,CAAE,OAAO,KAAK,EAAG,OAAO,KAAK,GAAG,MAAM,KAAK;SAC3C,OAAO,KAAK,CAAC,IAAI,CAAE;IACxB,OAAO,QAAQ,GAAG,iBAClB,OAAO,SAAS,GAAG;IAEnB,OAAO;AAER", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2082, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/utils/ThreeRayIntersectUtilities.js"], "sourcesContent": ["import { Vector3, Vector2, <PERSON>, DoubleSide, BackSide, REVISION } from 'three';\n\nconst IS_GT_REVISION_169 = parseInt( REVISION ) >= 169;\n\n// Ripped and modified From THREE.js Mesh raycast\n// https://github.com/mrdoob/three.js/blob/0aa87c999fe61e216c1133fba7a95772b503eddf/src/objects/Mesh.js#L115\nconst _vA = /* @__PURE__ */ new Vector3();\nconst _vB = /* @__PURE__ */ new Vector3();\nconst _vC = /* @__PURE__ */ new Vector3();\n\nconst _uvA = /* @__PURE__ */ new Vector2();\nconst _uvB = /* @__PURE__ */ new Vector2();\nconst _uvC = /* @__PURE__ */ new Vector2();\n\nconst _normalA = /* @__PURE__ */ new Vector3();\nconst _normalB = /* @__PURE__ */ new Vector3();\nconst _normalC = /* @__PURE__ */ new Vector3();\n\nconst _intersectionPoint = /* @__PURE__ */ new Vector3();\nfunction checkIntersection( ray, pA, pB, pC, point, side, near, far ) {\n\n\tlet intersect;\n\tif ( side === BackSide ) {\n\n\t\tintersect = ray.intersectTriangle( pC, pB, pA, true, point );\n\n\t} else {\n\n\t\tintersect = ray.intersectTriangle( pA, pB, pC, side !== DoubleSide, point );\n\n\t}\n\n\tif ( intersect === null ) return null;\n\n\tconst distance = ray.origin.distanceTo( point );\n\n\tif ( distance < near || distance > far ) return null;\n\n\treturn {\n\n\t\tdistance: distance,\n\t\tpoint: point.clone(),\n\n\t};\n\n}\n\nfunction checkBufferGeometryIntersection( ray, position, normal, uv, uv1, a, b, c, side, near, far ) {\n\n\t_vA.fromBufferAttribute( position, a );\n\t_vB.fromBufferAttribute( position, b );\n\t_vC.fromBufferAttribute( position, c );\n\n\tconst intersection = checkIntersection( ray, _vA, _vB, _vC, _intersectionPoint, side, near, far );\n\n\tif ( intersection ) {\n\n\t\tconst barycoord = new Vector3();\n\t\tTriangle.getBarycoord( _intersectionPoint, _vA, _vB, _vC, barycoord );\n\n\t\tif ( uv ) {\n\n\t\t\t_uvA.fromBufferAttribute( uv, a );\n\t\t\t_uvB.fromBufferAttribute( uv, b );\n\t\t\t_uvC.fromBufferAttribute( uv, c );\n\n\t\t\tintersection.uv = Triangle.getInterpolation( _intersectionPoint, _vA, _vB, _vC, _uvA, _uvB, _uvC, new Vector2() );\n\n\t\t}\n\n\t\tif ( uv1 ) {\n\n\t\t\t_uvA.fromBufferAttribute( uv1, a );\n\t\t\t_uvB.fromBufferAttribute( uv1, b );\n\t\t\t_uvC.fromBufferAttribute( uv1, c );\n\n\t\t\tintersection.uv1 = Triangle.getInterpolation( _intersectionPoint, _vA, _vB, _vC, _uvA, _uvB, _uvC, new Vector2() );\n\n\t\t}\n\n\t\tif ( normal ) {\n\n\t\t\t_normalA.fromBufferAttribute( normal, a );\n\t\t\t_normalB.fromBufferAttribute( normal, b );\n\t\t\t_normalC.fromBufferAttribute( normal, c );\n\n\t\t\tintersection.normal = Triangle.getInterpolation( _intersectionPoint, _vA, _vB, _vC, _normalA, _normalB, _normalC, new Vector3() );\n\t\t\tif ( intersection.normal.dot( ray.direction ) > 0 ) {\n\n\t\t\t\tintersection.normal.multiplyScalar( - 1 );\n\n\t\t\t}\n\n\t\t}\n\n\t\tconst face = {\n\t\t\ta: a,\n\t\t\tb: b,\n\t\t\tc: c,\n\t\t\tnormal: new Vector3(),\n\t\t\tmaterialIndex: 0\n\t\t};\n\n\t\tTriangle.getNormal( _vA, _vB, _vC, face.normal );\n\n\t\tintersection.face = face;\n\t\tintersection.faceIndex = a;\n\n\t\tif ( IS_GT_REVISION_169 ) {\n\n\t\t\tintersection.barycoord = barycoord;\n\n\t\t}\n\n\t}\n\n\treturn intersection;\n\n}\n\n// https://github.com/mrdoob/three.js/blob/0aa87c999fe61e216c1133fba7a95772b503eddf/src/objects/Mesh.js#L258\nfunction intersectTri( geo, side, ray, tri, intersections, near, far ) {\n\n\tconst triOffset = tri * 3;\n\tlet a = triOffset + 0;\n\tlet b = triOffset + 1;\n\tlet c = triOffset + 2;\n\n\tconst index = geo.index;\n\tif ( geo.index ) {\n\n\t\ta = index.getX( a );\n\t\tb = index.getX( b );\n\t\tc = index.getX( c );\n\n\t}\n\n\tconst { position, normal, uv, uv1 } = geo.attributes;\n\tconst intersection = checkBufferGeometryIntersection( ray, position, normal, uv, uv1, a, b, c, side, near, far );\n\n\tif ( intersection ) {\n\n\t\tintersection.faceIndex = tri;\n\t\tif ( intersections ) intersections.push( intersection );\n\t\treturn intersection;\n\n\t}\n\n\treturn null;\n\n}\n\nexport { intersectTri };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,qBAAqB,SAAU,kJAAA,CAAA,WAAQ,KAAM;AAEnD,iDAAiD;AACjD,4GAA4G;AAC5G,MAAM,MAAM,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AACvC,MAAM,MAAM,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AACvC,MAAM,MAAM,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAEvC,MAAM,OAAO,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AACxC,MAAM,OAAO,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AACxC,MAAM,OAAO,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAExC,MAAM,WAAW,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAC5C,MAAM,WAAW,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAC5C,MAAM,WAAW,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAE5C,MAAM,qBAAqB,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AACtD,SAAS,kBAAmB,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG;IAElE,IAAI;IACJ,IAAK,SAAS,kJAAA,CAAA,WAAQ,EAAG;QAExB,YAAY,IAAI,iBAAiB,CAAE,IAAI,IAAI,IAAI,MAAM;IAEtD,OAAO;QAEN,YAAY,IAAI,iBAAiB,CAAE,IAAI,IAAI,IAAI,SAAS,kJAAA,CAAA,aAAU,EAAE;IAErE;IAEA,IAAK,cAAc,MAAO,OAAO;IAEjC,MAAM,WAAW,IAAI,MAAM,CAAC,UAAU,CAAE;IAExC,IAAK,WAAW,QAAQ,WAAW,KAAM,OAAO;IAEhD,OAAO;QAEN,UAAU;QACV,OAAO,MAAM,KAAK;IAEnB;AAED;AAEA,SAAS,gCAAiC,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG;IAEjG,IAAI,mBAAmB,CAAE,UAAU;IACnC,IAAI,mBAAmB,CAAE,UAAU;IACnC,IAAI,mBAAmB,CAAE,UAAU;IAEnC,MAAM,eAAe,kBAAmB,KAAK,KAAK,KAAK,KAAK,oBAAoB,MAAM,MAAM;IAE5F,IAAK,cAAe;QAEnB,MAAM,YAAY,IAAI,kJAAA,CAAA,UAAO;QAC7B,kJAAA,CAAA,WAAQ,CAAC,YAAY,CAAE,oBAAoB,KAAK,KAAK,KAAK;QAE1D,IAAK,IAAK;YAET,KAAK,mBAAmB,CAAE,IAAI;YAC9B,KAAK,mBAAmB,CAAE,IAAI;YAC9B,KAAK,mBAAmB,CAAE,IAAI;YAE9B,aAAa,EAAE,GAAG,kJAAA,CAAA,WAAQ,CAAC,gBAAgB,CAAE,oBAAoB,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,IAAI,kJAAA,CAAA,UAAO;QAE9G;QAEA,IAAK,KAAM;YAEV,KAAK,mBAAmB,CAAE,KAAK;YAC/B,KAAK,mBAAmB,CAAE,KAAK;YAC/B,KAAK,mBAAmB,CAAE,KAAK;YAE/B,aAAa,GAAG,GAAG,kJAAA,CAAA,WAAQ,CAAC,gBAAgB,CAAE,oBAAoB,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,IAAI,kJAAA,CAAA,UAAO;QAE/G;QAEA,IAAK,QAAS;YAEb,SAAS,mBAAmB,CAAE,QAAQ;YACtC,SAAS,mBAAmB,CAAE,QAAQ;YACtC,SAAS,mBAAmB,CAAE,QAAQ;YAEtC,aAAa,MAAM,GAAG,kJAAA,CAAA,WAAQ,CAAC,gBAAgB,CAAE,oBAAoB,KAAK,KAAK,KAAK,UAAU,UAAU,UAAU,IAAI,kJAAA,CAAA,UAAO;YAC7H,IAAK,aAAa,MAAM,CAAC,GAAG,CAAE,IAAI,SAAS,IAAK,GAAI;gBAEnD,aAAa,MAAM,CAAC,cAAc,CAAE,CAAE;YAEvC;QAED;QAEA,MAAM,OAAO;YACZ,GAAG;YACH,GAAG;YACH,GAAG;YACH,QAAQ,IAAI,kJAAA,CAAA,UAAO;YACnB,eAAe;QAChB;QAEA,kJAAA,CAAA,WAAQ,CAAC,SAAS,CAAE,KAAK,KAAK,KAAK,KAAK,MAAM;QAE9C,aAAa,IAAI,GAAG;QACpB,aAAa,SAAS,GAAG;QAEzB,IAAK,oBAAqB;YAEzB,aAAa,SAAS,GAAG;QAE1B;IAED;IAEA,OAAO;AAER;AAEA,4GAA4G;AAC5G,SAAS,aAAc,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG;IAEnE,MAAM,YAAY,MAAM;IACxB,IAAI,IAAI,YAAY;IACpB,IAAI,IAAI,YAAY;IACpB,IAAI,IAAI,YAAY;IAEpB,MAAM,QAAQ,IAAI,KAAK;IACvB,IAAK,IAAI,KAAK,EAAG;QAEhB,IAAI,MAAM,IAAI,CAAE;QAChB,IAAI,MAAM,IAAI,CAAE;QAChB,IAAI,MAAM,IAAI,CAAE;IAEjB;IAEA,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,UAAU;IACpD,MAAM,eAAe,gCAAiC,KAAK,UAAU,QAAQ,IAAI,KAAK,GAAG,GAAG,GAAG,MAAM,MAAM;IAE3G,IAAK,cAAe;QAEnB,aAAa,SAAS,GAAG;QACzB,IAAK,eAAgB,cAAc,IAAI,CAAE;QACzC,OAAO;IAER;IAEA,OAAO;AAER", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/utils/TriangleUtilities.js"], "sourcesContent": ["\nimport { Vector2, Vector3, <PERSON> } from 'three';\n\n// sets the vertices of triangle `tri` with the 3 vertices after i\nexport function setTriangle( tri, i, index, pos ) {\n\n\tconst ta = tri.a;\n\tconst tb = tri.b;\n\tconst tc = tri.c;\n\n\tlet i0 = i;\n\tlet i1 = i + 1;\n\tlet i2 = i + 2;\n\tif ( index ) {\n\n\t\ti0 = index.getX( i0 );\n\t\ti1 = index.getX( i1 );\n\t\ti2 = index.getX( i2 );\n\n\t}\n\n\tta.x = pos.getX( i0 );\n\tta.y = pos.getY( i0 );\n\tta.z = pos.getZ( i0 );\n\n\ttb.x = pos.getX( i1 );\n\ttb.y = pos.getY( i1 );\n\ttb.z = pos.getZ( i1 );\n\n\ttc.x = pos.getX( i2 );\n\ttc.y = pos.getY( i2 );\n\ttc.z = pos.getZ( i2 );\n\n}\n\nconst tempV1 = /* @__PURE__ */ new Vector3();\nconst tempV2 = /* @__PURE__ */ new Vector3();\nconst tempV3 = /* @__PURE__ */ new Vector3();\nconst tempUV1 = /* @__PURE__ */ new Vector2();\nconst tempUV2 = /* @__PURE__ */ new Vector2();\nconst tempUV3 = /* @__PURE__ */ new Vector2();\n\nexport function getTriangleHitPointInfo( point, geometry, triangleIndex, target ) {\n\n\tconst indices = geometry.getIndex().array;\n\tconst positions = geometry.getAttribute( 'position' );\n\tconst uvs = geometry.getAttribute( 'uv' );\n\n\tconst a = indices[ triangleIndex * 3 ];\n\tconst b = indices[ triangleIndex * 3 + 1 ];\n\tconst c = indices[ triangleIndex * 3 + 2 ];\n\n\ttempV1.fromBufferAttribute( positions, a );\n\ttempV2.fromBufferAttribute( positions, b );\n\ttempV3.fromBufferAttribute( positions, c );\n\n\t// find the associated material index\n\tlet materialIndex = 0;\n\tconst groups = geometry.groups;\n\tconst firstVertexIndex = triangleIndex * 3;\n\tfor ( let i = 0, l = groups.length; i < l; i ++ ) {\n\n\t\tconst group = groups[ i ];\n\t\tconst { start, count } = group;\n\t\tif ( firstVertexIndex >= start && firstVertexIndex < start + count ) {\n\n\t\t\tmaterialIndex = group.materialIndex;\n\t\t\tbreak;\n\n\t\t}\n\n\t}\n\n\t// extract barycoord\n\tconst barycoord = target && target.barycoord ? target.barycoord : new Vector3();\n\tTriangle.getBarycoord( point, tempV1, tempV2, tempV3, barycoord );\n\n\t// extract uvs\n\tlet uv = null;\n\tif ( uvs ) {\n\n\t\ttempUV1.fromBufferAttribute( uvs, a );\n\t\ttempUV2.fromBufferAttribute( uvs, b );\n\t\ttempUV3.fromBufferAttribute( uvs, c );\n\n\t\tif ( target && target.uv ) uv = target.uv;\n\t\telse uv = new Vector2();\n\n\t\tTriangle.getInterpolation( point, tempV1, tempV2, tempV3, tempUV1, tempUV2, tempUV3, uv );\n\n\t}\n\n\t// adjust the provided target or create a new one\n\tif ( target ) {\n\n\t\tif ( ! target.face ) target.face = { };\n\t\ttarget.face.a = a;\n\t\ttarget.face.b = b;\n\t\ttarget.face.c = c;\n\t\ttarget.face.materialIndex = materialIndex;\n\t\tif ( ! target.face.normal ) target.face.normal = new Vector3();\n\t\tTriangle.getNormal( tempV1, tempV2, tempV3, target.face.normal );\n\n\t\tif ( uv ) target.uv = uv;\n\t\ttarget.barycoord = barycoord;\n\n\t\treturn target;\n\n\t} else {\n\n\t\treturn {\n\t\t\tface: {\n\t\t\t\ta: a,\n\t\t\t\tb: b,\n\t\t\t\tc: c,\n\t\t\t\tmaterialIndex: materialIndex,\n\t\t\t\tnormal: Triangle.getNormal( tempV1, tempV2, tempV3, new Vector3() )\n\t\t\t},\n\t\t\tuv: uv,\n\t\t\tbarycoord: barycoord,\n\t\t};\n\n\t}\n\n}\n"], "names": [], "mappings": ";;;;AACA;;AAGO,SAAS,YAAa,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG;IAE9C,MAAM,KAAK,IAAI,CAAC;IAChB,MAAM,KAAK,IAAI,CAAC;IAChB,MAAM,KAAK,IAAI,CAAC;IAEhB,IAAI,KAAK;IACT,IAAI,KAAK,IAAI;IACb,IAAI,KAAK,IAAI;IACb,IAAK,OAAQ;QAEZ,KAAK,MAAM,IAAI,CAAE;QACjB,KAAK,MAAM,IAAI,CAAE;QACjB,KAAK,MAAM,IAAI,CAAE;IAElB;IAEA,GAAG,CAAC,GAAG,IAAI,IAAI,CAAE;IACjB,GAAG,CAAC,GAAG,IAAI,IAAI,CAAE;IACjB,GAAG,CAAC,GAAG,IAAI,IAAI,CAAE;IAEjB,GAAG,CAAC,GAAG,IAAI,IAAI,CAAE;IACjB,GAAG,CAAC,GAAG,IAAI,IAAI,CAAE;IACjB,GAAG,CAAC,GAAG,IAAI,IAAI,CAAE;IAEjB,GAAG,CAAC,GAAG,IAAI,IAAI,CAAE;IACjB,GAAG,CAAC,GAAG,IAAI,IAAI,CAAE;IACjB,GAAG,CAAC,GAAG,IAAI,IAAI,CAAE;AAElB;AAEA,MAAM,SAAS,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAC1C,MAAM,SAAS,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAC1C,MAAM,SAAS,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAC1C,MAAM,UAAU,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAC3C,MAAM,UAAU,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAC3C,MAAM,UAAU,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAEpC,SAAS,wBAAyB,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM;IAE9E,MAAM,UAAU,SAAS,QAAQ,GAAG,KAAK;IACzC,MAAM,YAAY,SAAS,YAAY,CAAE;IACzC,MAAM,MAAM,SAAS,YAAY,CAAE;IAEnC,MAAM,IAAI,OAAO,CAAE,gBAAgB,EAAG;IACtC,MAAM,IAAI,OAAO,CAAE,gBAAgB,IAAI,EAAG;IAC1C,MAAM,IAAI,OAAO,CAAE,gBAAgB,IAAI,EAAG;IAE1C,OAAO,mBAAmB,CAAE,WAAW;IACvC,OAAO,mBAAmB,CAAE,WAAW;IACvC,OAAO,mBAAmB,CAAE,WAAW;IAEvC,qCAAqC;IACrC,IAAI,gBAAgB;IACpB,MAAM,SAAS,SAAS,MAAM;IAC9B,MAAM,mBAAmB,gBAAgB;IACzC,IAAM,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAI,GAAG,IAAO;QAEjD,MAAM,QAAQ,MAAM,CAAE,EAAG;QACzB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG;QACzB,IAAK,oBAAoB,SAAS,mBAAmB,QAAQ,OAAQ;YAEpE,gBAAgB,MAAM,aAAa;YACnC;QAED;IAED;IAEA,oBAAoB;IACpB,MAAM,YAAY,UAAU,OAAO,SAAS,GAAG,OAAO,SAAS,GAAG,IAAI,kJAAA,CAAA,UAAO;IAC7E,kJAAA,CAAA,WAAQ,CAAC,YAAY,CAAE,OAAO,QAAQ,QAAQ,QAAQ;IAEtD,cAAc;IACd,IAAI,KAAK;IACT,IAAK,KAAM;QAEV,QAAQ,mBAAmB,CAAE,KAAK;QAClC,QAAQ,mBAAmB,CAAE,KAAK;QAClC,QAAQ,mBAAmB,CAAE,KAAK;QAElC,IAAK,UAAU,OAAO,EAAE,EAAG,KAAK,OAAO,EAAE;aACpC,KAAK,IAAI,kJAAA,CAAA,UAAO;QAErB,kJAAA,CAAA,WAAQ,CAAC,gBAAgB,CAAE,OAAO,QAAQ,QAAQ,QAAQ,SAAS,SAAS,SAAS;IAEtF;IAEA,iDAAiD;IACjD,IAAK,QAAS;QAEb,IAAK,CAAE,OAAO,IAAI,EAAG,OAAO,IAAI,GAAG,CAAE;QACrC,OAAO,IAAI,CAAC,CAAC,GAAG;QAChB,OAAO,IAAI,CAAC,CAAC,GAAG;QAChB,OAAO,IAAI,CAAC,CAAC,GAAG;QAChB,OAAO,IAAI,CAAC,aAAa,GAAG;QAC5B,IAAK,CAAE,OAAO,IAAI,CAAC,MAAM,EAAG,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,kJAAA,CAAA,UAAO;QAC5D,kJAAA,CAAA,WAAQ,CAAC,SAAS,CAAE,QAAQ,QAAQ,QAAQ,OAAO,IAAI,CAAC,MAAM;QAE9D,IAAK,IAAK,OAAO,EAAE,GAAG;QACtB,OAAO,SAAS,GAAG;QAEnB,OAAO;IAER,OAAO;QAEN,OAAO;YACN,MAAM;gBACL,GAAG;gBACH,GAAG;gBACH,GAAG;gBACH,eAAe;gBACf,QAAQ,kJAAA,CAAA,WAAQ,CAAC,SAAS,CAAE,QAAQ,QAAQ,QAAQ,IAAI,kJAAA,CAAA,UAAO;YAChE;YACA,IAAI;YACJ,WAAW;QACZ;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2289, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/utils/iterationUtils.generated.js"], "sourcesContent": ["import { intersectTri } from '../../utils/ThreeRayIntersectUtilities.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\n\n/*************************************************************/\n/* This file is generated from \"iterationUtils.template.js\". */\n/*************************************************************/\n/* eslint-disable indent */\n\nfunction intersectTris( bvh, side, ray, offset, count, intersections, near, far ) {\n\n\tconst { geometry, _indirectBuffer } = bvh;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\n\t\tintersectTri( geometry, side, ray, i, intersections, near, far );\n\n\n\t}\n\n}\n\nfunction intersectClosestTri( bvh, side, ray, offset, count, near, far ) {\n\n\tconst { geometry, _indirectBuffer } = bvh;\n\tlet dist = Infinity;\n\tlet res = null;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tlet intersection;\n\n\t\tintersection = intersectTri( geometry, side, ray, i, null, near, far );\n\n\n\t\tif ( intersection && intersection.distance < dist ) {\n\n\t\t\tres = intersection;\n\t\t\tdist = intersection.distance;\n\n\t\t}\n\n\t}\n\n\treturn res;\n\n}\n\nfunction iterateOverTriangles(\n\toffset,\n\tcount,\n\tbvh,\n\tintersectsTriangleFunc,\n\tcontained,\n\tdepth,\n\ttriangle\n) {\n\n\tconst { geometry } = bvh;\n\tconst { index } = geometry;\n\tconst pos = geometry.attributes.position;\n\tfor ( let i = offset, l = count + offset; i < l; i ++ ) {\n\n\t\tlet tri;\n\n\t\ttri = i;\n\n\t\tsetTriangle( triangle, tri * 3, index, pos );\n\t\ttriangle.needsUpdate = true;\n\n\t\tif ( intersectsTriangleFunc( triangle, tri, contained, depth ) ) {\n\n\t\t\treturn true;\n\n\t\t}\n\n\t}\n\n\treturn false;\n\n}\n\nexport { intersectClosestTri, intersectTris, iterateOverTriangles };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEA,6DAA6D,GAC7D,6DAA6D,GAC7D,6DAA6D,GAC7D,yBAAyB,GAEzB,SAAS,cAAe,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG;IAE9E,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;IACtC,IAAM,IAAI,IAAI,QAAQ,MAAM,SAAS,OAAO,IAAI,KAAK,IAAO;QAG3D,CAAA,GAAA,qLAAA,CAAA,eAAY,AAAD,EAAG,UAAU,MAAM,KAAK,GAAG,eAAe,MAAM;IAG5D;AAED;AAEA,SAAS,oBAAqB,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;IAErE,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;IACtC,IAAI,OAAO;IACX,IAAI,MAAM;IACV,IAAM,IAAI,IAAI,QAAQ,MAAM,SAAS,OAAO,IAAI,KAAK,IAAO;QAE3D,IAAI;QAEJ,eAAe,CAAA,GAAA,qLAAA,CAAA,eAAY,AAAD,EAAG,UAAU,MAAM,KAAK,GAAG,MAAM,MAAM;QAGjE,IAAK,gBAAgB,aAAa,QAAQ,GAAG,MAAO;YAEnD,MAAM;YACN,OAAO,aAAa,QAAQ;QAE7B;IAED;IAEA,OAAO;AAER;AAEA,SAAS,qBACR,MAAM,EACN,KAAK,EACL,GAAG,EACH,sBAAsB,EACtB,SAAS,EACT,KAAK,EACL,QAAQ;IAGR,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,MAAM,MAAM,SAAS,UAAU,CAAC,QAAQ;IACxC,IAAM,IAAI,IAAI,QAAQ,IAAI,QAAQ,QAAQ,IAAI,GAAG,IAAO;QAEvD,IAAI;QAEJ,MAAM;QAEN,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,UAAU,MAAM,GAAG,OAAO;QACvC,SAAS,WAAW,GAAG;QAEvB,IAAK,uBAAwB,UAAU,KAAK,WAAW,QAAU;YAEhE,OAAO;QAER;IAED;IAEA,OAAO;AAER", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/cast/refit.generated.js"], "sourcesContent": ["import { IS_LEAFNODE_FLAG } from '../Constants.js';\n\n/****************************************************/\n/* This file is generated from \"refit.template.js\". */\n/****************************************************/\n\nfunction refit( bvh, nodeIndices = null ) {\n\n\tif ( nodeIndices && Array.isArray( nodeIndices ) ) {\n\n\t\tnodeIndices = new Set( nodeIndices );\n\n\t}\n\n\tconst geometry = bvh.geometry;\n\tconst indexArr = geometry.index ? geometry.index.array : null;\n\tconst posAttr = geometry.attributes.position;\n\n\tlet buffer, uint32Array, uint16Array, float32Array;\n\tlet byteOffset = 0;\n\tconst roots = bvh._roots;\n\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\tbuffer = roots[ i ];\n\t\tuint32Array = new Uint32Array( buffer );\n\t\tuint16Array = new Uint16Array( buffer );\n\t\tfloat32Array = new Float32Array( buffer );\n\n\t\t_traverse( 0, byteOffset );\n\t\tbyteOffset += buffer.byteLength;\n\n\t}\n\n\tfunction _traverse( node32Index, byteOffset, force = false ) {\n\n\t\tconst node16Index = node32Index * 2;\n\t\tconst isLeaf = uint16Array[ node16Index + 15 ] === IS_LEAFNODE_FLAG;\n\t\tif ( isLeaf ) {\n\n\t\t\tconst offset = uint32Array[ node32Index + 6 ];\n\t\t\tconst count = uint16Array[ node16Index + 14 ];\n\n\t\t\tlet minx = Infinity;\n\t\t\tlet miny = Infinity;\n\t\t\tlet minz = Infinity;\n\t\t\tlet maxx = - Infinity;\n\t\t\tlet maxy = - Infinity;\n\t\t\tlet maxz = - Infinity;\n\n\n\t\t\tfor ( let i = 3 * offset, l = 3 * ( offset + count ); i < l; i ++ ) {\n\n\t\t\t\tlet index = indexArr[ i ];\n\t\t\t\tconst x = posAttr.getX( index );\n\t\t\t\tconst y = posAttr.getY( index );\n\t\t\t\tconst z = posAttr.getZ( index );\n\n\t\t\t\tif ( x < minx ) minx = x;\n\t\t\t\tif ( x > maxx ) maxx = x;\n\n\t\t\t\tif ( y < miny ) miny = y;\n\t\t\t\tif ( y > maxy ) maxy = y;\n\n\t\t\t\tif ( z < minz ) minz = z;\n\t\t\t\tif ( z > maxz ) maxz = z;\n\n\t\t\t}\n\n\n\t\t\tif (\n\t\t\t\tfloat32Array[ node32Index + 0 ] !== minx ||\n\t\t\t\tfloat32Array[ node32Index + 1 ] !== miny ||\n\t\t\t\tfloat32Array[ node32Index + 2 ] !== minz ||\n\n\t\t\t\tfloat32Array[ node32Index + 3 ] !== maxx ||\n\t\t\t\tfloat32Array[ node32Index + 4 ] !== maxy ||\n\t\t\t\tfloat32Array[ node32Index + 5 ] !== maxz\n\t\t\t) {\n\n\t\t\t\tfloat32Array[ node32Index + 0 ] = minx;\n\t\t\t\tfloat32Array[ node32Index + 1 ] = miny;\n\t\t\t\tfloat32Array[ node32Index + 2 ] = minz;\n\n\t\t\t\tfloat32Array[ node32Index + 3 ] = maxx;\n\t\t\t\tfloat32Array[ node32Index + 4 ] = maxy;\n\t\t\t\tfloat32Array[ node32Index + 5 ] = maxz;\n\n\t\t\t\treturn true;\n\n\t\t\t} else {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconst left = node32Index + 8;\n\t\t\tconst right = uint32Array[ node32Index + 6 ];\n\n\t\t\t// the identifying node indices provided by the shapecast function include offsets of all\n\t\t\t// root buffers to guarantee they're unique between roots so offset left and right indices here.\n\t\t\tconst offsetLeft = left + byteOffset;\n\t\t\tconst offsetRight = right + byteOffset;\n\t\t\tlet forceChildren = force;\n\t\t\tlet includesLeft = false;\n\t\t\tlet includesRight = false;\n\n\t\t\tif ( nodeIndices ) {\n\n\t\t\t\t// if we see that neither the left or right child are included in the set that need to be updated\n\t\t\t\t// then we assume that all children need to be updated.\n\t\t\t\tif ( ! forceChildren ) {\n\n\t\t\t\t\tincludesLeft = nodeIndices.has( offsetLeft );\n\t\t\t\t\tincludesRight = nodeIndices.has( offsetRight );\n\t\t\t\t\tforceChildren = ! includesLeft && ! includesRight;\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tincludesLeft = true;\n\t\t\t\tincludesRight = true;\n\n\t\t\t}\n\n\t\t\tconst traverseLeft = forceChildren || includesLeft;\n\t\t\tconst traverseRight = forceChildren || includesRight;\n\n\t\t\tlet leftChange = false;\n\t\t\tif ( traverseLeft ) {\n\n\t\t\t\tleftChange = _traverse( left, byteOffset, forceChildren );\n\n\t\t\t}\n\n\t\t\tlet rightChange = false;\n\t\t\tif ( traverseRight ) {\n\n\t\t\t\trightChange = _traverse( right, byteOffset, forceChildren );\n\n\t\t\t}\n\n\t\t\tconst didChange = leftChange || rightChange;\n\t\t\tif ( didChange ) {\n\n\t\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\t\tconst lefti = left + i;\n\t\t\t\t\tconst righti = right + i;\n\t\t\t\t\tconst minLeftValue = float32Array[ lefti ];\n\t\t\t\t\tconst maxLeftValue = float32Array[ lefti + 3 ];\n\t\t\t\t\tconst minRightValue = float32Array[ righti ];\n\t\t\t\t\tconst maxRightValue = float32Array[ righti + 3 ];\n\n\t\t\t\t\tfloat32Array[ node32Index + i ] = minLeftValue < minRightValue ? minLeftValue : minRightValue;\n\t\t\t\t\tfloat32Array[ node32Index + i + 3 ] = maxLeftValue > maxRightValue ? maxLeftValue : maxRightValue;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn didChange;\n\n\t\t}\n\n\t}\n\n}\n\nexport { refit };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,oDAAoD,GACpD,oDAAoD,GACpD,oDAAoD,GAEpD,SAAS,MAAO,GAAG,EAAE,cAAc,IAAI;IAEtC,IAAK,eAAe,MAAM,OAAO,CAAE,cAAgB;QAElD,cAAc,IAAI,IAAK;IAExB;IAEA,MAAM,WAAW,IAAI,QAAQ;IAC7B,MAAM,WAAW,SAAS,KAAK,GAAG,SAAS,KAAK,CAAC,KAAK,GAAG;IACzD,MAAM,UAAU,SAAS,UAAU,CAAC,QAAQ;IAE5C,IAAI,QAAQ,aAAa,aAAa;IACtC,IAAI,aAAa;IACjB,MAAM,QAAQ,IAAI,MAAM;IACxB,IAAM,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,IAAO;QAEhD,SAAS,KAAK,CAAE,EAAG;QACnB,cAAc,IAAI,YAAa;QAC/B,cAAc,IAAI,YAAa;QAC/B,eAAe,IAAI,aAAc;QAEjC,UAAW,GAAG;QACd,cAAc,OAAO,UAAU;IAEhC;IAEA,SAAS,UAAW,WAAW,EAAE,UAAU,EAAE,QAAQ,KAAK;QAEzD,MAAM,cAAc,cAAc;QAClC,MAAM,SAAS,WAAW,CAAE,cAAc,GAAI,KAAK,mKAAA,CAAA,mBAAgB;QACnE,IAAK,QAAS;YAEb,MAAM,SAAS,WAAW,CAAE,cAAc,EAAG;YAC7C,MAAM,QAAQ,WAAW,CAAE,cAAc,GAAI;YAE7C,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,OAAO,CAAE;YACb,IAAI,OAAO,CAAE;YACb,IAAI,OAAO,CAAE;YAGb,IAAM,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,CAAE,SAAS,KAAM,GAAG,IAAI,GAAG,IAAO;gBAEnE,IAAI,QAAQ,QAAQ,CAAE,EAAG;gBACzB,MAAM,IAAI,QAAQ,IAAI,CAAE;gBACxB,MAAM,IAAI,QAAQ,IAAI,CAAE;gBACxB,MAAM,IAAI,QAAQ,IAAI,CAAE;gBAExB,IAAK,IAAI,MAAO,OAAO;gBACvB,IAAK,IAAI,MAAO,OAAO;gBAEvB,IAAK,IAAI,MAAO,OAAO;gBACvB,IAAK,IAAI,MAAO,OAAO;gBAEvB,IAAK,IAAI,MAAO,OAAO;gBACvB,IAAK,IAAI,MAAO,OAAO;YAExB;YAGA,IACC,YAAY,CAAE,cAAc,EAAG,KAAK,QACpC,YAAY,CAAE,cAAc,EAAG,KAAK,QACpC,YAAY,CAAE,cAAc,EAAG,KAAK,QAEpC,YAAY,CAAE,cAAc,EAAG,KAAK,QACpC,YAAY,CAAE,cAAc,EAAG,KAAK,QACpC,YAAY,CAAE,cAAc,EAAG,KAAK,MACnC;gBAED,YAAY,CAAE,cAAc,EAAG,GAAG;gBAClC,YAAY,CAAE,cAAc,EAAG,GAAG;gBAClC,YAAY,CAAE,cAAc,EAAG,GAAG;gBAElC,YAAY,CAAE,cAAc,EAAG,GAAG;gBAClC,YAAY,CAAE,cAAc,EAAG,GAAG;gBAClC,YAAY,CAAE,cAAc,EAAG,GAAG;gBAElC,OAAO;YAER,OAAO;gBAEN,OAAO;YAER;QAED,OAAO;YAEN,MAAM,OAAO,cAAc;YAC3B,MAAM,QAAQ,WAAW,CAAE,cAAc,EAAG;YAE5C,yFAAyF;YACzF,gGAAgG;YAChG,MAAM,aAAa,OAAO;YAC1B,MAAM,cAAc,QAAQ;YAC5B,IAAI,gBAAgB;YACpB,IAAI,eAAe;YACnB,IAAI,gBAAgB;YAEpB,IAAK,aAAc;gBAElB,iGAAiG;gBACjG,uDAAuD;gBACvD,IAAK,CAAE,eAAgB;oBAEtB,eAAe,YAAY,GAAG,CAAE;oBAChC,gBAAgB,YAAY,GAAG,CAAE;oBACjC,gBAAgB,CAAE,gBAAgB,CAAE;gBAErC;YAED,OAAO;gBAEN,eAAe;gBACf,gBAAgB;YAEjB;YAEA,MAAM,eAAe,iBAAiB;YACtC,MAAM,gBAAgB,iBAAiB;YAEvC,IAAI,aAAa;YACjB,IAAK,cAAe;gBAEnB,aAAa,UAAW,MAAM,YAAY;YAE3C;YAEA,IAAI,cAAc;YAClB,IAAK,eAAgB;gBAEpB,cAAc,UAAW,OAAO,YAAY;YAE7C;YAEA,MAAM,YAAY,cAAc;YAChC,IAAK,WAAY;gBAEhB,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;oBAE9B,MAAM,QAAQ,OAAO;oBACrB,MAAM,SAAS,QAAQ;oBACvB,MAAM,eAAe,YAAY,CAAE,MAAO;oBAC1C,MAAM,eAAe,YAAY,CAAE,QAAQ,EAAG;oBAC9C,MAAM,gBAAgB,YAAY,CAAE,OAAQ;oBAC5C,MAAM,gBAAgB,YAAY,CAAE,SAAS,EAAG;oBAEhD,YAAY,CAAE,cAAc,EAAG,GAAG,eAAe,gBAAgB,eAAe;oBAChF,YAAY,CAAE,cAAc,IAAI,EAAG,GAAG,eAAe,gBAAgB,eAAe;gBAErF;YAED;YAEA,OAAO;QAER;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2454, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/utils/intersectUtils.js"], "sourcesContent": ["/**\n * This function performs intersection tests similar to Ray.intersectBox in three.js,\n * with the difference that the box values are read from an array to improve performance.\n */\nexport function intersectRay( nodeIndex32, array, ray, near, far ) {\n\n\tlet tmin, tmax, tymin, tymax, tzmin, tzmax;\n\n\tconst invdirx = 1 / ray.direction.x,\n\t\tinvdiry = 1 / ray.direction.y,\n\t\tinvdirz = 1 / ray.direction.z;\n\n\tconst ox = ray.origin.x;\n\tconst oy = ray.origin.y;\n\tconst oz = ray.origin.z;\n\n\tlet minx = array[ nodeIndex32 ];\n\tlet maxx = array[ nodeIndex32 + 3 ];\n\n\tlet miny = array[ nodeIndex32 + 1 ];\n\tlet maxy = array[ nodeIndex32 + 3 + 1 ];\n\n\tlet minz = array[ nodeIndex32 + 2 ];\n\tlet maxz = array[ nodeIndex32 + 3 + 2 ];\n\n\tif ( invdirx >= 0 ) {\n\n\t\ttmin = ( minx - ox ) * invdirx;\n\t\ttmax = ( maxx - ox ) * invdirx;\n\n\t} else {\n\n\t\ttmin = ( maxx - ox ) * invdirx;\n\t\ttmax = ( minx - ox ) * invdirx;\n\n\t}\n\n\tif ( invdiry >= 0 ) {\n\n\t\ttymin = ( miny - oy ) * invdiry;\n\t\ttymax = ( maxy - oy ) * invdiry;\n\n\t} else {\n\n\t\ttymin = ( maxy - oy ) * invdiry;\n\t\ttymax = ( miny - oy ) * invdiry;\n\n\t}\n\n\tif ( ( tmin > tymax ) || ( tymin > tmax ) ) return false;\n\n\tif ( tymin > tmin || isNaN( tmin ) ) tmin = tymin;\n\n\tif ( tymax < tmax || isNaN( tmax ) ) tmax = tymax;\n\n\tif ( invdirz >= 0 ) {\n\n\t\ttzmin = ( minz - oz ) * invdirz;\n\t\ttzmax = ( maxz - oz ) * invdirz;\n\n\t} else {\n\n\t\ttzmin = ( maxz - oz ) * invdirz;\n\t\ttzmax = ( minz - oz ) * invdirz;\n\n\t}\n\n\tif ( ( tmin > tzmax ) || ( tzmin > tmax ) ) return false;\n\n\tif ( tzmin > tmin || tmin !== tmin ) tmin = tzmin;\n\n\tif ( tzmax < tmax || tmax !== tmax ) tmax = tzmax;\n\n\t//return point closest to the ray (positive side)\n\n\treturn tmin <= far && tmax >= near;\n\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACM,SAAS,aAAc,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;IAE/D,IAAI,MAAM,MAAM,OAAO,OAAO,OAAO;IAErC,MAAM,UAAU,IAAI,IAAI,SAAS,CAAC,CAAC,EAClC,UAAU,IAAI,IAAI,SAAS,CAAC,CAAC,EAC7B,UAAU,IAAI,IAAI,SAAS,CAAC,CAAC;IAE9B,MAAM,KAAK,IAAI,MAAM,CAAC,CAAC;IACvB,MAAM,KAAK,IAAI,MAAM,CAAC,CAAC;IACvB,MAAM,KAAK,IAAI,MAAM,CAAC,CAAC;IAEvB,IAAI,OAAO,KAAK,CAAE,YAAa;IAC/B,IAAI,OAAO,KAAK,CAAE,cAAc,EAAG;IAEnC,IAAI,OAAO,KAAK,CAAE,cAAc,EAAG;IACnC,IAAI,OAAO,KAAK,CAAE,cAAc,IAAI,EAAG;IAEvC,IAAI,OAAO,KAAK,CAAE,cAAc,EAAG;IACnC,IAAI,OAAO,KAAK,CAAE,cAAc,IAAI,EAAG;IAEvC,IAAK,WAAW,GAAI;QAEnB,OAAO,CAAE,OAAO,EAAG,IAAI;QACvB,OAAO,CAAE,OAAO,EAAG,IAAI;IAExB,OAAO;QAEN,OAAO,CAAE,OAAO,EAAG,IAAI;QACvB,OAAO,CAAE,OAAO,EAAG,IAAI;IAExB;IAEA,IAAK,WAAW,GAAI;QAEnB,QAAQ,CAAE,OAAO,EAAG,IAAI;QACxB,QAAQ,CAAE,OAAO,EAAG,IAAI;IAEzB,OAAO;QAEN,QAAQ,CAAE,OAAO,EAAG,IAAI;QACxB,QAAQ,CAAE,OAAO,EAAG,IAAI;IAEzB;IAEA,IAAK,AAAE,OAAO,SAAa,QAAQ,MAAS,OAAO;IAEnD,IAAK,QAAQ,QAAQ,MAAO,OAAS,OAAO;IAE5C,IAAK,QAAQ,QAAQ,MAAO,OAAS,OAAO;IAE5C,IAAK,WAAW,GAAI;QAEnB,QAAQ,CAAE,OAAO,EAAG,IAAI;QACxB,QAAQ,CAAE,OAAO,EAAG,IAAI;IAEzB,OAAO;QAEN,QAAQ,CAAE,OAAO,EAAG,IAAI;QACxB,QAAQ,CAAE,OAAO,EAAG,IAAI;IAEzB;IAEA,IAAK,AAAE,OAAO,SAAa,QAAQ,MAAS,OAAO;IAEnD,IAAK,QAAQ,QAAQ,SAAS,MAAO,OAAO;IAE5C,IAAK,QAAQ,QAAQ,SAAS,MAAO,OAAO;IAE5C,iDAAiD;IAEjD,OAAO,QAAQ,OAAO,QAAQ;AAE/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/cast/raycast.generated.js"], "sourcesContent": ["import { intersectRay } from '../utils/intersectUtils.js';\nimport { IS_LEAF, OFFSET, COUNT, LEFT_NODE, RIGHT_NODE } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\nimport { intersectTris } from '../utils/iterationUtils.generated.js';\nimport '../utils/iterationUtils_indirect.generated.js';\n\n/******************************************************/\n/* This file is generated from \"raycast.template.js\". */\n/******************************************************/\n\nfunction raycast( bvh, root, side, ray, intersects, near, far ) {\n\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\t_raycast( 0, bvh, side, ray, intersects, near, far );\n\tBufferStack.clearBuffer();\n\n}\n\nfunction _raycast( nodeIndex32, bvh, side, ray, intersects, near, far ) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tconst nodeIndex16 = nodeIndex32 * 2;\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\n\t\tintersectTris( bvh, side, ray, offset, count, intersects, near, far );\n\n\n\t} else {\n\n\t\tconst leftIndex = LEFT_NODE( nodeIndex32 );\n\t\tif ( intersectRay( leftIndex, float32Array, ray, near, far ) ) {\n\n\t\t\t_raycast( leftIndex, bvh, side, ray, intersects, near, far );\n\n\t\t}\n\n\t\tconst rightIndex = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\tif ( intersectRay( rightIndex, float32Array, ray, near, far ) ) {\n\n\t\t\t_raycast( rightIndex, bvh, side, ray, intersects, near, far );\n\n\t\t}\n\n\t}\n\n}\n\nexport { raycast };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;;AAGA,sDAAsD,GACtD,sDAAsD,GACtD,sDAAsD,GAEtD,SAAS,QAAS,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG;IAE5D,8KAAA,CAAA,cAAW,CAAC,SAAS,CAAE,IAAI,MAAM,CAAE,KAAM;IACzC,SAAU,GAAG,KAAK,MAAM,KAAK,YAAY,MAAM;IAC/C,8KAAA,CAAA,cAAW,CAAC,WAAW;AAExB;AAEA,SAAS,SAAU,WAAW,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG;IAEpE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,8KAAA,CAAA,cAAW;IAC9D,MAAM,cAAc,cAAc;IAClC,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,UAAO,AAAD,EAAG,aAAa;IACrC,IAAK,QAAS;QAEb,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAG,aAAa;QACpC,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,QAAK,AAAD,EAAG,aAAa;QAGlC,CAAA,GAAA,8LAAA,CAAA,gBAAa,AAAD,EAAG,KAAK,MAAM,KAAK,QAAQ,OAAO,YAAY,MAAM;IAGjE,OAAO;QAEN,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAG;QAC7B,IAAK,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAG,WAAW,cAAc,KAAK,MAAM,MAAQ;YAE9D,SAAU,WAAW,KAAK,MAAM,KAAK,YAAY,MAAM;QAExD;QAEA,MAAM,aAAa,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAG,aAAa;QAC5C,IAAK,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAG,YAAY,cAAc,KAAK,MAAM,MAAQ;YAE/D,SAAU,YAAY,KAAK,MAAM,KAAK,YAAY,MAAM;QAEzD;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2551, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/cast/raycastFirst.generated.js"], "sourcesContent": ["import { IS_LEAF, OFFSET, COUNT, SPLIT_AXIS, LEFT_NODE, RIGHT_NODE } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\nimport { intersectRay } from '../utils/intersectUtils.js';\nimport { intersectClosestTri } from '../utils/iterationUtils.generated.js';\nimport '../utils/iterationUtils_indirect.generated.js';\n\n/***********************************************************/\n/* This file is generated from \"raycastFirst.template.js\". */\n/***********************************************************/\n\nconst _xyzFields = [ 'x', 'y', 'z' ];\n\nfunction raycastFirst( bvh, root, side, ray, near, far ) {\n\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\tconst result = _raycastFirst( 0, bvh, side, ray, near, far );\n\tBufferStack.clearBuffer();\n\n\treturn result;\n\n}\n\nfunction _raycastFirst( nodeIndex32, bvh, side, ray, near, far ) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\n\t\t// eslint-disable-next-line no-unreachable\n\t\treturn intersectClosestTri( bvh, side, ray, offset, count, near, far );\n\n\n\t} else {\n\n\t\t// consider the position of the split plane with respect to the oncoming ray; whichever direction\n\t\t// the ray is coming from, look for an intersection among that side of the tree first\n\t\tconst splitAxis = SPLIT_AXIS( nodeIndex32, uint32Array );\n\t\tconst xyzAxis = _xyzFields[ splitAxis ];\n\t\tconst rayDir = ray.direction[ xyzAxis ];\n\t\tconst leftToRight = rayDir >= 0;\n\n\t\t// c1 is the child to check first\n\t\tlet c1, c2;\n\t\tif ( leftToRight ) {\n\n\t\t\tc1 = LEFT_NODE( nodeIndex32 );\n\t\t\tc2 = RIGHT_NODE( nodeIndex32, uint32Array );\n\n\t\t} else {\n\n\t\t\tc1 = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\tc2 = LEFT_NODE( nodeIndex32 );\n\n\t\t}\n\n\t\tconst c1Intersection = intersectRay( c1, float32Array, ray, near, far );\n\t\tconst c1Result = c1Intersection ? _raycastFirst( c1, bvh, side, ray, near, far ) : null;\n\n\t\t// if we got an intersection in the first node and it's closer than the second node's bounding\n\t\t// box, we don't need to consider the second node because it couldn't possibly be a better result\n\t\tif ( c1Result ) {\n\n\t\t\t// check if the point is within the second bounds\n\t\t\t// \"point\" is in the local frame of the bvh\n\t\t\tconst point = c1Result.point[ xyzAxis ];\n\t\t\tconst isOutside = leftToRight ?\n\t\t\t\tpoint <= float32Array[ c2 + splitAxis ] : // min bounding data\n\t\t\t\tpoint >= float32Array[ c2 + splitAxis + 3 ]; // max bounding data\n\n\t\t\tif ( isOutside ) {\n\n\t\t\t\treturn c1Result;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// either there was no intersection in the first node, or there could still be a closer\n\t\t// intersection in the second, so check the second node and then take the better of the two\n\t\tconst c2Intersection = intersectRay( c2, float32Array, ray, near, far );\n\t\tconst c2Result = c2Intersection ? _raycastFirst( c2, bvh, side, ray, near, far ) : null;\n\n\t\tif ( c1Result && c2Result ) {\n\n\t\t\treturn c1Result.distance <= c2Result.distance ? c1Result : c2Result;\n\n\t\t} else {\n\n\t\t\treturn c1Result || c2Result || null;\n\n\t\t}\n\n\t}\n\n}\n\nexport { raycastFirst };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;;AAGA,2DAA2D,GAC3D,2DAA2D,GAC3D,2DAA2D,GAE3D,MAAM,aAAa;IAAE;IAAK;IAAK;CAAK;AAEpC,SAAS,aAAc,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;IAErD,8KAAA,CAAA,cAAW,CAAC,SAAS,CAAE,IAAI,MAAM,CAAE,KAAM;IACzC,MAAM,SAAS,cAAe,GAAG,KAAK,MAAM,KAAK,MAAM;IACvD,8KAAA,CAAA,cAAW,CAAC,WAAW;IAEvB,OAAO;AAER;AAEA,SAAS,cAAe,WAAW,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;IAE7D,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,8KAAA,CAAA,cAAW;IAC9D,IAAI,cAAc,cAAc;IAEhC,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,UAAO,AAAD,EAAG,aAAa;IACrC,IAAK,QAAS;QAEb,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAG,aAAa;QACpC,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,QAAK,AAAD,EAAG,aAAa;QAGlC,0CAA0C;QAC1C,OAAO,CAAA,GAAA,8LAAA,CAAA,sBAAmB,AAAD,EAAG,KAAK,MAAM,KAAK,QAAQ,OAAO,MAAM;IAGlE,OAAO;QAEN,iGAAiG;QACjG,qFAAqF;QACrF,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAG,aAAa;QAC3C,MAAM,UAAU,UAAU,CAAE,UAAW;QACvC,MAAM,SAAS,IAAI,SAAS,CAAE,QAAS;QACvC,MAAM,cAAc,UAAU;QAE9B,iCAAiC;QACjC,IAAI,IAAI;QACR,IAAK,aAAc;YAElB,KAAK,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAG;YAChB,KAAK,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAG,aAAa;QAE/B,OAAO;YAEN,KAAK,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAG,aAAa;YAC9B,KAAK,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAG;QAEjB;QAEA,MAAM,iBAAiB,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAG,IAAI,cAAc,KAAK,MAAM;QAClE,MAAM,WAAW,iBAAiB,cAAe,IAAI,KAAK,MAAM,KAAK,MAAM,OAAQ;QAEnF,8FAA8F;QAC9F,iGAAiG;QACjG,IAAK,UAAW;YAEf,iDAAiD;YACjD,2CAA2C;YAC3C,MAAM,QAAQ,SAAS,KAAK,CAAE,QAAS;YACvC,MAAM,YAAY,cACjB,SAAS,YAAY,CAAE,KAAK,UAAW,GACvC,SAAS,YAAY,CAAE,KAAK,YAAY,EAAG,EAAE,oBAAoB;YAElE,IAAK,WAAY;gBAEhB,OAAO;YAER;QAED;QAEA,uFAAuF;QACvF,2FAA2F;QAC3F,MAAM,iBAAiB,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAG,IAAI,cAAc,KAAK,MAAM;QAClE,MAAM,WAAW,iBAAiB,cAAe,IAAI,KAAK,MAAM,KAAK,MAAM,OAAQ;QAEnF,IAAK,YAAY,UAAW;YAE3B,OAAO,SAAS,QAAQ,IAAI,SAAS,QAAQ,GAAG,WAAW;QAE5D,OAAO;YAEN,OAAO,YAAY,YAAY;QAEhC;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2630, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/cast/intersectsGeometry.generated.js"], "sourcesContent": ["import { Box3, Matrix4 } from 'three';\nimport { OrientedBox } from '../../math/OrientedBox.js';\nimport { ExtendedTriangle } from '../../math/ExtendedTriangle.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\nimport { arrayToBox } from '../../utils/ArrayBoxUtilities.js';\nimport { IS_LEAF, OFFSET, COUNT, BOUNDING_DATA_INDEX } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\n\n/*****************************************************************/\n/* This file is generated from \"intersectsGeometry.template.js\". */\n/*****************************************************************/\n/* eslint-disable indent */\n\nconst boundingBox = /* @__PURE__ */ new Box3();\nconst triangle = /* @__PURE__ */ new ExtendedTriangle();\nconst triangle2 = /* @__PURE__ */ new ExtendedTriangle();\nconst invertedMat = /* @__PURE__ */ new Matrix4();\n\nconst obb = /* @__PURE__ */ new OrientedBox();\nconst obb2 = /* @__PURE__ */ new OrientedBox();\n\nfunction intersectsGeometry( bvh, root, otherGeometry, geometryToBvh ) {\n\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\tconst result = _intersectsGeometry( 0, bvh, otherGeometry, geometryToBvh );\n\tBufferStack.clearBuffer();\n\n\treturn result;\n\n}\n\nfunction _intersectsGeometry( nodeIndex32, bvh, otherGeometry, geometryToBvh, cachedObb = null ) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\tif ( cachedObb === null ) {\n\n\t\tif ( ! otherGeometry.boundingBox ) {\n\n\t\t\totherGeometry.computeBoundingBox();\n\n\t\t}\n\n\t\tobb.set( otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh );\n\t\tcachedObb = obb;\n\n\t}\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst thisGeometry = bvh.geometry;\n\t\tconst thisIndex = thisGeometry.index;\n\t\tconst thisPos = thisGeometry.attributes.position;\n\n\t\tconst index = otherGeometry.index;\n\t\tconst pos = otherGeometry.attributes.position;\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\t\t// get the inverse of the geometry matrix so we can transform our triangles into the\n\t\t// geometry space we're trying to test. We assume there are fewer triangles being checked\n\t\t// here.\n\t\tinvertedMat.copy( geometryToBvh ).invert();\n\n\t\tif ( otherGeometry.boundsTree ) {\n\n\t\t\t// if there's a bounds tree\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( nodeIndex32 ), float32Array, obb2 );\n\t\t\tobb2.matrix.copy( invertedMat );\n\t\t\tobb2.needsUpdate = true;\n\n\t\t\t// TODO: use a triangle iteration function here\n\t\t\tconst res = otherGeometry.boundsTree.shapecast( {\n\n\t\t\t\tintersectsBounds: box => obb2.intersectsBox( box ),\n\n\t\t\t\tintersectsTriangle: tri => {\n\n\t\t\t\t\ttri.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\ttri.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\ttri.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\ttri.needsUpdate = true;\n\n\n\t\t\t\t\tfor ( let i = offset * 3, l = ( count + offset ) * 3; i < l; i += 3 ) {\n\n\t\t\t\t\t\t// this triangle needs to be transformed into the current BVH coordinate frame\n\t\t\t\t\t\tsetTriangle( triangle2, i, thisIndex, thisPos );\n\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\t\t\t\t\t\tif ( tri.intersectsTriangle( triangle2 ) ) {\n\n\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\n\t\t\t\t\treturn false;\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t\treturn res;\n\n\t\t} else {\n\n\t\t\t// if we're just dealing with raw geometry\n\n\t\t\tfor ( let i = offset * 3, l = ( count + offset ) * 3; i < l; i += 3 ) {\n\n\t\t\t\t// this triangle needs to be transformed into the current BVH coordinate frame\n\t\t\t\tsetTriangle( triangle, i, thisIndex, thisPos );\n\n\n\t\t\t\ttriangle.a.applyMatrix4( invertedMat );\n\t\t\t\ttriangle.b.applyMatrix4( invertedMat );\n\t\t\t\ttriangle.c.applyMatrix4( invertedMat );\n\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\tfor ( let i2 = 0, l2 = index.count; i2 < l2; i2 += 3 ) {\n\n\t\t\t\t\tsetTriangle( triangle2, i2, index, pos );\n\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\tif ( triangle.intersectsTriangle( triangle2 ) ) {\n\n\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\n\t\t\t}\n\n\n\t\t}\n\n\t} else {\n\n\t\tconst left = nodeIndex32 + 8;\n\t\tconst right = uint32Array[ nodeIndex32 + 6 ];\n\n\t\tarrayToBox( BOUNDING_DATA_INDEX( left ), float32Array, boundingBox );\n\t\tconst leftIntersection =\n\t\t\tcachedObb.intersectsBox( boundingBox ) &&\n\t\t\t_intersectsGeometry( left, bvh, otherGeometry, geometryToBvh, cachedObb );\n\n\t\tif ( leftIntersection ) return true;\n\n\t\tarrayToBox( BOUNDING_DATA_INDEX( right ), float32Array, boundingBox );\n\t\tconst rightIntersection =\n\t\t\tcachedObb.intersectsBox( boundingBox ) &&\n\t\t\t_intersectsGeometry( right, bvh, otherGeometry, geometryToBvh, cachedObb );\n\n\t\tif ( rightIntersection ) return true;\n\n\t\treturn false;\n\n\t}\n\n}\n\nexport { intersectsGeometry };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,iEAAiE,GACjE,iEAAiE,GACjE,iEAAiE,GACjE,yBAAyB,GAEzB,MAAM,cAAc,aAAa,GAAG,IAAI,kJAAA,CAAA,OAAI;AAC5C,MAAM,WAAW,aAAa,GAAG,IAAI,0KAAA,CAAA,mBAAgB;AACrD,MAAM,YAAY,aAAa,GAAG,IAAI,0KAAA,CAAA,mBAAgB;AACtD,MAAM,cAAc,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAE/C,MAAM,MAAM,aAAa,GAAG,IAAI,qKAAA,CAAA,cAAW;AAC3C,MAAM,OAAO,aAAa,GAAG,IAAI,qKAAA,CAAA,cAAW;AAE5C,SAAS,mBAAoB,GAAG,EAAE,IAAI,EAAE,aAAa,EAAE,aAAa;IAEnE,8KAAA,CAAA,cAAW,CAAC,SAAS,CAAE,IAAI,MAAM,CAAE,KAAM;IACzC,MAAM,SAAS,oBAAqB,GAAG,KAAK,eAAe;IAC3D,8KAAA,CAAA,cAAW,CAAC,WAAW;IAEvB,OAAO;AAER;AAEA,SAAS,oBAAqB,WAAW,EAAE,GAAG,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,IAAI;IAE7F,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,8KAAA,CAAA,cAAW;IAC9D,IAAI,cAAc,cAAc;IAEhC,IAAK,cAAc,MAAO;QAEzB,IAAK,CAAE,cAAc,WAAW,EAAG;YAElC,cAAc,kBAAkB;QAEjC;QAEA,IAAI,GAAG,CAAE,cAAc,WAAW,CAAC,GAAG,EAAE,cAAc,WAAW,CAAC,GAAG,EAAE;QACvE,YAAY;IAEb;IAEA,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,UAAO,AAAD,EAAG,aAAa;IACrC,IAAK,QAAS;QAEb,MAAM,eAAe,IAAI,QAAQ;QACjC,MAAM,YAAY,aAAa,KAAK;QACpC,MAAM,UAAU,aAAa,UAAU,CAAC,QAAQ;QAEhD,MAAM,QAAQ,cAAc,KAAK;QACjC,MAAM,MAAM,cAAc,UAAU,CAAC,QAAQ;QAE7C,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAG,aAAa;QACpC,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,QAAK,AAAD,EAAG,aAAa;QAElC,oFAAoF;QACpF,yFAAyF;QACzF,QAAQ;QACR,YAAY,IAAI,CAAE,eAAgB,MAAM;QAExC,IAAK,cAAc,UAAU,EAAG;YAE/B,2BAA2B;YAC3B,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,cAAe,cAAc;YAC9D,KAAK,MAAM,CAAC,IAAI,CAAE;YAClB,KAAK,WAAW,GAAG;YAEnB,+CAA+C;YAC/C,MAAM,MAAM,cAAc,UAAU,CAAC,SAAS,CAAE;gBAE/C,kBAAkB,CAAA,MAAO,KAAK,aAAa,CAAE;gBAE7C,oBAAoB,CAAA;oBAEnB,IAAI,CAAC,CAAC,YAAY,CAAE;oBACpB,IAAI,CAAC,CAAC,YAAY,CAAE;oBACpB,IAAI,CAAC,CAAC,YAAY,CAAE;oBACpB,IAAI,WAAW,GAAG;oBAGlB,IAAM,IAAI,IAAI,SAAS,GAAG,IAAI,CAAE,QAAQ,MAAO,IAAI,GAAG,IAAI,GAAG,KAAK,EAAI;wBAErE,8EAA8E;wBAC9E,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,WAAW,GAAG,WAAW;wBACtC,UAAU,WAAW,GAAG;wBACxB,IAAK,IAAI,kBAAkB,CAAE,YAAc;4BAE1C,OAAO;wBAER;oBAED;oBAGA,OAAO;gBAER;YAED;YAEA,OAAO;QAER,OAAO;YAEN,0CAA0C;YAE1C,IAAM,IAAI,IAAI,SAAS,GAAG,IAAI,CAAE,QAAQ,MAAO,IAAI,GAAG,IAAI,GAAG,KAAK,EAAI;gBAErE,8EAA8E;gBAC9E,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,UAAU,GAAG,WAAW;gBAGrC,SAAS,CAAC,CAAC,YAAY,CAAE;gBACzB,SAAS,CAAC,CAAC,YAAY,CAAE;gBACzB,SAAS,CAAC,CAAC,YAAY,CAAE;gBACzB,SAAS,WAAW,GAAG;gBAEvB,IAAM,IAAI,KAAK,GAAG,KAAK,MAAM,KAAK,EAAE,KAAK,IAAI,MAAM,EAAI;oBAEtD,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,WAAW,IAAI,OAAO;oBACnC,UAAU,WAAW,GAAG;oBAExB,IAAK,SAAS,kBAAkB,CAAE,YAAc;wBAE/C,OAAO;oBAER;gBAED;YAGD;QAGD;IAED,OAAO;QAEN,MAAM,OAAO,cAAc;QAC3B,MAAM,QAAQ,WAAW,CAAE,cAAc,EAAG;QAE5C,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,OAAQ,cAAc;QACvD,MAAM,mBACL,UAAU,aAAa,CAAE,gBACzB,oBAAqB,MAAM,KAAK,eAAe,eAAe;QAE/D,IAAK,kBAAmB,OAAO;QAE/B,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,QAAS,cAAc;QACxD,MAAM,oBACL,UAAU,aAAa,CAAE,gBACzB,oBAAqB,OAAO,KAAK,eAAe,eAAe;QAEhE,IAAK,mBAAoB,OAAO;QAEhC,OAAO;IAER;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2744, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/cast/closestPointToGeometry.generated.js"], "sourcesContent": ["import { Matrix4, Vector3 } from 'three';\nimport { OrientedBox } from '../../math/OrientedBox.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\nimport { getTriCount } from '../build/geometryUtils.js';\nimport { ExtendedTrianglePool } from '../../utils/ExtendedTrianglePool.js';\n\n/*********************************************************************/\n/* This file is generated from \"closestPointToGeometry.template.js\". */\n/*********************************************************************/\n\nconst tempMatrix = /* @__PURE__ */ new Matrix4();\nconst obb = /* @__PURE__ */ new OrientedBox();\nconst obb2 = /* @__PURE__ */ new OrientedBox();\nconst temp1 = /* @__PURE__ */ new Vector3();\nconst temp2 = /* @__PURE__ */ new Vector3();\nconst temp3 = /* @__PURE__ */ new Vector3();\nconst temp4 = /* @__PURE__ */ new Vector3();\n\nfunction closestPointToGeometry(\n\tbvh,\n\totherGeometry,\n\tgeometryToBvh,\n\ttarget1 = { },\n\ttarget2 = { },\n\tminThreshold = 0,\n\tmaxThreshold = Infinity,\n) {\n\n\tif ( ! otherGeometry.boundingBox ) {\n\n\t\totherGeometry.computeBoundingBox();\n\n\t}\n\n\tobb.set( otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh );\n\tobb.needsUpdate = true;\n\n\tconst geometry = bvh.geometry;\n\tconst pos = geometry.attributes.position;\n\tconst index = geometry.index;\n\tconst otherPos = otherGeometry.attributes.position;\n\tconst otherIndex = otherGeometry.index;\n\tconst triangle = ExtendedTrianglePool.getPrimitive();\n\tconst triangle2 = ExtendedTrianglePool.getPrimitive();\n\n\tlet tempTarget1 = temp1;\n\tlet tempTargetDest1 = temp2;\n\tlet tempTarget2 = null;\n\tlet tempTargetDest2 = null;\n\n\tif ( target2 ) {\n\n\t\ttempTarget2 = temp3;\n\t\ttempTargetDest2 = temp4;\n\n\t}\n\n\tlet closestDistance = Infinity;\n\tlet closestDistanceTriIndex = null;\n\tlet closestDistanceOtherTriIndex = null;\n\ttempMatrix.copy( geometryToBvh ).invert();\n\tobb2.matrix.copy( tempMatrix );\n\tbvh.shapecast(\n\t\t{\n\n\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\treturn obb.distanceToBox( box );\n\n\t\t\t},\n\n\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\tif ( score < closestDistance && score < maxThreshold ) {\n\n\t\t\t\t\t// if we know the triangles of this bounds will be intersected next then\n\t\t\t\t\t// save the bounds to use during triangle checks.\n\t\t\t\t\tif ( isLeaf ) {\n\n\t\t\t\t\t\tobb2.min.copy( box.min );\n\t\t\t\t\t\tobb2.max.copy( box.max );\n\t\t\t\t\t\tobb2.needsUpdate = true;\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\n\t\t\t},\n\n\t\t\tintersectsRange: ( offset, count ) => {\n\n\t\t\t\tif ( otherGeometry.boundsTree ) {\n\n\t\t\t\t\t// if the other geometry has a bvh then use the accelerated path where we use shapecast to find\n\t\t\t\t\t// the closest bounds in the other geometry to check.\n\t\t\t\t\tconst otherBvh = otherGeometry.boundsTree;\n\t\t\t\t\treturn otherBvh.shapecast( {\n\t\t\t\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\t\t\t\treturn obb2.distanceToBox( box );\n\n\t\t\t\t\t\t},\n\n\t\t\t\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\t\t\t\treturn score < closestDistance && score < maxThreshold;\n\n\t\t\t\t\t\t},\n\n\t\t\t\t\t\tintersectsRange: ( otherOffset, otherCount ) => {\n\n\t\t\t\t\t\t\tfor ( let i2 = otherOffset, l2 = otherOffset + otherCount; i2 < l2; i2 ++ ) {\n\n\n\t\t\t\t\t\t\t\tsetTriangle( triangle2, 3 * i2, otherIndex, otherPos );\n\n\t\t\t\t\t\t\t\ttriangle2.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\ttriangle2.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\ttriangle2.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\t\t\tfor ( let i = offset, l = offset + count; i < l; i ++ ) {\n\n\n\t\t\t\t\t\t\t\t\tsetTriangle( triangle, 3 * i, index, pos );\n\n\t\t\t\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\t\t\t\tconst dist = triangle.distanceToTriangle( triangle2, tempTarget1, tempTarget2 );\n\t\t\t\t\t\t\t\t\tif ( dist < closestDistance ) {\n\n\t\t\t\t\t\t\t\t\t\ttempTargetDest1.copy( tempTarget1 );\n\n\t\t\t\t\t\t\t\t\t\tif ( tempTargetDest2 ) {\n\n\t\t\t\t\t\t\t\t\t\t\ttempTargetDest2.copy( tempTarget2 );\n\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\tclosestDistance = dist;\n\t\t\t\t\t\t\t\t\t\tclosestDistanceTriIndex = i;\n\t\t\t\t\t\t\t\t\t\tclosestDistanceOtherTriIndex = i2;\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t// stop traversal if we find a point that's under the given threshold\n\t\t\t\t\t\t\t\t\tif ( dist < minThreshold ) {\n\n\t\t\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t},\n\t\t\t\t\t} );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// If no bounds tree then we'll just check every triangle.\n\t\t\t\t\tconst triCount = getTriCount( otherGeometry );\n\t\t\t\t\tfor ( let i2 = 0, l2 = triCount; i2 < l2; i2 ++ ) {\n\n\t\t\t\t\t\tsetTriangle( triangle2, 3 * i2, otherIndex, otherPos );\n\t\t\t\t\t\ttriangle2.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttriangle2.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttriangle2.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\tfor ( let i = offset, l = offset + count; i < l; i ++ ) {\n\n\n\t\t\t\t\t\t\tsetTriangle( triangle, 3 * i, index, pos );\n\n\t\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\t\tconst dist = triangle.distanceToTriangle( triangle2, tempTarget1, tempTarget2 );\n\t\t\t\t\t\t\tif ( dist < closestDistance ) {\n\n\t\t\t\t\t\t\t\ttempTargetDest1.copy( tempTarget1 );\n\n\t\t\t\t\t\t\t\tif ( tempTargetDest2 ) {\n\n\t\t\t\t\t\t\t\t\ttempTargetDest2.copy( tempTarget2 );\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tclosestDistance = dist;\n\t\t\t\t\t\t\t\tclosestDistanceTriIndex = i;\n\t\t\t\t\t\t\t\tclosestDistanceOtherTriIndex = i2;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// stop traversal if we find a point that's under the given threshold\n\t\t\t\t\t\t\tif ( dist < minThreshold ) {\n\n\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t},\n\n\t\t}\n\n\t);\n\n\tExtendedTrianglePool.releasePrimitive( triangle );\n\tExtendedTrianglePool.releasePrimitive( triangle2 );\n\n\tif ( closestDistance === Infinity ) {\n\n\t\treturn null;\n\n\t}\n\n\tif ( ! target1.point ) {\n\n\t\ttarget1.point = tempTargetDest1.clone();\n\n\t} else {\n\n\t\ttarget1.point.copy( tempTargetDest1 );\n\n\t}\n\n\ttarget1.distance = closestDistance,\n\ttarget1.faceIndex = closestDistanceTriIndex;\n\n\tif ( target2 ) {\n\n\t\tif ( ! target2.point ) target2.point = tempTargetDest2.clone();\n\t\telse target2.point.copy( tempTargetDest2 );\n\t\ttarget2.point.applyMatrix4( tempMatrix );\n\t\ttempTargetDest1.applyMatrix4( tempMatrix );\n\t\ttarget2.distance = tempTargetDest1.sub( target2.point ).length();\n\t\ttarget2.faceIndex = closestDistanceOtherTriIndex;\n\n\t}\n\n\treturn target1;\n\n}\n\nexport { closestPointToGeometry };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,qEAAqE,GACrE,qEAAqE,GACrE,qEAAqE,GAErE,MAAM,aAAa,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAC9C,MAAM,MAAM,aAAa,GAAG,IAAI,qKAAA,CAAA,cAAW;AAC3C,MAAM,OAAO,aAAa,GAAG,IAAI,qKAAA,CAAA,cAAW;AAC5C,MAAM,QAAQ,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AACzC,MAAM,QAAQ,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AACzC,MAAM,QAAQ,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AACzC,MAAM,QAAQ,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAEzC,SAAS,uBACR,GAAG,EACH,aAAa,EACb,aAAa,EACb,UAAU,CAAE,CAAC,EACb,UAAU,CAAE,CAAC,EACb,eAAe,CAAC,EAChB,eAAe,QAAQ;IAGvB,IAAK,CAAE,cAAc,WAAW,EAAG;QAElC,cAAc,kBAAkB;IAEjC;IAEA,IAAI,GAAG,CAAE,cAAc,WAAW,CAAC,GAAG,EAAE,cAAc,WAAW,CAAC,GAAG,EAAE;IACvE,IAAI,WAAW,GAAG;IAElB,MAAM,WAAW,IAAI,QAAQ;IAC7B,MAAM,MAAM,SAAS,UAAU,CAAC,QAAQ;IACxC,MAAM,QAAQ,SAAS,KAAK;IAC5B,MAAM,WAAW,cAAc,UAAU,CAAC,QAAQ;IAClD,MAAM,aAAa,cAAc,KAAK;IACtC,MAAM,WAAW,+KAAA,CAAA,uBAAoB,CAAC,YAAY;IAClD,MAAM,YAAY,+KAAA,CAAA,uBAAoB,CAAC,YAAY;IAEnD,IAAI,cAAc;IAClB,IAAI,kBAAkB;IACtB,IAAI,cAAc;IAClB,IAAI,kBAAkB;IAEtB,IAAK,SAAU;QAEd,cAAc;QACd,kBAAkB;IAEnB;IAEA,IAAI,kBAAkB;IACtB,IAAI,0BAA0B;IAC9B,IAAI,+BAA+B;IACnC,WAAW,IAAI,CAAE,eAAgB,MAAM;IACvC,KAAK,MAAM,CAAC,IAAI,CAAE;IAClB,IAAI,SAAS,CACZ;QAEC,qBAAqB,CAAA;YAEpB,OAAO,IAAI,aAAa,CAAE;QAE3B;QAEA,kBAAkB,CAAE,KAAK,QAAQ;YAEhC,IAAK,QAAQ,mBAAmB,QAAQ,cAAe;gBAEtD,wEAAwE;gBACxE,iDAAiD;gBACjD,IAAK,QAAS;oBAEb,KAAK,GAAG,CAAC,IAAI,CAAE,IAAI,GAAG;oBACtB,KAAK,GAAG,CAAC,IAAI,CAAE,IAAI,GAAG;oBACtB,KAAK,WAAW,GAAG;gBAEpB;gBAEA,OAAO;YAER;YAEA,OAAO;QAER;QAEA,iBAAiB,CAAE,QAAQ;YAE1B,IAAK,cAAc,UAAU,EAAG;gBAE/B,+FAA+F;gBAC/F,qDAAqD;gBACrD,MAAM,WAAW,cAAc,UAAU;gBACzC,OAAO,SAAS,SAAS,CAAE;oBAC1B,qBAAqB,CAAA;wBAEpB,OAAO,KAAK,aAAa,CAAE;oBAE5B;oBAEA,kBAAkB,CAAE,KAAK,QAAQ;wBAEhC,OAAO,QAAQ,mBAAmB,QAAQ;oBAE3C;oBAEA,iBAAiB,CAAE,aAAa;wBAE/B,IAAM,IAAI,KAAK,aAAa,KAAK,cAAc,YAAY,KAAK,IAAI,KAAQ;4BAG3E,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,WAAW,IAAI,IAAI,YAAY;4BAE5C,UAAU,CAAC,CAAC,YAAY,CAAE;4BAC1B,UAAU,CAAC,CAAC,YAAY,CAAE;4BAC1B,UAAU,CAAC,CAAC,YAAY,CAAE;4BAC1B,UAAU,WAAW,GAAG;4BAExB,IAAM,IAAI,IAAI,QAAQ,IAAI,SAAS,OAAO,IAAI,GAAG,IAAO;gCAGvD,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,UAAU,IAAI,GAAG,OAAO;gCAErC,SAAS,WAAW,GAAG;gCAEvB,MAAM,OAAO,SAAS,kBAAkB,CAAE,WAAW,aAAa;gCAClE,IAAK,OAAO,iBAAkB;oCAE7B,gBAAgB,IAAI,CAAE;oCAEtB,IAAK,iBAAkB;wCAEtB,gBAAgB,IAAI,CAAE;oCAEvB;oCAEA,kBAAkB;oCAClB,0BAA0B;oCAC1B,+BAA+B;gCAEhC;gCAEA,qEAAqE;gCACrE,IAAK,OAAO,cAAe;oCAE1B,OAAO;gCAER;4BAED;wBAED;oBAED;gBACD;YAED,OAAO;gBAEN,0DAA0D;gBAC1D,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAG;gBAC9B,IAAM,IAAI,KAAK,GAAG,KAAK,UAAU,KAAK,IAAI,KAAQ;oBAEjD,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,WAAW,IAAI,IAAI,YAAY;oBAC5C,UAAU,CAAC,CAAC,YAAY,CAAE;oBAC1B,UAAU,CAAC,CAAC,YAAY,CAAE;oBAC1B,UAAU,CAAC,CAAC,YAAY,CAAE;oBAC1B,UAAU,WAAW,GAAG;oBAExB,IAAM,IAAI,IAAI,QAAQ,IAAI,SAAS,OAAO,IAAI,GAAG,IAAO;wBAGvD,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,UAAU,IAAI,GAAG,OAAO;wBAErC,SAAS,WAAW,GAAG;wBAEvB,MAAM,OAAO,SAAS,kBAAkB,CAAE,WAAW,aAAa;wBAClE,IAAK,OAAO,iBAAkB;4BAE7B,gBAAgB,IAAI,CAAE;4BAEtB,IAAK,iBAAkB;gCAEtB,gBAAgB,IAAI,CAAE;4BAEvB;4BAEA,kBAAkB;4BAClB,0BAA0B;4BAC1B,+BAA+B;wBAEhC;wBAEA,qEAAqE;wBACrE,IAAK,OAAO,cAAe;4BAE1B,OAAO;wBAER;oBAED;gBAED;YAED;QAED;IAED;IAID,+KAAA,CAAA,uBAAoB,CAAC,gBAAgB,CAAE;IACvC,+KAAA,CAAA,uBAAoB,CAAC,gBAAgB,CAAE;IAEvC,IAAK,oBAAoB,UAAW;QAEnC,OAAO;IAER;IAEA,IAAK,CAAE,QAAQ,KAAK,EAAG;QAEtB,QAAQ,KAAK,GAAG,gBAAgB,KAAK;IAEtC,OAAO;QAEN,QAAQ,KAAK,CAAC,IAAI,CAAE;IAErB;IAEA,QAAQ,QAAQ,GAAG,iBACnB,QAAQ,SAAS,GAAG;IAEpB,IAAK,SAAU;QAEd,IAAK,CAAE,QAAQ,KAAK,EAAG,QAAQ,KAAK,GAAG,gBAAgB,KAAK;aACvD,QAAQ,KAAK,CAAC,IAAI,CAAE;QACzB,QAAQ,KAAK,CAAC,YAAY,CAAE;QAC5B,gBAAgB,YAAY,CAAE;QAC9B,QAAQ,QAAQ,GAAG,gBAAgB,GAAG,CAAE,QAAQ,KAAK,EAAG,MAAM;QAC9D,QAAQ,SAAS,GAAG;IAErB;IAEA,OAAO;AAER", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2906, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/utils/iterationUtils_indirect.generated.js"], "sourcesContent": ["import { intersectTri } from '../../utils/ThreeRayIntersectUtilities.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\n\n/*************************************************************/\n/* This file is generated from \"iterationUtils.template.js\". */\n/*************************************************************/\n/* eslint-disable indent */\n\nfunction intersectTris_indirect( bvh, side, ray, offset, count, intersections, near, far ) {\n\n\tconst { geometry, _indirectBuffer } = bvh;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tlet vi = _indirectBuffer ? _indirectBuffer[ i ] : i;\n\t\tintersectTri( geometry, side, ray, vi, intersections, near, far );\n\n\n\t}\n\n}\n\nfunction intersectClosestTri_indirect( bvh, side, ray, offset, count, near, far ) {\n\n\tconst { geometry, _indirectBuffer } = bvh;\n\tlet dist = Infinity;\n\tlet res = null;\n\tfor ( let i = offset, end = offset + count; i < end; i ++ ) {\n\n\t\tlet intersection;\n\t\tintersection = intersectTri( geometry, side, ray, _indirectBuffer ? _indirectBuffer[ i ] : i, null, near, far );\n\n\n\t\tif ( intersection && intersection.distance < dist ) {\n\n\t\t\tres = intersection;\n\t\t\tdist = intersection.distance;\n\n\t\t}\n\n\t}\n\n\treturn res;\n\n}\n\nfunction iterateOverTriangles_indirect(\n\toffset,\n\tcount,\n\tbvh,\n\tintersectsTriangleFunc,\n\tcontained,\n\tdepth,\n\ttriangle\n) {\n\n\tconst { geometry } = bvh;\n\tconst { index } = geometry;\n\tconst pos = geometry.attributes.position;\n\tfor ( let i = offset, l = count + offset; i < l; i ++ ) {\n\n\t\tlet tri;\n\t\ttri = bvh.resolveTriangleIndex( i );\n\n\t\tsetTriangle( triangle, tri * 3, index, pos );\n\t\ttriangle.needsUpdate = true;\n\n\t\tif ( intersectsTriangleFunc( triangle, tri, contained, depth ) ) {\n\n\t\t\treturn true;\n\n\t\t}\n\n\t}\n\n\treturn false;\n\n}\n\nexport { intersectClosestTri_indirect, intersectTris_indirect, iterateOverTriangles_indirect };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEA,6DAA6D,GAC7D,6DAA6D,GAC7D,6DAA6D,GAC7D,yBAAyB,GAEzB,SAAS,uBAAwB,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG;IAEvF,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;IACtC,IAAM,IAAI,IAAI,QAAQ,MAAM,SAAS,OAAO,IAAI,KAAK,IAAO;QAE3D,IAAI,KAAK,kBAAkB,eAAe,CAAE,EAAG,GAAG;QAClD,CAAA,GAAA,qLAAA,CAAA,eAAY,AAAD,EAAG,UAAU,MAAM,KAAK,IAAI,eAAe,MAAM;IAG7D;AAED;AAEA,SAAS,6BAA8B,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;IAE9E,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;IACtC,IAAI,OAAO;IACX,IAAI,MAAM;IACV,IAAM,IAAI,IAAI,QAAQ,MAAM,SAAS,OAAO,IAAI,KAAK,IAAO;QAE3D,IAAI;QACJ,eAAe,CAAA,GAAA,qLAAA,CAAA,eAAY,AAAD,EAAG,UAAU,MAAM,KAAK,kBAAkB,eAAe,CAAE,EAAG,GAAG,GAAG,MAAM,MAAM;QAG1G,IAAK,gBAAgB,aAAa,QAAQ,GAAG,MAAO;YAEnD,MAAM;YACN,OAAO,aAAa,QAAQ;QAE7B;IAED;IAEA,OAAO;AAER;AAEA,SAAS,8BACR,MAAM,EACN,KAAK,EACL,GAAG,EACH,sBAAsB,EACtB,SAAS,EACT,KAAK,EACL,QAAQ;IAGR,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,MAAM,MAAM,SAAS,UAAU,CAAC,QAAQ;IACxC,IAAM,IAAI,IAAI,QAAQ,IAAI,QAAQ,QAAQ,IAAI,GAAG,IAAO;QAEvD,IAAI;QACJ,MAAM,IAAI,oBAAoB,CAAE;QAEhC,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,UAAU,MAAM,GAAG,OAAO;QACvC,SAAS,WAAW,GAAG;QAEvB,IAAK,uBAAwB,UAAU,KAAK,WAAW,QAAU;YAEhE,OAAO;QAER;IAED;IAEA,OAAO;AAER", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2958, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/cast/refit_indirect.generated.js"], "sourcesContent": ["import { IS_LEAFNODE_FLAG } from '../Constants.js';\n\n/****************************************************/\n/* This file is generated from \"refit.template.js\". */\n/****************************************************/\n\nfunction refit_indirect( bvh, nodeIndices = null ) {\n\n\tif ( nodeIndices && Array.isArray( nodeIndices ) ) {\n\n\t\tnodeIndices = new Set( nodeIndices );\n\n\t}\n\n\tconst geometry = bvh.geometry;\n\tconst indexArr = geometry.index ? geometry.index.array : null;\n\tconst posAttr = geometry.attributes.position;\n\n\tlet buffer, uint32Array, uint16Array, float32Array;\n\tlet byteOffset = 0;\n\tconst roots = bvh._roots;\n\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\tbuffer = roots[ i ];\n\t\tuint32Array = new Uint32Array( buffer );\n\t\tuint16Array = new Uint16Array( buffer );\n\t\tfloat32Array = new Float32Array( buffer );\n\n\t\t_traverse( 0, byteOffset );\n\t\tbyteOffset += buffer.byteLength;\n\n\t}\n\n\tfunction _traverse( node32Index, byteOffset, force = false ) {\n\n\t\tconst node16Index = node32Index * 2;\n\t\tconst isLeaf = uint16Array[ node16Index + 15 ] === IS_LEAFNODE_FLAG;\n\t\tif ( isLeaf ) {\n\n\t\t\tconst offset = uint32Array[ node32Index + 6 ];\n\t\t\tconst count = uint16Array[ node16Index + 14 ];\n\n\t\t\tlet minx = Infinity;\n\t\t\tlet miny = Infinity;\n\t\t\tlet minz = Infinity;\n\t\t\tlet maxx = - Infinity;\n\t\t\tlet maxy = - Infinity;\n\t\t\tlet maxz = - Infinity;\n\n\t\t\tfor ( let i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\t\t\tconst t = 3 * bvh.resolveTriangleIndex( i );\n\t\t\t\tfor ( let j = 0; j < 3; j ++ ) {\n\n\t\t\t\t\tlet index = t + j;\n\t\t\t\t\tindex = indexArr ? indexArr[ index ] : index;\n\n\t\t\t\t\tconst x = posAttr.getX( index );\n\t\t\t\t\tconst y = posAttr.getY( index );\n\t\t\t\t\tconst z = posAttr.getZ( index );\n\n\t\t\t\t\tif ( x < minx ) minx = x;\n\t\t\t\t\tif ( x > maxx ) maxx = x;\n\n\t\t\t\t\tif ( y < miny ) miny = y;\n\t\t\t\t\tif ( y > maxy ) maxy = y;\n\n\t\t\t\t\tif ( z < minz ) minz = z;\n\t\t\t\t\tif ( z > maxz ) maxz = z;\n\n\n\t\t\t\t}\n\n\t\t\t}\n\n\n\t\t\tif (\n\t\t\t\tfloat32Array[ node32Index + 0 ] !== minx ||\n\t\t\t\tfloat32Array[ node32Index + 1 ] !== miny ||\n\t\t\t\tfloat32Array[ node32Index + 2 ] !== minz ||\n\n\t\t\t\tfloat32Array[ node32Index + 3 ] !== maxx ||\n\t\t\t\tfloat32Array[ node32Index + 4 ] !== maxy ||\n\t\t\t\tfloat32Array[ node32Index + 5 ] !== maxz\n\t\t\t) {\n\n\t\t\t\tfloat32Array[ node32Index + 0 ] = minx;\n\t\t\t\tfloat32Array[ node32Index + 1 ] = miny;\n\t\t\t\tfloat32Array[ node32Index + 2 ] = minz;\n\n\t\t\t\tfloat32Array[ node32Index + 3 ] = maxx;\n\t\t\t\tfloat32Array[ node32Index + 4 ] = maxy;\n\t\t\t\tfloat32Array[ node32Index + 5 ] = maxz;\n\n\t\t\t\treturn true;\n\n\t\t\t} else {\n\n\t\t\t\treturn false;\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconst left = node32Index + 8;\n\t\t\tconst right = uint32Array[ node32Index + 6 ];\n\n\t\t\t// the identifying node indices provided by the shapecast function include offsets of all\n\t\t\t// root buffers to guarantee they're unique between roots so offset left and right indices here.\n\t\t\tconst offsetLeft = left + byteOffset;\n\t\t\tconst offsetRight = right + byteOffset;\n\t\t\tlet forceChildren = force;\n\t\t\tlet includesLeft = false;\n\t\t\tlet includesRight = false;\n\n\t\t\tif ( nodeIndices ) {\n\n\t\t\t\t// if we see that neither the left or right child are included in the set that need to be updated\n\t\t\t\t// then we assume that all children need to be updated.\n\t\t\t\tif ( ! forceChildren ) {\n\n\t\t\t\t\tincludesLeft = nodeIndices.has( offsetLeft );\n\t\t\t\t\tincludesRight = nodeIndices.has( offsetRight );\n\t\t\t\t\tforceChildren = ! includesLeft && ! includesRight;\n\n\t\t\t\t}\n\n\t\t\t} else {\n\n\t\t\t\tincludesLeft = true;\n\t\t\t\tincludesRight = true;\n\n\t\t\t}\n\n\t\t\tconst traverseLeft = forceChildren || includesLeft;\n\t\t\tconst traverseRight = forceChildren || includesRight;\n\n\t\t\tlet leftChange = false;\n\t\t\tif ( traverseLeft ) {\n\n\t\t\t\tleftChange = _traverse( left, byteOffset, forceChildren );\n\n\t\t\t}\n\n\t\t\tlet rightChange = false;\n\t\t\tif ( traverseRight ) {\n\n\t\t\t\trightChange = _traverse( right, byteOffset, forceChildren );\n\n\t\t\t}\n\n\t\t\tconst didChange = leftChange || rightChange;\n\t\t\tif ( didChange ) {\n\n\t\t\t\tfor ( let i = 0; i < 3; i ++ ) {\n\n\t\t\t\t\tconst lefti = left + i;\n\t\t\t\t\tconst righti = right + i;\n\t\t\t\t\tconst minLeftValue = float32Array[ lefti ];\n\t\t\t\t\tconst maxLeftValue = float32Array[ lefti + 3 ];\n\t\t\t\t\tconst minRightValue = float32Array[ righti ];\n\t\t\t\t\tconst maxRightValue = float32Array[ righti + 3 ];\n\n\t\t\t\t\tfloat32Array[ node32Index + i ] = minLeftValue < minRightValue ? minLeftValue : minRightValue;\n\t\t\t\t\tfloat32Array[ node32Index + i + 3 ] = maxLeftValue > maxRightValue ? maxLeftValue : maxRightValue;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn didChange;\n\n\t\t}\n\n\t}\n\n}\n\nexport { refit_indirect };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,oDAAoD,GACpD,oDAAoD,GACpD,oDAAoD,GAEpD,SAAS,eAAgB,GAAG,EAAE,cAAc,IAAI;IAE/C,IAAK,eAAe,MAAM,OAAO,CAAE,cAAgB;QAElD,cAAc,IAAI,IAAK;IAExB;IAEA,MAAM,WAAW,IAAI,QAAQ;IAC7B,MAAM,WAAW,SAAS,KAAK,GAAG,SAAS,KAAK,CAAC,KAAK,GAAG;IACzD,MAAM,UAAU,SAAS,UAAU,CAAC,QAAQ;IAE5C,IAAI,QAAQ,aAAa,aAAa;IACtC,IAAI,aAAa;IACjB,MAAM,QAAQ,IAAI,MAAM;IACxB,IAAM,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,IAAO;QAEhD,SAAS,KAAK,CAAE,EAAG;QACnB,cAAc,IAAI,YAAa;QAC/B,cAAc,IAAI,YAAa;QAC/B,eAAe,IAAI,aAAc;QAEjC,UAAW,GAAG;QACd,cAAc,OAAO,UAAU;IAEhC;IAEA,SAAS,UAAW,WAAW,EAAE,UAAU,EAAE,QAAQ,KAAK;QAEzD,MAAM,cAAc,cAAc;QAClC,MAAM,SAAS,WAAW,CAAE,cAAc,GAAI,KAAK,mKAAA,CAAA,mBAAgB;QACnE,IAAK,QAAS;YAEb,MAAM,SAAS,WAAW,CAAE,cAAc,EAAG;YAC7C,MAAM,QAAQ,WAAW,CAAE,cAAc,GAAI;YAE7C,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,OAAO;YACX,IAAI,OAAO,CAAE;YACb,IAAI,OAAO,CAAE;YACb,IAAI,OAAO,CAAE;YAEb,IAAM,IAAI,IAAI,QAAQ,IAAI,SAAS,OAAO,IAAI,GAAG,IAAO;gBAEvD,MAAM,IAAI,IAAI,IAAI,oBAAoB,CAAE;gBACxC,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;oBAE9B,IAAI,QAAQ,IAAI;oBAChB,QAAQ,WAAW,QAAQ,CAAE,MAAO,GAAG;oBAEvC,MAAM,IAAI,QAAQ,IAAI,CAAE;oBACxB,MAAM,IAAI,QAAQ,IAAI,CAAE;oBACxB,MAAM,IAAI,QAAQ,IAAI,CAAE;oBAExB,IAAK,IAAI,MAAO,OAAO;oBACvB,IAAK,IAAI,MAAO,OAAO;oBAEvB,IAAK,IAAI,MAAO,OAAO;oBACvB,IAAK,IAAI,MAAO,OAAO;oBAEvB,IAAK,IAAI,MAAO,OAAO;oBACvB,IAAK,IAAI,MAAO,OAAO;gBAGxB;YAED;YAGA,IACC,YAAY,CAAE,cAAc,EAAG,KAAK,QACpC,YAAY,CAAE,cAAc,EAAG,KAAK,QACpC,YAAY,CAAE,cAAc,EAAG,KAAK,QAEpC,YAAY,CAAE,cAAc,EAAG,KAAK,QACpC,YAAY,CAAE,cAAc,EAAG,KAAK,QACpC,YAAY,CAAE,cAAc,EAAG,KAAK,MACnC;gBAED,YAAY,CAAE,cAAc,EAAG,GAAG;gBAClC,YAAY,CAAE,cAAc,EAAG,GAAG;gBAClC,YAAY,CAAE,cAAc,EAAG,GAAG;gBAElC,YAAY,CAAE,cAAc,EAAG,GAAG;gBAClC,YAAY,CAAE,cAAc,EAAG,GAAG;gBAClC,YAAY,CAAE,cAAc,EAAG,GAAG;gBAElC,OAAO;YAER,OAAO;gBAEN,OAAO;YAER;QAED,OAAO;YAEN,MAAM,OAAO,cAAc;YAC3B,MAAM,QAAQ,WAAW,CAAE,cAAc,EAAG;YAE5C,yFAAyF;YACzF,gGAAgG;YAChG,MAAM,aAAa,OAAO;YAC1B,MAAM,cAAc,QAAQ;YAC5B,IAAI,gBAAgB;YACpB,IAAI,eAAe;YACnB,IAAI,gBAAgB;YAEpB,IAAK,aAAc;gBAElB,iGAAiG;gBACjG,uDAAuD;gBACvD,IAAK,CAAE,eAAgB;oBAEtB,eAAe,YAAY,GAAG,CAAE;oBAChC,gBAAgB,YAAY,GAAG,CAAE;oBACjC,gBAAgB,CAAE,gBAAgB,CAAE;gBAErC;YAED,OAAO;gBAEN,eAAe;gBACf,gBAAgB;YAEjB;YAEA,MAAM,eAAe,iBAAiB;YACtC,MAAM,gBAAgB,iBAAiB;YAEvC,IAAI,aAAa;YACjB,IAAK,cAAe;gBAEnB,aAAa,UAAW,MAAM,YAAY;YAE3C;YAEA,IAAI,cAAc;YAClB,IAAK,eAAgB;gBAEpB,cAAc,UAAW,OAAO,YAAY;YAE7C;YAEA,MAAM,YAAY,cAAc;YAChC,IAAK,WAAY;gBAEhB,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;oBAE9B,MAAM,QAAQ,OAAO;oBACrB,MAAM,SAAS,QAAQ;oBACvB,MAAM,eAAe,YAAY,CAAE,MAAO;oBAC1C,MAAM,eAAe,YAAY,CAAE,QAAQ,EAAG;oBAC9C,MAAM,gBAAgB,YAAY,CAAE,OAAQ;oBAC5C,MAAM,gBAAgB,YAAY,CAAE,SAAS,EAAG;oBAEhD,YAAY,CAAE,cAAc,EAAG,GAAG,eAAe,gBAAgB,eAAe;oBAChF,YAAY,CAAE,cAAc,IAAI,EAAG,GAAG,eAAe,gBAAgB,eAAe;gBAErF;YAED;YAEA,OAAO;QAER;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3076, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/cast/raycast_indirect.generated.js"], "sourcesContent": ["import { intersectRay } from '../utils/intersectUtils.js';\nimport { IS_LEAF, OFFSET, COUNT, LEFT_NODE, RIGHT_NODE } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\nimport '../utils/iterationUtils.generated.js';\nimport { intersectTris_indirect } from '../utils/iterationUtils_indirect.generated.js';\n\n/******************************************************/\n/* This file is generated from \"raycast.template.js\". */\n/******************************************************/\n\nfunction raycast_indirect( bvh, root, side, ray, intersects, near, far ) {\n\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\t_raycast( 0, bvh, side, ray, intersects, near, far );\n\tBufferStack.clearBuffer();\n\n}\n\nfunction _raycast( nodeIndex32, bvh, side, ray, intersects, near, far ) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tconst nodeIndex16 = nodeIndex32 * 2;\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\t\tintersectTris_indirect( bvh, side, ray, offset, count, intersects, near, far );\n\n\n\t} else {\n\n\t\tconst leftIndex = LEFT_NODE( nodeIndex32 );\n\t\tif ( intersectRay( leftIndex, float32Array, ray, near, far ) ) {\n\n\t\t\t_raycast( leftIndex, bvh, side, ray, intersects, near, far );\n\n\t\t}\n\n\t\tconst rightIndex = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\tif ( intersectRay( rightIndex, float32Array, ray, near, far ) ) {\n\n\t\t\t_raycast( rightIndex, bvh, side, ray, intersects, near, far );\n\n\t\t}\n\n\t}\n\n}\n\nexport { raycast_indirect };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,sDAAsD,GACtD,sDAAsD,GACtD,sDAAsD,GAEtD,SAAS,iBAAkB,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG;IAErE,8KAAA,CAAA,cAAW,CAAC,SAAS,CAAE,IAAI,MAAM,CAAE,KAAM;IACzC,SAAU,GAAG,KAAK,MAAM,KAAK,YAAY,MAAM;IAC/C,8KAAA,CAAA,cAAW,CAAC,WAAW;AAExB;AAEA,SAAS,SAAU,WAAW,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG;IAEpE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,8KAAA,CAAA,cAAW;IAC9D,MAAM,cAAc,cAAc;IAClC,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,UAAO,AAAD,EAAG,aAAa;IACrC,IAAK,QAAS;QAEb,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAG,aAAa;QACpC,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,QAAK,AAAD,EAAG,aAAa;QAElC,CAAA,GAAA,uMAAA,CAAA,yBAAsB,AAAD,EAAG,KAAK,MAAM,KAAK,QAAQ,OAAO,YAAY,MAAM;IAG1E,OAAO;QAEN,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAG;QAC7B,IAAK,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAG,WAAW,cAAc,KAAK,MAAM,MAAQ;YAE9D,SAAU,WAAW,KAAK,MAAM,KAAK,YAAY,MAAM;QAExD;QAEA,MAAM,aAAa,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAG,aAAa;QAC5C,IAAK,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAG,YAAY,cAAc,KAAK,MAAM,MAAQ;YAE/D,SAAU,YAAY,KAAK,MAAM,KAAK,YAAY,MAAM;QAEzD;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/cast/raycastFirst_indirect.generated.js"], "sourcesContent": ["import { IS_LEAF, OFFSET, COUNT, SPLIT_AXIS, LEFT_NODE, RIGHT_NODE } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\nimport { intersectRay } from '../utils/intersectUtils.js';\nimport '../utils/iterationUtils.generated.js';\nimport { intersectClosestTri_indirect } from '../utils/iterationUtils_indirect.generated.js';\n\n/***********************************************************/\n/* This file is generated from \"raycastFirst.template.js\". */\n/***********************************************************/\n\nconst _xyzFields = [ 'x', 'y', 'z' ];\n\nfunction raycastFirst_indirect( bvh, root, side, ray, near, far ) {\n\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\tconst result = _raycastFirst( 0, bvh, side, ray, near, far );\n\tBufferStack.clearBuffer();\n\n\treturn result;\n\n}\n\nfunction _raycastFirst( nodeIndex32, bvh, side, ray, near, far ) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\t\treturn intersectClosestTri_indirect( bvh, side, ray, offset, count, near, far );\n\n\n\t} else {\n\n\t\t// consider the position of the split plane with respect to the oncoming ray; whichever direction\n\t\t// the ray is coming from, look for an intersection among that side of the tree first\n\t\tconst splitAxis = SPLIT_AXIS( nodeIndex32, uint32Array );\n\t\tconst xyzAxis = _xyzFields[ splitAxis ];\n\t\tconst rayDir = ray.direction[ xyzAxis ];\n\t\tconst leftToRight = rayDir >= 0;\n\n\t\t// c1 is the child to check first\n\t\tlet c1, c2;\n\t\tif ( leftToRight ) {\n\n\t\t\tc1 = LEFT_NODE( nodeIndex32 );\n\t\t\tc2 = RIGHT_NODE( nodeIndex32, uint32Array );\n\n\t\t} else {\n\n\t\t\tc1 = RIGHT_NODE( nodeIndex32, uint32Array );\n\t\t\tc2 = LEFT_NODE( nodeIndex32 );\n\n\t\t}\n\n\t\tconst c1Intersection = intersectRay( c1, float32Array, ray, near, far );\n\t\tconst c1Result = c1Intersection ? _raycastFirst( c1, bvh, side, ray, near, far ) : null;\n\n\t\t// if we got an intersection in the first node and it's closer than the second node's bounding\n\t\t// box, we don't need to consider the second node because it couldn't possibly be a better result\n\t\tif ( c1Result ) {\n\n\t\t\t// check if the point is within the second bounds\n\t\t\t// \"point\" is in the local frame of the bvh\n\t\t\tconst point = c1Result.point[ xyzAxis ];\n\t\t\tconst isOutside = leftToRight ?\n\t\t\t\tpoint <= float32Array[ c2 + splitAxis ] : // min bounding data\n\t\t\t\tpoint >= float32Array[ c2 + splitAxis + 3 ]; // max bounding data\n\n\t\t\tif ( isOutside ) {\n\n\t\t\t\treturn c1Result;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// either there was no intersection in the first node, or there could still be a closer\n\t\t// intersection in the second, so check the second node and then take the better of the two\n\t\tconst c2Intersection = intersectRay( c2, float32Array, ray, near, far );\n\t\tconst c2Result = c2Intersection ? _raycastFirst( c2, bvh, side, ray, near, far ) : null;\n\n\t\tif ( c1Result && c2Result ) {\n\n\t\t\treturn c1Result.distance <= c2Result.distance ? c1Result : c2Result;\n\n\t\t} else {\n\n\t\t\treturn c1Result || c2Result || null;\n\n\t\t}\n\n\t}\n\n}\n\nexport { raycastFirst_indirect };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,2DAA2D,GAC3D,2DAA2D,GAC3D,2DAA2D,GAE3D,MAAM,aAAa;IAAE;IAAK;IAAK;CAAK;AAEpC,SAAS,sBAAuB,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;IAE9D,8KAAA,CAAA,cAAW,CAAC,SAAS,CAAE,IAAI,MAAM,CAAE,KAAM;IACzC,MAAM,SAAS,cAAe,GAAG,KAAK,MAAM,KAAK,MAAM;IACvD,8KAAA,CAAA,cAAW,CAAC,WAAW;IAEvB,OAAO;AAER;AAEA,SAAS,cAAe,WAAW,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;IAE7D,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,8KAAA,CAAA,cAAW;IAC9D,IAAI,cAAc,cAAc;IAEhC,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,UAAO,AAAD,EAAG,aAAa;IACrC,IAAK,QAAS;QAEb,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAG,aAAa;QACpC,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,QAAK,AAAD,EAAG,aAAa;QAElC,OAAO,CAAA,GAAA,uMAAA,CAAA,+BAA4B,AAAD,EAAG,KAAK,MAAM,KAAK,QAAQ,OAAO,MAAM;IAG3E,OAAO;QAEN,iGAAiG;QACjG,qFAAqF;QACrF,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAG,aAAa;QAC3C,MAAM,UAAU,UAAU,CAAE,UAAW;QACvC,MAAM,SAAS,IAAI,SAAS,CAAE,QAAS;QACvC,MAAM,cAAc,UAAU;QAE9B,iCAAiC;QACjC,IAAI,IAAI;QACR,IAAK,aAAc;YAElB,KAAK,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAG;YAChB,KAAK,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAG,aAAa;QAE/B,OAAO;YAEN,KAAK,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAG,aAAa;YAC9B,KAAK,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAG;QAEjB;QAEA,MAAM,iBAAiB,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAG,IAAI,cAAc,KAAK,MAAM;QAClE,MAAM,WAAW,iBAAiB,cAAe,IAAI,KAAK,MAAM,KAAK,MAAM,OAAQ;QAEnF,8FAA8F;QAC9F,iGAAiG;QACjG,IAAK,UAAW;YAEf,iDAAiD;YACjD,2CAA2C;YAC3C,MAAM,QAAQ,SAAS,KAAK,CAAE,QAAS;YACvC,MAAM,YAAY,cACjB,SAAS,YAAY,CAAE,KAAK,UAAW,GACvC,SAAS,YAAY,CAAE,KAAK,YAAY,EAAG,EAAE,oBAAoB;YAElE,IAAK,WAAY;gBAEhB,OAAO;YAER;QAED;QAEA,uFAAuF;QACvF,2FAA2F;QAC3F,MAAM,iBAAiB,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAG,IAAI,cAAc,KAAK,MAAM;QAClE,MAAM,WAAW,iBAAiB,cAAe,IAAI,KAAK,MAAM,KAAK,MAAM,OAAQ;QAEnF,IAAK,YAAY,UAAW;YAE3B,OAAO,SAAS,QAAQ,IAAI,SAAS,QAAQ,GAAG,WAAW;QAE5D,OAAO;YAEN,OAAO,YAAY,YAAY;QAEhC;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/cast/intersectsGeometry_indirect.generated.js"], "sourcesContent": ["import { Box3, Matrix4 } from 'three';\nimport { OrientedBox } from '../../math/OrientedBox.js';\nimport { ExtendedTriangle } from '../../math/ExtendedTriangle.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\nimport { arrayToBox } from '../../utils/ArrayBoxUtilities.js';\nimport { IS_LEAF, OFFSET, COUNT, BOUNDING_DATA_INDEX } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\n\n/*****************************************************************/\n/* This file is generated from \"intersectsGeometry.template.js\". */\n/*****************************************************************/\n/* eslint-disable indent */\n\nconst boundingBox = /* @__PURE__ */ new Box3();\nconst triangle = /* @__PURE__ */ new ExtendedTriangle();\nconst triangle2 = /* @__PURE__ */ new ExtendedTriangle();\nconst invertedMat = /* @__PURE__ */ new Matrix4();\n\nconst obb = /* @__PURE__ */ new OrientedBox();\nconst obb2 = /* @__PURE__ */ new OrientedBox();\n\nfunction intersectsGeometry_indirect( bvh, root, otherGeometry, geometryToBvh ) {\n\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\tconst result = _intersectsGeometry( 0, bvh, otherGeometry, geometryToBvh );\n\tBufferStack.clearBuffer();\n\n\treturn result;\n\n}\n\nfunction _intersectsGeometry( nodeIndex32, bvh, otherGeometry, geometryToBvh, cachedObb = null ) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\tif ( cachedObb === null ) {\n\n\t\tif ( ! otherGeometry.boundingBox ) {\n\n\t\t\totherGeometry.computeBoundingBox();\n\n\t\t}\n\n\t\tobb.set( otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh );\n\t\tcachedObb = obb;\n\n\t}\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst thisGeometry = bvh.geometry;\n\t\tconst thisIndex = thisGeometry.index;\n\t\tconst thisPos = thisGeometry.attributes.position;\n\n\t\tconst index = otherGeometry.index;\n\t\tconst pos = otherGeometry.attributes.position;\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\t\t// get the inverse of the geometry matrix so we can transform our triangles into the\n\t\t// geometry space we're trying to test. We assume there are fewer triangles being checked\n\t\t// here.\n\t\tinvertedMat.copy( geometryToBvh ).invert();\n\n\t\tif ( otherGeometry.boundsTree ) {\n\n\t\t\t// if there's a bounds tree\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( nodeIndex32 ), float32Array, obb2 );\n\t\t\tobb2.matrix.copy( invertedMat );\n\t\t\tobb2.needsUpdate = true;\n\n\t\t\t// TODO: use a triangle iteration function here\n\t\t\tconst res = otherGeometry.boundsTree.shapecast( {\n\n\t\t\t\tintersectsBounds: box => obb2.intersectsBox( box ),\n\n\t\t\t\tintersectsTriangle: tri => {\n\n\t\t\t\t\ttri.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\ttri.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\ttri.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\ttri.needsUpdate = true;\n\n\t\t\t\t\tfor ( let i = offset, l = count + offset; i < l; i ++ ) {\n\n\t\t\t\t\t\t// this triangle needs to be transformed into the current BVH coordinate frame\n\t\t\t\t\t\tsetTriangle( triangle2, 3 * bvh.resolveTriangleIndex( i ), thisIndex, thisPos );\n\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\t\t\t\t\t\tif ( tri.intersectsTriangle( triangle2 ) ) {\n\n\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\n\t\t\t\t\treturn false;\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t\treturn res;\n\n\t\t} else {\n\n\t\t\t// if we're just dealing with raw geometry\n\t\t\tfor ( let i = offset, l = count + offset; i < l; i ++ ) {\n\n\t\t\t\t// this triangle needs to be transformed into the current BVH coordinate frame\n\t\t\t\tconst ti = bvh.resolveTriangleIndex( i );\n\t\t\t\tsetTriangle( triangle, 3 * ti, thisIndex, thisPos );\n\n\n\t\t\t\ttriangle.a.applyMatrix4( invertedMat );\n\t\t\t\ttriangle.b.applyMatrix4( invertedMat );\n\t\t\t\ttriangle.c.applyMatrix4( invertedMat );\n\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\tfor ( let i2 = 0, l2 = index.count; i2 < l2; i2 += 3 ) {\n\n\t\t\t\t\tsetTriangle( triangle2, i2, index, pos );\n\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\tif ( triangle.intersectsTriangle( triangle2 ) ) {\n\n\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\n\t\t}\n\n\t} else {\n\n\t\tconst left = nodeIndex32 + 8;\n\t\tconst right = uint32Array[ nodeIndex32 + 6 ];\n\n\t\tarrayToBox( BOUNDING_DATA_INDEX( left ), float32Array, boundingBox );\n\t\tconst leftIntersection =\n\t\t\tcachedObb.intersectsBox( boundingBox ) &&\n\t\t\t_intersectsGeometry( left, bvh, otherGeometry, geometryToBvh, cachedObb );\n\n\t\tif ( leftIntersection ) return true;\n\n\t\tarrayToBox( BOUNDING_DATA_INDEX( right ), float32Array, boundingBox );\n\t\tconst rightIntersection =\n\t\t\tcachedObb.intersectsBox( boundingBox ) &&\n\t\t\t_intersectsGeometry( right, bvh, otherGeometry, geometryToBvh, cachedObb );\n\n\t\tif ( rightIntersection ) return true;\n\n\t\treturn false;\n\n\t}\n\n}\n\nexport { intersectsGeometry_indirect };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,iEAAiE,GACjE,iEAAiE,GACjE,iEAAiE,GACjE,yBAAyB,GAEzB,MAAM,cAAc,aAAa,GAAG,IAAI,kJAAA,CAAA,OAAI;AAC5C,MAAM,WAAW,aAAa,GAAG,IAAI,0KAAA,CAAA,mBAAgB;AACrD,MAAM,YAAY,aAAa,GAAG,IAAI,0KAAA,CAAA,mBAAgB;AACtD,MAAM,cAAc,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAE/C,MAAM,MAAM,aAAa,GAAG,IAAI,qKAAA,CAAA,cAAW;AAC3C,MAAM,OAAO,aAAa,GAAG,IAAI,qKAAA,CAAA,cAAW;AAE5C,SAAS,4BAA6B,GAAG,EAAE,IAAI,EAAE,aAAa,EAAE,aAAa;IAE5E,8KAAA,CAAA,cAAW,CAAC,SAAS,CAAE,IAAI,MAAM,CAAE,KAAM;IACzC,MAAM,SAAS,oBAAqB,GAAG,KAAK,eAAe;IAC3D,8KAAA,CAAA,cAAW,CAAC,WAAW;IAEvB,OAAO;AAER;AAEA,SAAS,oBAAqB,WAAW,EAAE,GAAG,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,IAAI;IAE7F,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,8KAAA,CAAA,cAAW;IAC9D,IAAI,cAAc,cAAc;IAEhC,IAAK,cAAc,MAAO;QAEzB,IAAK,CAAE,cAAc,WAAW,EAAG;YAElC,cAAc,kBAAkB;QAEjC;QAEA,IAAI,GAAG,CAAE,cAAc,WAAW,CAAC,GAAG,EAAE,cAAc,WAAW,CAAC,GAAG,EAAE;QACvE,YAAY;IAEb;IAEA,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,UAAO,AAAD,EAAG,aAAa;IACrC,IAAK,QAAS;QAEb,MAAM,eAAe,IAAI,QAAQ;QACjC,MAAM,YAAY,aAAa,KAAK;QACpC,MAAM,UAAU,aAAa,UAAU,CAAC,QAAQ;QAEhD,MAAM,QAAQ,cAAc,KAAK;QACjC,MAAM,MAAM,cAAc,UAAU,CAAC,QAAQ;QAE7C,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAG,aAAa;QACpC,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,QAAK,AAAD,EAAG,aAAa;QAElC,oFAAoF;QACpF,yFAAyF;QACzF,QAAQ;QACR,YAAY,IAAI,CAAE,eAAgB,MAAM;QAExC,IAAK,cAAc,UAAU,EAAG;YAE/B,2BAA2B;YAC3B,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,cAAe,cAAc;YAC9D,KAAK,MAAM,CAAC,IAAI,CAAE;YAClB,KAAK,WAAW,GAAG;YAEnB,+CAA+C;YAC/C,MAAM,MAAM,cAAc,UAAU,CAAC,SAAS,CAAE;gBAE/C,kBAAkB,CAAA,MAAO,KAAK,aAAa,CAAE;gBAE7C,oBAAoB,CAAA;oBAEnB,IAAI,CAAC,CAAC,YAAY,CAAE;oBACpB,IAAI,CAAC,CAAC,YAAY,CAAE;oBACpB,IAAI,CAAC,CAAC,YAAY,CAAE;oBACpB,IAAI,WAAW,GAAG;oBAElB,IAAM,IAAI,IAAI,QAAQ,IAAI,QAAQ,QAAQ,IAAI,GAAG,IAAO;wBAEvD,8EAA8E;wBAC9E,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,WAAW,IAAI,IAAI,oBAAoB,CAAE,IAAK,WAAW;wBACtE,UAAU,WAAW,GAAG;wBACxB,IAAK,IAAI,kBAAkB,CAAE,YAAc;4BAE1C,OAAO;wBAER;oBAED;oBAGA,OAAO;gBAER;YAED;YAEA,OAAO;QAER,OAAO;YAEN,0CAA0C;YAC1C,IAAM,IAAI,IAAI,QAAQ,IAAI,QAAQ,QAAQ,IAAI,GAAG,IAAO;gBAEvD,8EAA8E;gBAC9E,MAAM,KAAK,IAAI,oBAAoB,CAAE;gBACrC,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,UAAU,IAAI,IAAI,WAAW;gBAG1C,SAAS,CAAC,CAAC,YAAY,CAAE;gBACzB,SAAS,CAAC,CAAC,YAAY,CAAE;gBACzB,SAAS,CAAC,CAAC,YAAY,CAAE;gBACzB,SAAS,WAAW,GAAG;gBAEvB,IAAM,IAAI,KAAK,GAAG,KAAK,MAAM,KAAK,EAAE,KAAK,IAAI,MAAM,EAAI;oBAEtD,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,WAAW,IAAI,OAAO;oBACnC,UAAU,WAAW,GAAG;oBAExB,IAAK,SAAS,kBAAkB,CAAE,YAAc;wBAE/C,OAAO;oBAER;gBAED;YAED;QAGD;IAED,OAAO;QAEN,MAAM,OAAO,cAAc;QAC3B,MAAM,QAAQ,WAAW,CAAE,cAAc,EAAG;QAE5C,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,OAAQ,cAAc;QACvD,MAAM,mBACL,UAAU,aAAa,CAAE,gBACzB,oBAAqB,MAAM,KAAK,eAAe,eAAe;QAE/D,IAAK,kBAAmB,OAAO;QAE/B,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,QAAS,cAAc;QACxD,MAAM,oBACL,UAAU,aAAa,CAAE,gBACzB,oBAAqB,OAAO,KAAK,eAAe,eAAe;QAEhE,IAAK,mBAAoB,OAAO;QAEhC,OAAO;IAER;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/cast/closestPointToGeometry_indirect.generated.js"], "sourcesContent": ["import { Matrix4, Vector3 } from 'three';\nimport { OrientedBox } from '../../math/OrientedBox.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\nimport { getTriCount } from '../build/geometryUtils.js';\nimport { ExtendedTrianglePool } from '../../utils/ExtendedTrianglePool.js';\n\n/*********************************************************************/\n/* This file is generated from \"closestPointToGeometry.template.js\". */\n/*********************************************************************/\n\nconst tempMatrix = /* @__PURE__ */ new Matrix4();\nconst obb = /* @__PURE__ */ new OrientedBox();\nconst obb2 = /* @__PURE__ */ new OrientedBox();\nconst temp1 = /* @__PURE__ */ new Vector3();\nconst temp2 = /* @__PURE__ */ new Vector3();\nconst temp3 = /* @__PURE__ */ new Vector3();\nconst temp4 = /* @__PURE__ */ new Vector3();\n\nfunction closestPointToGeometry_indirect(\n\tbvh,\n\totherGeometry,\n\tgeometryToBvh,\n\ttarget1 = { },\n\ttarget2 = { },\n\tminThreshold = 0,\n\tmaxThreshold = Infinity,\n) {\n\n\tif ( ! otherGeometry.boundingBox ) {\n\n\t\totherGeometry.computeBoundingBox();\n\n\t}\n\n\tobb.set( otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh );\n\tobb.needsUpdate = true;\n\n\tconst geometry = bvh.geometry;\n\tconst pos = geometry.attributes.position;\n\tconst index = geometry.index;\n\tconst otherPos = otherGeometry.attributes.position;\n\tconst otherIndex = otherGeometry.index;\n\tconst triangle = ExtendedTrianglePool.getPrimitive();\n\tconst triangle2 = ExtendedTrianglePool.getPrimitive();\n\n\tlet tempTarget1 = temp1;\n\tlet tempTargetDest1 = temp2;\n\tlet tempTarget2 = null;\n\tlet tempTargetDest2 = null;\n\n\tif ( target2 ) {\n\n\t\ttempTarget2 = temp3;\n\t\ttempTargetDest2 = temp4;\n\n\t}\n\n\tlet closestDistance = Infinity;\n\tlet closestDistanceTriIndex = null;\n\tlet closestDistanceOtherTriIndex = null;\n\ttempMatrix.copy( geometryToBvh ).invert();\n\tobb2.matrix.copy( tempMatrix );\n\tbvh.shapecast(\n\t\t{\n\n\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\treturn obb.distanceToBox( box );\n\n\t\t\t},\n\n\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\tif ( score < closestDistance && score < maxThreshold ) {\n\n\t\t\t\t\t// if we know the triangles of this bounds will be intersected next then\n\t\t\t\t\t// save the bounds to use during triangle checks.\n\t\t\t\t\tif ( isLeaf ) {\n\n\t\t\t\t\t\tobb2.min.copy( box.min );\n\t\t\t\t\t\tobb2.max.copy( box.max );\n\t\t\t\t\t\tobb2.needsUpdate = true;\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\n\t\t\t},\n\n\t\t\tintersectsRange: ( offset, count ) => {\n\n\t\t\t\tif ( otherGeometry.boundsTree ) {\n\n\t\t\t\t\t// if the other geometry has a bvh then use the accelerated path where we use shapecast to find\n\t\t\t\t\t// the closest bounds in the other geometry to check.\n\t\t\t\t\tconst otherBvh = otherGeometry.boundsTree;\n\t\t\t\t\treturn otherBvh.shapecast( {\n\t\t\t\t\t\tboundsTraverseOrder: box => {\n\n\t\t\t\t\t\t\treturn obb2.distanceToBox( box );\n\n\t\t\t\t\t\t},\n\n\t\t\t\t\t\tintersectsBounds: ( box, isLeaf, score ) => {\n\n\t\t\t\t\t\t\treturn score < closestDistance && score < maxThreshold;\n\n\t\t\t\t\t\t},\n\n\t\t\t\t\t\tintersectsRange: ( otherOffset, otherCount ) => {\n\n\t\t\t\t\t\t\tfor ( let i2 = otherOffset, l2 = otherOffset + otherCount; i2 < l2; i2 ++ ) {\n\n\t\t\t\t\t\t\t\tconst ti2 = otherBvh.resolveTriangleIndex( i2 );\n\t\t\t\t\t\t\t\tsetTriangle( triangle2, 3 * ti2, otherIndex, otherPos );\n\n\t\t\t\t\t\t\t\ttriangle2.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\ttriangle2.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\ttriangle2.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\t\t\tfor ( let i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\t\t\t\t\t\t\t\tconst ti = bvh.resolveTriangleIndex( i );\n\t\t\t\t\t\t\t\t\tsetTriangle( triangle, 3 * ti, index, pos );\n\n\t\t\t\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\t\t\t\tconst dist = triangle.distanceToTriangle( triangle2, tempTarget1, tempTarget2 );\n\t\t\t\t\t\t\t\t\tif ( dist < closestDistance ) {\n\n\t\t\t\t\t\t\t\t\t\ttempTargetDest1.copy( tempTarget1 );\n\n\t\t\t\t\t\t\t\t\t\tif ( tempTargetDest2 ) {\n\n\t\t\t\t\t\t\t\t\t\t\ttempTargetDest2.copy( tempTarget2 );\n\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\tclosestDistance = dist;\n\t\t\t\t\t\t\t\t\t\tclosestDistanceTriIndex = i;\n\t\t\t\t\t\t\t\t\t\tclosestDistanceOtherTriIndex = i2;\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t// stop traversal if we find a point that's under the given threshold\n\t\t\t\t\t\t\t\t\tif ( dist < minThreshold ) {\n\n\t\t\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t},\n\t\t\t\t\t} );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t// If no bounds tree then we'll just check every triangle.\n\t\t\t\t\tconst triCount = getTriCount( otherGeometry );\n\t\t\t\t\tfor ( let i2 = 0, l2 = triCount; i2 < l2; i2 ++ ) {\n\n\t\t\t\t\t\tsetTriangle( triangle2, 3 * i2, otherIndex, otherPos );\n\t\t\t\t\t\ttriangle2.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttriangle2.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttriangle2.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\t\tfor ( let i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\t\t\t\t\t\tconst ti = bvh.resolveTriangleIndex( i );\n\t\t\t\t\t\t\tsetTriangle( triangle, 3 * ti, index, pos );\n\n\t\t\t\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\t\t\t\tconst dist = triangle.distanceToTriangle( triangle2, tempTarget1, tempTarget2 );\n\t\t\t\t\t\t\tif ( dist < closestDistance ) {\n\n\t\t\t\t\t\t\t\ttempTargetDest1.copy( tempTarget1 );\n\n\t\t\t\t\t\t\t\tif ( tempTargetDest2 ) {\n\n\t\t\t\t\t\t\t\t\ttempTargetDest2.copy( tempTarget2 );\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tclosestDistance = dist;\n\t\t\t\t\t\t\t\tclosestDistanceTriIndex = i;\n\t\t\t\t\t\t\t\tclosestDistanceOtherTriIndex = i2;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// stop traversal if we find a point that's under the given threshold\n\t\t\t\t\t\t\tif ( dist < minThreshold ) {\n\n\t\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t},\n\n\t\t}\n\n\t);\n\n\tExtendedTrianglePool.releasePrimitive( triangle );\n\tExtendedTrianglePool.releasePrimitive( triangle2 );\n\n\tif ( closestDistance === Infinity ) {\n\n\t\treturn null;\n\n\t}\n\n\tif ( ! target1.point ) {\n\n\t\ttarget1.point = tempTargetDest1.clone();\n\n\t} else {\n\n\t\ttarget1.point.copy( tempTargetDest1 );\n\n\t}\n\n\ttarget1.distance = closestDistance,\n\ttarget1.faceIndex = closestDistanceTriIndex;\n\n\tif ( target2 ) {\n\n\t\tif ( ! target2.point ) target2.point = tempTargetDest2.clone();\n\t\telse target2.point.copy( tempTargetDest2 );\n\t\ttarget2.point.applyMatrix4( tempMatrix );\n\t\ttempTargetDest1.applyMatrix4( tempMatrix );\n\t\ttarget2.distance = tempTargetDest1.sub( target2.point ).length();\n\t\ttarget2.faceIndex = closestDistanceOtherTriIndex;\n\n\t}\n\n\treturn target1;\n\n}\n\nexport { closestPointToGeometry_indirect };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,qEAAqE,GACrE,qEAAqE,GACrE,qEAAqE,GAErE,MAAM,aAAa,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAC9C,MAAM,MAAM,aAAa,GAAG,IAAI,qKAAA,CAAA,cAAW;AAC3C,MAAM,OAAO,aAAa,GAAG,IAAI,qKAAA,CAAA,cAAW;AAC5C,MAAM,QAAQ,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AACzC,MAAM,QAAQ,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AACzC,MAAM,QAAQ,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AACzC,MAAM,QAAQ,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAEzC,SAAS,gCACR,GAAG,EACH,aAAa,EACb,aAAa,EACb,UAAU,CAAE,CAAC,EACb,UAAU,CAAE,CAAC,EACb,eAAe,CAAC,EAChB,eAAe,QAAQ;IAGvB,IAAK,CAAE,cAAc,WAAW,EAAG;QAElC,cAAc,kBAAkB;IAEjC;IAEA,IAAI,GAAG,CAAE,cAAc,WAAW,CAAC,GAAG,EAAE,cAAc,WAAW,CAAC,GAAG,EAAE;IACvE,IAAI,WAAW,GAAG;IAElB,MAAM,WAAW,IAAI,QAAQ;IAC7B,MAAM,MAAM,SAAS,UAAU,CAAC,QAAQ;IACxC,MAAM,QAAQ,SAAS,KAAK;IAC5B,MAAM,WAAW,cAAc,UAAU,CAAC,QAAQ;IAClD,MAAM,aAAa,cAAc,KAAK;IACtC,MAAM,WAAW,+KAAA,CAAA,uBAAoB,CAAC,YAAY;IAClD,MAAM,YAAY,+KAAA,CAAA,uBAAoB,CAAC,YAAY;IAEnD,IAAI,cAAc;IAClB,IAAI,kBAAkB;IACtB,IAAI,cAAc;IAClB,IAAI,kBAAkB;IAEtB,IAAK,SAAU;QAEd,cAAc;QACd,kBAAkB;IAEnB;IAEA,IAAI,kBAAkB;IACtB,IAAI,0BAA0B;IAC9B,IAAI,+BAA+B;IACnC,WAAW,IAAI,CAAE,eAAgB,MAAM;IACvC,KAAK,MAAM,CAAC,IAAI,CAAE;IAClB,IAAI,SAAS,CACZ;QAEC,qBAAqB,CAAA;YAEpB,OAAO,IAAI,aAAa,CAAE;QAE3B;QAEA,kBAAkB,CAAE,KAAK,QAAQ;YAEhC,IAAK,QAAQ,mBAAmB,QAAQ,cAAe;gBAEtD,wEAAwE;gBACxE,iDAAiD;gBACjD,IAAK,QAAS;oBAEb,KAAK,GAAG,CAAC,IAAI,CAAE,IAAI,GAAG;oBACtB,KAAK,GAAG,CAAC,IAAI,CAAE,IAAI,GAAG;oBACtB,KAAK,WAAW,GAAG;gBAEpB;gBAEA,OAAO;YAER;YAEA,OAAO;QAER;QAEA,iBAAiB,CAAE,QAAQ;YAE1B,IAAK,cAAc,UAAU,EAAG;gBAE/B,+FAA+F;gBAC/F,qDAAqD;gBACrD,MAAM,WAAW,cAAc,UAAU;gBACzC,OAAO,SAAS,SAAS,CAAE;oBAC1B,qBAAqB,CAAA;wBAEpB,OAAO,KAAK,aAAa,CAAE;oBAE5B;oBAEA,kBAAkB,CAAE,KAAK,QAAQ;wBAEhC,OAAO,QAAQ,mBAAmB,QAAQ;oBAE3C;oBAEA,iBAAiB,CAAE,aAAa;wBAE/B,IAAM,IAAI,KAAK,aAAa,KAAK,cAAc,YAAY,KAAK,IAAI,KAAQ;4BAE3E,MAAM,MAAM,SAAS,oBAAoB,CAAE;4BAC3C,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,WAAW,IAAI,KAAK,YAAY;4BAE7C,UAAU,CAAC,CAAC,YAAY,CAAE;4BAC1B,UAAU,CAAC,CAAC,YAAY,CAAE;4BAC1B,UAAU,CAAC,CAAC,YAAY,CAAE;4BAC1B,UAAU,WAAW,GAAG;4BAExB,IAAM,IAAI,IAAI,QAAQ,IAAI,SAAS,OAAO,IAAI,GAAG,IAAO;gCAEvD,MAAM,KAAK,IAAI,oBAAoB,CAAE;gCACrC,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,UAAU,IAAI,IAAI,OAAO;gCAEtC,SAAS,WAAW,GAAG;gCAEvB,MAAM,OAAO,SAAS,kBAAkB,CAAE,WAAW,aAAa;gCAClE,IAAK,OAAO,iBAAkB;oCAE7B,gBAAgB,IAAI,CAAE;oCAEtB,IAAK,iBAAkB;wCAEtB,gBAAgB,IAAI,CAAE;oCAEvB;oCAEA,kBAAkB;oCAClB,0BAA0B;oCAC1B,+BAA+B;gCAEhC;gCAEA,qEAAqE;gCACrE,IAAK,OAAO,cAAe;oCAE1B,OAAO;gCAER;4BAED;wBAED;oBAED;gBACD;YAED,OAAO;gBAEN,0DAA0D;gBAC1D,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAG;gBAC9B,IAAM,IAAI,KAAK,GAAG,KAAK,UAAU,KAAK,IAAI,KAAQ;oBAEjD,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,WAAW,IAAI,IAAI,YAAY;oBAC5C,UAAU,CAAC,CAAC,YAAY,CAAE;oBAC1B,UAAU,CAAC,CAAC,YAAY,CAAE;oBAC1B,UAAU,CAAC,CAAC,YAAY,CAAE;oBAC1B,UAAU,WAAW,GAAG;oBAExB,IAAM,IAAI,IAAI,QAAQ,IAAI,SAAS,OAAO,IAAI,GAAG,IAAO;wBAEvD,MAAM,KAAK,IAAI,oBAAoB,CAAE;wBACrC,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,UAAU,IAAI,IAAI,OAAO;wBAEtC,SAAS,WAAW,GAAG;wBAEvB,MAAM,OAAO,SAAS,kBAAkB,CAAE,WAAW,aAAa;wBAClE,IAAK,OAAO,iBAAkB;4BAE7B,gBAAgB,IAAI,CAAE;4BAEtB,IAAK,iBAAkB;gCAEtB,gBAAgB,IAAI,CAAE;4BAEvB;4BAEA,kBAAkB;4BAClB,0BAA0B;4BAC1B,+BAA+B;wBAEhC;wBAEA,qEAAqE;wBACrE,IAAK,OAAO,cAAe;4BAE1B,OAAO;wBAER;oBAED;gBAED;YAED;QAED;IAED;IAID,+KAAA,CAAA,uBAAoB,CAAC,gBAAgB,CAAE;IACvC,+KAAA,CAAA,uBAAoB,CAAC,gBAAgB,CAAE;IAEvC,IAAK,oBAAoB,UAAW;QAEnC,OAAO;IAER;IAEA,IAAK,CAAE,QAAQ,KAAK,EAAG;QAEtB,QAAQ,KAAK,GAAG,gBAAgB,KAAK;IAEtC,OAAO;QAEN,QAAQ,KAAK,CAAC,IAAI,CAAE;IAErB;IAEA,QAAQ,QAAQ,GAAG,iBACnB,QAAQ,SAAS,GAAG;IAEpB,IAAK,SAAU;QAEd,IAAK,CAAE,QAAQ,KAAK,EAAG,QAAQ,KAAK,GAAG,gBAAgB,KAAK;aACvD,QAAQ,KAAK,CAAC,IAAI,CAAE;QACzB,QAAQ,KAAK,CAAC,YAAY,CAAE;QAC5B,gBAAgB,YAAY,CAAE;QAC9B,QAAQ,QAAQ,GAAG,gBAAgB,GAAG,CAAE,QAAQ,KAAK,EAAG,MAAM;QAC9D,QAAQ,SAAS,GAAG;IAErB;IAEA,OAAO;AAER", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/utils/BufferUtils.js"], "sourcesContent": ["export function isSharedArrayBufferSupported() {\n\n\treturn typeof SharedArrayBuffer !== 'undefined';\n\n}\n\nexport function convertToBufferType( array, BufferConstructor ) {\n\n\tif ( array === null ) {\n\n\t\treturn array;\n\n\t} else if ( array.buffer ) {\n\n\t\tconst buffer = array.buffer;\n\t\tif ( buffer.constructor === BufferConstructor ) {\n\n\t\t\treturn array;\n\n\t\t}\n\n\t\tconst ArrayConstructor = array.constructor;\n\t\tconst result = new ArrayConstructor( new BufferConstructor( buffer.byteLength ) );\n\t\tresult.set( array );\n\t\treturn result;\n\n\t} else {\n\n\t\tif ( array.constructor === BufferConstructor ) {\n\n\t\t\treturn array;\n\n\t\t}\n\n\t\tconst result = new BufferConstructor( array.byteLength );\n\t\tnew Uint8Array( result ).set( new Uint8Array( array ) );\n\t\treturn result;\n\n\t}\n\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS;IAEf,OAAO,OAAO,sBAAsB;AAErC;AAEO,SAAS,oBAAqB,KAAK,EAAE,iBAAiB;IAE5D,IAAK,UAAU,MAAO;QAErB,OAAO;IAER,OAAO,IAAK,MAAM,MAAM,EAAG;QAE1B,MAAM,SAAS,MAAM,MAAM;QAC3B,IAAK,OAAO,WAAW,KAAK,mBAAoB;YAE/C,OAAO;QAER;QAEA,MAAM,mBAAmB,MAAM,WAAW;QAC1C,MAAM,SAAS,IAAI,iBAAkB,IAAI,kBAAmB,OAAO,UAAU;QAC7E,OAAO,GAAG,CAAE;QACZ,OAAO;IAER,OAAO;QAEN,IAAK,MAAM,WAAW,KAAK,mBAAoB;YAE9C,OAAO;QAER;QAEA,MAAM,SAAS,IAAI,kBAAmB,MAAM,UAAU;QACtD,IAAI,WAAY,QAAS,GAAG,CAAE,IAAI,WAAY;QAC9C,OAAO;IAER;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3511, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/cast/bvhcast.js"], "sourcesContent": ["import { Box3, Matrix4 } from 'three';\nimport { BufferStack } from '../utils/BufferStack.js';\nimport { BOUNDING_DATA_INDEX, COUNT, IS_LEAF, LEFT_NODE, OFFSET, RIGHT_NODE } from '../utils/nodeBufferUtils.js';\nimport { arrayToBox } from '../../utils/ArrayBoxUtilities.js';\nimport { PrimitivePool } from '../../utils/PrimitivePool.js';\n\nconst _bufferStack1 = new BufferStack.constructor();\nconst _bufferStack2 = new BufferStack.constructor();\nconst _boxPool = new PrimitivePool( () => new Box3() );\nconst _leftBox1 = new Box3();\nconst _rightBox1 = new Box3();\n\nconst _leftBox2 = new Box3();\nconst _rightBox2 = new Box3();\n\nlet _active = false;\n\nexport function bvhcast( bvh, otherBvh, matrixToLocal, intersectsRanges ) {\n\n\tif ( _active ) {\n\n\t\tthrow new Error( 'MeshBVH: Recursive calls to bvhcast not supported.' );\n\n\t}\n\n\t_active = true;\n\n\tconst roots = bvh._roots;\n\tconst otherRoots = otherBvh._roots;\n\tlet result;\n\tlet offset1 = 0;\n\tlet offset2 = 0;\n\tconst invMat = new Matrix4().copy( matrixToLocal ).invert();\n\n\t// iterate over the first set of roots\n\tfor ( let i = 0, il = roots.length; i < il; i ++ ) {\n\n\t\t_bufferStack1.setBuffer( roots[ i ] );\n\t\toffset2 = 0;\n\n\t\t// prep the initial root box\n\t\tconst localBox = _boxPool.getPrimitive();\n\t\tarrayToBox( BOUNDING_DATA_INDEX( 0 ), _bufferStack1.float32Array, localBox );\n\t\tlocalBox.applyMatrix4( invMat );\n\n\t\t// iterate over the second set of roots\n\t\tfor ( let j = 0, jl = otherRoots.length; j < jl; j ++ ) {\n\n\t\t\t_bufferStack2.setBuffer( otherRoots[ j ] );\n\n\t\t\tresult = _traverse(\n\t\t\t\t0, 0, matrixToLocal, invMat, intersectsRanges,\n\t\t\t\toffset1, offset2, 0, 0,\n\t\t\t\tlocalBox,\n\t\t\t);\n\n\t\t\t_bufferStack2.clearBuffer();\n\t\t\toffset2 += otherRoots[ j ].length;\n\n\t\t\tif ( result ) {\n\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// release stack info\n\t\t_boxPool.releasePrimitive( localBox );\n\t\t_bufferStack1.clearBuffer();\n\t\toffset1 += roots[ i ].length;\n\n\t\tif ( result ) {\n\n\t\t\tbreak;\n\n\t\t}\n\n\t}\n\n\t_active = false;\n\treturn result;\n\n}\n\nfunction _traverse(\n\tnode1Index32,\n\tnode2Index32,\n\tmatrix2to1,\n\tmatrix1to2,\n\tintersectsRangesFunc,\n\n\t// offsets for ids\n\tnode1IndexByteOffset = 0,\n\tnode2IndexByteOffset = 0,\n\n\t// tree depth\n\tdepth1 = 0,\n\tdepth2 = 0,\n\n\tcurrBox = null,\n\treversed = false,\n\n) {\n\n\t// get the buffer stacks associated with the current indices\n\tlet bufferStack1, bufferStack2;\n\tif ( reversed ) {\n\n\t\tbufferStack1 = _bufferStack2;\n\t\tbufferStack2 = _bufferStack1;\n\n\t} else {\n\n\t\tbufferStack1 = _bufferStack1;\n\t\tbufferStack2 = _bufferStack2;\n\n\t}\n\n\t// get the local instances of the typed buffers\n\tconst\n\t\tfloat32Array1 = bufferStack1.float32Array,\n\t\tuint32Array1 = bufferStack1.uint32Array,\n\t\tuint16Array1 = bufferStack1.uint16Array,\n\t\tfloat32Array2 = bufferStack2.float32Array,\n\t\tuint32Array2 = bufferStack2.uint32Array,\n\t\tuint16Array2 = bufferStack2.uint16Array;\n\n\tconst node1Index16 = node1Index32 * 2;\n\tconst node2Index16 = node2Index32 * 2;\n\tconst isLeaf1 = IS_LEAF( node1Index16, uint16Array1 );\n\tconst isLeaf2 = IS_LEAF( node2Index16, uint16Array2 );\n\tlet result = false;\n\tif ( isLeaf2 && isLeaf1 ) {\n\n\t\t// if both bounds are leaf nodes then fire the callback if the boxes intersect\n\t\tif ( reversed ) {\n\n\t\t\tresult = intersectsRangesFunc(\n\t\t\t\tOFFSET( node2Index32, uint32Array2 ), COUNT( node2Index32 * 2, uint16Array2 ),\n\t\t\t\tOFFSET( node1Index32, uint32Array1 ), COUNT( node1Index32 * 2, uint16Array1 ),\n\t\t\t\tdepth2, node2IndexByteOffset + node2Index32,\n\t\t\t\tdepth1, node1IndexByteOffset + node1Index32,\n\t\t\t);\n\n\t\t} else {\n\n\t\t\tresult = intersectsRangesFunc(\n\t\t\t\tOFFSET( node1Index32, uint32Array1 ), COUNT( node1Index32 * 2, uint16Array1 ),\n\t\t\t\tOFFSET( node2Index32, uint32Array2 ), COUNT( node2Index32 * 2, uint16Array2 ),\n\t\t\t\tdepth1, node1IndexByteOffset + node1Index32,\n\t\t\t\tdepth2, node2IndexByteOffset + node2Index32,\n\t\t\t);\n\n\t\t}\n\n\t} else if ( isLeaf2 ) {\n\n\t\t// SWAP\n\t\t// If we've traversed to the leaf node on the other bvh then we need to swap over\n\t\t// to traverse down the first one\n\n\t\t// get the new box to use\n\t\tconst newBox = _boxPool.getPrimitive();\n\t\tarrayToBox( BOUNDING_DATA_INDEX( node2Index32 ), float32Array2, newBox );\n\t\tnewBox.applyMatrix4( matrix2to1 );\n\n\t\t// get the child bounds to check before traversal\n\t\tconst cl1 = LEFT_NODE( node1Index32 );\n\t\tconst cr1 = RIGHT_NODE( node1Index32, uint32Array1 );\n\t\tarrayToBox( BOUNDING_DATA_INDEX( cl1 ), float32Array1, _leftBox1 );\n\t\tarrayToBox( BOUNDING_DATA_INDEX( cr1 ), float32Array1, _rightBox1 );\n\n\t\t// precompute the intersections otherwise the global boxes will be modified during traversal\n\t\tconst intersectCl1 = newBox.intersectsBox( _leftBox1 );\n\t\tconst intersectCr1 = newBox.intersectsBox( _rightBox1 );\n\t\tresult = (\n\t\t\tintersectCl1 && _traverse(\n\t\t\t\tnode2Index32, cl1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\tnewBox, ! reversed,\n\t\t\t)\n\t\t) || (\n\t\t\tintersectCr1 && _traverse(\n\t\t\t\tnode2Index32, cr1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\tnewBox, ! reversed,\n\t\t\t)\n\t\t);\n\n\t\t_boxPool.releasePrimitive( newBox );\n\n\t} else {\n\n\t\t// if neither are leaves then we should swap if one of the children does not\n\t\t// intersect with the current bounds\n\n\t\t// get the child bounds to check\n\t\tconst cl2 = LEFT_NODE( node2Index32 );\n\t\tconst cr2 = RIGHT_NODE( node2Index32, uint32Array2 );\n\t\tarrayToBox( BOUNDING_DATA_INDEX( cl2 ), float32Array2, _leftBox2 );\n\t\tarrayToBox( BOUNDING_DATA_INDEX( cr2 ), float32Array2, _rightBox2 );\n\n\t\tconst leftIntersects = currBox.intersectsBox( _leftBox2 );\n\t\tconst rightIntersects = currBox.intersectsBox( _rightBox2 );\n\t\tif ( leftIntersects && rightIntersects ) {\n\n\t\t\t// continue to traverse both children if they both intersect\n\t\t\tresult = _traverse(\n\t\t\t\tnode1Index32, cl2, matrix2to1, matrix1to2, intersectsRangesFunc,\n\t\t\t\tnode1IndexByteOffset, node2IndexByteOffset, depth1, depth2 + 1,\n\t\t\t\tcurrBox, reversed,\n\t\t\t) || _traverse(\n\t\t\t\tnode1Index32, cr2, matrix2to1, matrix1to2, intersectsRangesFunc,\n\t\t\t\tnode1IndexByteOffset, node2IndexByteOffset, depth1, depth2 + 1,\n\t\t\t\tcurrBox, reversed,\n\t\t\t);\n\n\t\t} else if ( leftIntersects ) {\n\n\t\t\tif ( isLeaf1 ) {\n\n\t\t\t\t// if the current box is a leaf then just continue\n\t\t\t\tresult = _traverse(\n\t\t\t\t\tnode1Index32, cl2, matrix2to1, matrix1to2, intersectsRangesFunc,\n\t\t\t\t\tnode1IndexByteOffset, node2IndexByteOffset, depth1, depth2 + 1,\n\t\t\t\t\tcurrBox, reversed,\n\t\t\t\t);\n\n\t\t\t} else {\n\n\t\t\t\t// SWAP\n\t\t\t\t// if only one box intersects then we have to swap to the other bvh to continue\n\t\t\t\tconst newBox = _boxPool.getPrimitive();\n\t\t\t\tnewBox.copy( _leftBox2 ).applyMatrix4( matrix2to1 );\n\n\t\t\t\tconst cl1 = LEFT_NODE( node1Index32 );\n\t\t\t\tconst cr1 = RIGHT_NODE( node1Index32, uint32Array1 );\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( cl1 ), float32Array1, _leftBox1 );\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( cr1 ), float32Array1, _rightBox1 );\n\n\t\t\t\t// precompute the intersections otherwise the global boxes will be modified during traversal\n\t\t\t\tconst intersectCl1 = newBox.intersectsBox( _leftBox1 );\n\t\t\t\tconst intersectCr1 = newBox.intersectsBox( _rightBox1 );\n\t\t\t\tresult = (\n\t\t\t\t\tintersectCl1 && _traverse(\n\t\t\t\t\t\tcl2, cl1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\t\t\tnewBox, ! reversed,\n\t\t\t\t\t)\n\t\t\t\t) || (\n\t\t\t\t\tintersectCr1 && _traverse(\n\t\t\t\t\t\tcl2, cr1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\t\t\tnewBox, ! reversed,\n\t\t\t\t\t)\n\t\t\t\t);\n\n\t\t\t\t_boxPool.releasePrimitive( newBox );\n\n\t\t\t}\n\n\t\t} else if ( rightIntersects ) {\n\n\t\t\tif ( isLeaf1 ) {\n\n\t\t\t\t// if the current box is a leaf then just continue\n\t\t\t\tresult = _traverse(\n\t\t\t\t\tnode1Index32, cr2, matrix2to1, matrix1to2, intersectsRangesFunc,\n\t\t\t\t\tnode1IndexByteOffset, node2IndexByteOffset, depth1, depth2 + 1,\n\t\t\t\t\tcurrBox, reversed,\n\t\t\t\t);\n\n\t\t\t} else {\n\n\t\t\t\t// SWAP\n\t\t\t\t// if only one box intersects then we have to swap to the other bvh to continue\n\t\t\t\tconst newBox = _boxPool.getPrimitive();\n\t\t\t\tnewBox.copy( _rightBox2 ).applyMatrix4( matrix2to1 );\n\n\t\t\t\tconst cl1 = LEFT_NODE( node1Index32 );\n\t\t\t\tconst cr1 = RIGHT_NODE( node1Index32, uint32Array1 );\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( cl1 ), float32Array1, _leftBox1 );\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( cr1 ), float32Array1, _rightBox1 );\n\n\t\t\t\t// precompute the intersections otherwise the global boxes will be modified during traversal\n\t\t\t\tconst intersectCl1 = newBox.intersectsBox( _leftBox1 );\n\t\t\t\tconst intersectCr1 = newBox.intersectsBox( _rightBox1 );\n\t\t\t\tresult = (\n\t\t\t\t\tintersectCl1 && _traverse(\n\t\t\t\t\t\tcr2, cl1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\t\t\tnewBox, ! reversed,\n\t\t\t\t\t)\n\t\t\t\t) || (\n\t\t\t\t\tintersectCr1 && _traverse(\n\t\t\t\t\t\tcr2, cr1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\t\t\tnewBox, ! reversed,\n\t\t\t\t\t)\n\t\t\t\t);\n\n\t\t\t\t_boxPool.releasePrimitive( newBox );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\treturn result;\n\n}\n\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,IAAI,8KAAA,CAAA,cAAW,CAAC,WAAW;AACjD,MAAM,gBAAgB,IAAI,8KAAA,CAAA,cAAW,CAAC,WAAW;AACjD,MAAM,WAAW,IAAI,wKAAA,CAAA,gBAAa,CAAE,IAAM,IAAI,kJAAA,CAAA,OAAI;AAClD,MAAM,YAAY,IAAI,kJAAA,CAAA,OAAI;AAC1B,MAAM,aAAa,IAAI,kJAAA,CAAA,OAAI;AAE3B,MAAM,YAAY,IAAI,kJAAA,CAAA,OAAI;AAC1B,MAAM,aAAa,IAAI,kJAAA,CAAA,OAAI;AAE3B,IAAI,UAAU;AAEP,SAAS,QAAS,GAAG,EAAE,QAAQ,EAAE,aAAa,EAAE,gBAAgB;IAEtE,IAAK,SAAU;QAEd,MAAM,IAAI,MAAO;IAElB;IAEA,UAAU;IAEV,MAAM,QAAQ,IAAI,MAAM;IACxB,MAAM,aAAa,SAAS,MAAM;IAClC,IAAI;IACJ,IAAI,UAAU;IACd,IAAI,UAAU;IACd,MAAM,SAAS,IAAI,kJAAA,CAAA,UAAO,GAAG,IAAI,CAAE,eAAgB,MAAM;IAEzD,sCAAsC;IACtC,IAAM,IAAI,IAAI,GAAG,KAAK,MAAM,MAAM,EAAE,IAAI,IAAI,IAAO;QAElD,cAAc,SAAS,CAAE,KAAK,CAAE,EAAG;QACnC,UAAU;QAEV,4BAA4B;QAC5B,MAAM,WAAW,SAAS,YAAY;QACtC,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,IAAK,cAAc,YAAY,EAAE;QAClE,SAAS,YAAY,CAAE;QAEvB,uCAAuC;QACvC,IAAM,IAAI,IAAI,GAAG,KAAK,WAAW,MAAM,EAAE,IAAI,IAAI,IAAO;YAEvD,cAAc,SAAS,CAAE,UAAU,CAAE,EAAG;YAExC,SAAS,UACR,GAAG,GAAG,eAAe,QAAQ,kBAC7B,SAAS,SAAS,GAAG,GACrB;YAGD,cAAc,WAAW;YACzB,WAAW,UAAU,CAAE,EAAG,CAAC,MAAM;YAEjC,IAAK,QAAS;gBAEb;YAED;QAED;QAEA,qBAAqB;QACrB,SAAS,gBAAgB,CAAE;QAC3B,cAAc,WAAW;QACzB,WAAW,KAAK,CAAE,EAAG,CAAC,MAAM;QAE5B,IAAK,QAAS;YAEb;QAED;IAED;IAEA,UAAU;IACV,OAAO;AAER;AAEA,SAAS,UACR,YAAY,EACZ,YAAY,EACZ,UAAU,EACV,UAAU,EACV,oBAAoB,EAEpB,kBAAkB;AAClB,uBAAuB,CAAC,EACxB,uBAAuB,CAAC,EAExB,aAAa;AACb,SAAS,CAAC,EACV,SAAS,CAAC,EAEV,UAAU,IAAI,EACd,WAAW,KAAK;IAIhB,4DAA4D;IAC5D,IAAI,cAAc;IAClB,IAAK,UAAW;QAEf,eAAe;QACf,eAAe;IAEhB,OAAO;QAEN,eAAe;QACf,eAAe;IAEhB;IAEA,+CAA+C;IAC/C,MACC,gBAAgB,aAAa,YAAY,EACzC,eAAe,aAAa,WAAW,EACvC,eAAe,aAAa,WAAW,EACvC,gBAAgB,aAAa,YAAY,EACzC,eAAe,aAAa,WAAW,EACvC,eAAe,aAAa,WAAW;IAExC,MAAM,eAAe,eAAe;IACpC,MAAM,eAAe,eAAe;IACpC,MAAM,UAAU,CAAA,GAAA,kLAAA,CAAA,UAAO,AAAD,EAAG,cAAc;IACvC,MAAM,UAAU,CAAA,GAAA,kLAAA,CAAA,UAAO,AAAD,EAAG,cAAc;IACvC,IAAI,SAAS;IACb,IAAK,WAAW,SAAU;QAEzB,8EAA8E;QAC9E,IAAK,UAAW;YAEf,SAAS,qBACR,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAG,cAAc,eAAgB,CAAA,GAAA,kLAAA,CAAA,QAAK,AAAD,EAAG,eAAe,GAAG,eAC/D,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAG,cAAc,eAAgB,CAAA,GAAA,kLAAA,CAAA,QAAK,AAAD,EAAG,eAAe,GAAG,eAC/D,QAAQ,uBAAuB,cAC/B,QAAQ,uBAAuB;QAGjC,OAAO;YAEN,SAAS,qBACR,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAG,cAAc,eAAgB,CAAA,GAAA,kLAAA,CAAA,QAAK,AAAD,EAAG,eAAe,GAAG,eAC/D,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAG,cAAc,eAAgB,CAAA,GAAA,kLAAA,CAAA,QAAK,AAAD,EAAG,eAAe,GAAG,eAC/D,QAAQ,uBAAuB,cAC/B,QAAQ,uBAAuB;QAGjC;IAED,OAAO,IAAK,SAAU;QAErB,OAAO;QACP,iFAAiF;QACjF,iCAAiC;QAEjC,yBAAyB;QACzB,MAAM,SAAS,SAAS,YAAY;QACpC,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,eAAgB,eAAe;QAChE,OAAO,YAAY,CAAE;QAErB,iDAAiD;QACjD,MAAM,MAAM,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAG;QACvB,MAAM,MAAM,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAG,cAAc;QACtC,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,MAAO,eAAe;QACvD,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,MAAO,eAAe;QAEvD,4FAA4F;QAC5F,MAAM,eAAe,OAAO,aAAa,CAAE;QAC3C,MAAM,eAAe,OAAO,aAAa,CAAE;QAC3C,SAAS,AACR,gBAAgB,UACf,cAAc,KAAK,YAAY,YAAY,sBAC3C,sBAAsB,sBAAsB,QAAQ,SAAS,GAC7D,QAAQ,CAAE,aAGX,gBAAgB,UACf,cAAc,KAAK,YAAY,YAAY,sBAC3C,sBAAsB,sBAAsB,QAAQ,SAAS,GAC7D,QAAQ,CAAE;QAIZ,SAAS,gBAAgB,CAAE;IAE5B,OAAO;QAEN,4EAA4E;QAC5E,oCAAoC;QAEpC,gCAAgC;QAChC,MAAM,MAAM,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAG;QACvB,MAAM,MAAM,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAG,cAAc;QACtC,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,MAAO,eAAe;QACvD,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,MAAO,eAAe;QAEvD,MAAM,iBAAiB,QAAQ,aAAa,CAAE;QAC9C,MAAM,kBAAkB,QAAQ,aAAa,CAAE;QAC/C,IAAK,kBAAkB,iBAAkB;YAExC,4DAA4D;YAC5D,SAAS,UACR,cAAc,KAAK,YAAY,YAAY,sBAC3C,sBAAsB,sBAAsB,QAAQ,SAAS,GAC7D,SAAS,aACL,UACJ,cAAc,KAAK,YAAY,YAAY,sBAC3C,sBAAsB,sBAAsB,QAAQ,SAAS,GAC7D,SAAS;QAGX,OAAO,IAAK,gBAAiB;YAE5B,IAAK,SAAU;gBAEd,kDAAkD;gBAClD,SAAS,UACR,cAAc,KAAK,YAAY,YAAY,sBAC3C,sBAAsB,sBAAsB,QAAQ,SAAS,GAC7D,SAAS;YAGX,OAAO;gBAEN,OAAO;gBACP,+EAA+E;gBAC/E,MAAM,SAAS,SAAS,YAAY;gBACpC,OAAO,IAAI,CAAE,WAAY,YAAY,CAAE;gBAEvC,MAAM,MAAM,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAG;gBACvB,MAAM,MAAM,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAG,cAAc;gBACtC,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,MAAO,eAAe;gBACvD,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,MAAO,eAAe;gBAEvD,4FAA4F;gBAC5F,MAAM,eAAe,OAAO,aAAa,CAAE;gBAC3C,MAAM,eAAe,OAAO,aAAa,CAAE;gBAC3C,SAAS,AACR,gBAAgB,UACf,KAAK,KAAK,YAAY,YAAY,sBAClC,sBAAsB,sBAAsB,QAAQ,SAAS,GAC7D,QAAQ,CAAE,aAGX,gBAAgB,UACf,KAAK,KAAK,YAAY,YAAY,sBAClC,sBAAsB,sBAAsB,QAAQ,SAAS,GAC7D,QAAQ,CAAE;gBAIZ,SAAS,gBAAgB,CAAE;YAE5B;QAED,OAAO,IAAK,iBAAkB;YAE7B,IAAK,SAAU;gBAEd,kDAAkD;gBAClD,SAAS,UACR,cAAc,KAAK,YAAY,YAAY,sBAC3C,sBAAsB,sBAAsB,QAAQ,SAAS,GAC7D,SAAS;YAGX,OAAO;gBAEN,OAAO;gBACP,+EAA+E;gBAC/E,MAAM,SAAS,SAAS,YAAY;gBACpC,OAAO,IAAI,CAAE,YAAa,YAAY,CAAE;gBAExC,MAAM,MAAM,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAG;gBACvB,MAAM,MAAM,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAG,cAAc;gBACtC,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,MAAO,eAAe;gBACvD,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG,MAAO,eAAe;gBAEvD,4FAA4F;gBAC5F,MAAM,eAAe,OAAO,aAAa,CAAE;gBAC3C,MAAM,eAAe,OAAO,aAAa,CAAE;gBAC3C,SAAS,AACR,gBAAgB,UACf,KAAK,KAAK,YAAY,YAAY,sBAClC,sBAAsB,sBAAsB,QAAQ,SAAS,GAC7D,QAAQ,CAAE,aAGX,gBAAgB,UACf,KAAK,KAAK,YAAY,YAAY,sBAClC,sBAAsB,sBAAsB,QAAQ,SAAS,GAC7D,QAAQ,CAAE;gBAIZ,SAAS,gBAAgB,CAAE;YAE5B;QAED;IAED;IAEA,OAAO;AAER", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3677, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/core/MeshBVH.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Box3, FrontSide } from 'three';\nimport { CENTER, BYTES_PER_NODE, IS_LEAFNODE_FLAG, SKIP_GENERATION } from './Constants.js';\nimport { buildPackedTree } from './build/buildTree.js';\nimport { OrientedBox } from '../math/OrientedBox.js';\nimport { arrayToBox } from '../utils/ArrayBoxUtilities.js';\nimport { ExtendedTrianglePool } from '../utils/ExtendedTrianglePool.js';\nimport { shapecast } from './cast/shapecast.js';\nimport { closestPointToPoint } from './cast/closestPointToPoint.js';\n\nimport { iterateOverTriangles } from './utils/iterationUtils.generated.js';\nimport { refit } from './cast/refit.generated.js';\nimport { raycast } from './cast/raycast.generated.js';\nimport { raycastFirst } from './cast/raycastFirst.generated.js';\nimport { intersectsGeometry } from './cast/intersectsGeometry.generated.js';\nimport { closestPointToGeometry } from './cast/closestPointToGeometry.generated.js';\n\nimport { iterateOverTriangles_indirect } from './utils/iterationUtils_indirect.generated.js';\nimport { refit_indirect } from './cast/refit_indirect.generated.js';\nimport { raycast_indirect } from './cast/raycast_indirect.generated.js';\nimport { raycastFirst_indirect } from './cast/raycastFirst_indirect.generated.js';\nimport { intersectsGeometry_indirect } from './cast/intersectsGeometry_indirect.generated.js';\nimport { closestPointToGeometry_indirect } from './cast/closestPointToGeometry_indirect.generated.js';\nimport { isSharedArrayBufferSupported } from '../utils/BufferUtils.js';\nimport { setTriangle } from '../utils/TriangleUtilities.js';\nimport { bvhcast } from './cast/bvhcast.js';\n\nconst obb = /* @__PURE__ */ new OrientedBox();\nconst tempBox = /* @__PURE__ */ new Box3();\nexport const DEFAULT_OPTIONS = {\n\tstrategy: CENTER,\n\tmaxDepth: 40,\n\tmaxLeafTris: 10,\n\tuseSharedArrayBuffer: false,\n\tsetBoundingBox: true,\n\tonProgress: null,\n\tindirect: false,\n\tverbose: true,\n\trange: null\n};\n\nexport class MeshBVH {\n\n\tstatic serialize( bvh, options = {} ) {\n\n\t\toptions = {\n\t\t\tcloneBuffers: true,\n\t\t\t...options,\n\t\t};\n\n\t\tconst geometry = bvh.geometry;\n\t\tconst rootData = bvh._roots;\n\t\tconst indirectBuffer = bvh._indirectBuffer;\n\t\tconst indexAttribute = geometry.getIndex();\n\t\tlet result;\n\t\tif ( options.cloneBuffers ) {\n\n\t\t\tresult = {\n\t\t\t\troots: rootData.map( root => root.slice() ),\n\t\t\t\tindex: indexAttribute ? indexAttribute.array.slice() : null,\n\t\t\t\tindirectBuffer: indirectBuffer ? indirectBuffer.slice() : null,\n\t\t\t};\n\n\t\t} else {\n\n\t\t\tresult = {\n\t\t\t\troots: rootData,\n\t\t\t\tindex: indexAttribute ? indexAttribute.array : null,\n\t\t\t\tindirectBuffer: indirectBuffer,\n\t\t\t};\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n\tstatic deserialize( data, geometry, options = {} ) {\n\n\t\toptions = {\n\t\t\tsetIndex: true,\n\t\t\tindirect: Boolean( data.indirectBuffer ),\n\t\t\t...options,\n\t\t};\n\n\t\tconst { index, roots, indirectBuffer } = data;\n\t\tconst bvh = new MeshBVH( geometry, { ...options, [ SKIP_GENERATION ]: true } );\n\t\tbvh._roots = roots;\n\t\tbvh._indirectBuffer = indirectBuffer || null;\n\n\t\tif ( options.setIndex ) {\n\n\t\t\tconst indexAttribute = geometry.getIndex();\n\t\t\tif ( indexAttribute === null ) {\n\n\t\t\t\tconst newIndex = new BufferAttribute( data.index, 1, false );\n\t\t\t\tgeometry.setIndex( newIndex );\n\n\t\t\t} else if ( indexAttribute.array !== index ) {\n\n\t\t\t\tindexAttribute.array.set( index );\n\t\t\t\tindexAttribute.needsUpdate = true;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn bvh;\n\n\t}\n\n\tget indirect() {\n\n\t\treturn ! ! this._indirectBuffer;\n\n\t}\n\n\tconstructor( geometry, options = {} ) {\n\n\t\tif ( ! geometry.isBufferGeometry ) {\n\n\t\t\tthrow new Error( 'MeshBVH: Only BufferGeometries are supported.' );\n\n\t\t} else if ( geometry.index && geometry.index.isInterleavedBufferAttribute ) {\n\n\t\t\tthrow new Error( 'MeshBVH: InterleavedBufferAttribute is not supported for the index attribute.' );\n\n\t\t}\n\n\t\t// default options\n\t\toptions = Object.assign( {\n\n\t\t\t...DEFAULT_OPTIONS,\n\n\t\t\t// undocumented options\n\n\t\t\t// Whether to skip generating the tree. Used for deserialization.\n\t\t\t[ SKIP_GENERATION ]: false,\n\n\t\t}, options );\n\n\t\tif ( options.useSharedArrayBuffer && ! isSharedArrayBufferSupported() ) {\n\n\t\t\tthrow new Error( 'MeshBVH: SharedArrayBuffer is not available.' );\n\n\t\t}\n\n\t\t// retain references to the geometry so we can use them it without having to\n\t\t// take a geometry reference in every function.\n\t\tthis.geometry = geometry;\n\t\tthis._roots = null;\n\t\tthis._indirectBuffer = null;\n\t\tif ( ! options[ SKIP_GENERATION ] ) {\n\n\t\t\tbuildPackedTree( this, options );\n\n\t\t\tif ( ! geometry.boundingBox && options.setBoundingBox ) {\n\n\t\t\t\tgeometry.boundingBox = this.getBoundingBox( new Box3() );\n\n\t\t\t}\n\n\t\t}\n\n\t\tthis.resolveTriangleIndex = options.indirect ? i => this._indirectBuffer[ i ] : i => i;\n\n\t}\n\n\trefit( nodeIndices = null ) {\n\n\t\tconst refitFunc = this.indirect ? refit_indirect : refit;\n\t\treturn refitFunc( this, nodeIndices );\n\n\t}\n\n\ttraverse( callback, rootIndex = 0 ) {\n\n\t\tconst buffer = this._roots[ rootIndex ];\n\t\tconst uint32Array = new Uint32Array( buffer );\n\t\tconst uint16Array = new Uint16Array( buffer );\n\t\t_traverse( 0 );\n\n\t\tfunction _traverse( node32Index, depth = 0 ) {\n\n\t\t\tconst node16Index = node32Index * 2;\n\t\t\tconst isLeaf = uint16Array[ node16Index + 15 ] === IS_LEAFNODE_FLAG;\n\t\t\tif ( isLeaf ) {\n\n\t\t\t\tconst offset = uint32Array[ node32Index + 6 ];\n\t\t\t\tconst count = uint16Array[ node16Index + 14 ];\n\t\t\t\tcallback( depth, isLeaf, new Float32Array( buffer, node32Index * 4, 6 ), offset, count );\n\n\t\t\t} else {\n\n\t\t\t\t// TODO: use node functions here\n\t\t\t\tconst left = node32Index + BYTES_PER_NODE / 4;\n\t\t\t\tconst right = uint32Array[ node32Index + 6 ];\n\t\t\t\tconst splitAxis = uint32Array[ node32Index + 7 ];\n\t\t\t\tconst stopTraversal = callback( depth, isLeaf, new Float32Array( buffer, node32Index * 4, 6 ), splitAxis );\n\n\t\t\t\tif ( ! stopTraversal ) {\n\n\t\t\t\t\t_traverse( left, depth + 1 );\n\t\t\t\t\t_traverse( right, depth + 1 );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/* Core Cast Functions */\n\traycast( ray, materialOrSide = FrontSide, near = 0, far = Infinity ) {\n\n\t\tconst roots = this._roots;\n\t\tconst geometry = this.geometry;\n\t\tconst intersects = [];\n\t\tconst isMaterial = materialOrSide.isMaterial;\n\t\tconst isArrayMaterial = Array.isArray( materialOrSide );\n\n\t\tconst groups = geometry.groups;\n\t\tconst side = isMaterial ? materialOrSide.side : materialOrSide;\n\t\tconst raycastFunc = this.indirect ? raycast_indirect : raycast;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tconst materialSide = isArrayMaterial ? materialOrSide[ groups[ i ].materialIndex ].side : side;\n\t\t\tconst startCount = intersects.length;\n\n\t\t\traycastFunc( this, i, materialSide, ray, intersects, near, far );\n\n\t\t\tif ( isArrayMaterial ) {\n\n\t\t\t\tconst materialIndex = groups[ i ].materialIndex;\n\t\t\t\tfor ( let j = startCount, jl = intersects.length; j < jl; j ++ ) {\n\n\t\t\t\t\tintersects[ j ].face.materialIndex = materialIndex;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn intersects;\n\n\t}\n\n\traycastFirst( ray, materialOrSide = FrontSide, near = 0, far = Infinity ) {\n\n\t\tconst roots = this._roots;\n\t\tconst geometry = this.geometry;\n\t\tconst isMaterial = materialOrSide.isMaterial;\n\t\tconst isArrayMaterial = Array.isArray( materialOrSide );\n\n\t\tlet closestResult = null;\n\n\t\tconst groups = geometry.groups;\n\t\tconst side = isMaterial ? materialOrSide.side : materialOrSide;\n\t\tconst raycastFirstFunc = this.indirect ? raycastFirst_indirect : raycastFirst;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tconst materialSide = isArrayMaterial ? materialOrSide[ groups[ i ].materialIndex ].side : side;\n\t\t\tconst result = raycastFirstFunc( this, i, materialSide, ray, near, far );\n\t\t\tif ( result != null && ( closestResult == null || result.distance < closestResult.distance ) ) {\n\n\t\t\t\tclosestResult = result;\n\t\t\t\tif ( isArrayMaterial ) {\n\n\t\t\t\t\tresult.face.materialIndex = groups[ i ].materialIndex;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn closestResult;\n\n\t}\n\n\tintersectsGeometry( otherGeometry, geomToMesh ) {\n\n\t\tlet result = false;\n\t\tconst roots = this._roots;\n\t\tconst intersectsGeometryFunc = this.indirect ? intersectsGeometry_indirect : intersectsGeometry;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tresult = intersectsGeometryFunc( this, i, otherGeometry, geomToMesh );\n\n\t\t\tif ( result ) {\n\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn result;\n\n\t}\n\n\tshapecast( callbacks ) {\n\n\t\tconst triangle = ExtendedTrianglePool.getPrimitive();\n\t\tconst iterateFunc = this.indirect ? iterateOverTriangles_indirect : iterateOverTriangles;\n\t\tlet {\n\t\t\tboundsTraverseOrder,\n\t\t\tintersectsBounds,\n\t\t\tintersectsRange,\n\t\t\tintersectsTriangle,\n\t\t} = callbacks;\n\n\t\t// wrap the intersectsRange function\n\t\tif ( intersectsRange && intersectsTriangle ) {\n\n\t\t\tconst originalIntersectsRange = intersectsRange;\n\t\t\tintersectsRange = ( offset, count, contained, depth, nodeIndex ) => {\n\n\t\t\t\tif ( ! originalIntersectsRange( offset, count, contained, depth, nodeIndex ) ) {\n\n\t\t\t\t\treturn iterateFunc( offset, count, this, intersectsTriangle, contained, depth, triangle );\n\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\n\t\t\t};\n\n\t\t} else if ( ! intersectsRange ) {\n\n\t\t\tif ( intersectsTriangle ) {\n\n\t\t\t\tintersectsRange = ( offset, count, contained, depth ) => {\n\n\t\t\t\t\treturn iterateFunc( offset, count, this, intersectsTriangle, contained, depth, triangle );\n\n\t\t\t\t};\n\n\t\t\t} else {\n\n\t\t\t\tintersectsRange = ( offset, count, contained ) => {\n\n\t\t\t\t\treturn contained;\n\n\t\t\t\t};\n\n\t\t\t}\n\n\t\t}\n\n\t\t// run shapecast\n\t\tlet result = false;\n\t\tlet byteOffset = 0;\n\t\tconst roots = this._roots;\n\t\tfor ( let i = 0, l = roots.length; i < l; i ++ ) {\n\n\t\t\tconst root = roots[ i ];\n\t\t\tresult = shapecast( this, i, intersectsBounds, intersectsRange, boundsTraverseOrder, byteOffset );\n\n\t\t\tif ( result ) {\n\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t\tbyteOffset += root.byteLength;\n\n\t\t}\n\n\t\tExtendedTrianglePool.releasePrimitive( triangle );\n\n\t\treturn result;\n\n\t}\n\n\tbvhcast( otherBvh, matrixToLocal, callbacks ) {\n\n\t\tlet {\n\t\t\tintersectsRanges,\n\t\t\tintersectsTriangles,\n\t\t} = callbacks;\n\n\t\tconst triangle1 = ExtendedTrianglePool.getPrimitive();\n\t\tconst indexAttr1 = this.geometry.index;\n\t\tconst positionAttr1 = this.geometry.attributes.position;\n\t\tconst assignTriangle1 = this.indirect ?\n\t\t\ti1 => {\n\n\n\t\t\t\tconst ti = this.resolveTriangleIndex( i1 );\n\t\t\t\tsetTriangle( triangle1, ti * 3, indexAttr1, positionAttr1 );\n\n\t\t\t} :\n\t\t\ti1 => {\n\n\t\t\t\tsetTriangle( triangle1, i1 * 3, indexAttr1, positionAttr1 );\n\n\t\t\t};\n\n\t\tconst triangle2 = ExtendedTrianglePool.getPrimitive();\n\t\tconst indexAttr2 = otherBvh.geometry.index;\n\t\tconst positionAttr2 = otherBvh.geometry.attributes.position;\n\t\tconst assignTriangle2 = otherBvh.indirect ?\n\t\t\ti2 => {\n\n\t\t\t\tconst ti2 = otherBvh.resolveTriangleIndex( i2 );\n\t\t\t\tsetTriangle( triangle2, ti2 * 3, indexAttr2, positionAttr2 );\n\n\t\t\t} :\n\t\t\ti2 => {\n\n\t\t\t\tsetTriangle( triangle2, i2 * 3, indexAttr2, positionAttr2 );\n\n\t\t\t};\n\n\t\t// generate triangle callback if needed\n\t\tif ( intersectsTriangles ) {\n\n\t\t\tconst iterateOverDoubleTriangles = ( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) => {\n\n\t\t\t\tfor ( let i2 = offset2, l2 = offset2 + count2; i2 < l2; i2 ++ ) {\n\n\t\t\t\t\tassignTriangle2( i2 );\n\n\t\t\t\t\ttriangle2.a.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.b.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.c.applyMatrix4( matrixToLocal );\n\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\tfor ( let i1 = offset1, l1 = offset1 + count1; i1 < l1; i1 ++ ) {\n\n\t\t\t\t\t\tassignTriangle1( i1 );\n\n\t\t\t\t\t\ttriangle1.needsUpdate = true;\n\n\t\t\t\t\t\tif ( intersectsTriangles( triangle1, triangle2, i1, i2, depth1, index1, depth2, index2 ) ) {\n\n\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\treturn false;\n\n\t\t\t};\n\n\t\t\tif ( intersectsRanges ) {\n\n\t\t\t\tconst originalIntersectsRanges = intersectsRanges;\n\t\t\t\tintersectsRanges = function ( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) {\n\n\t\t\t\t\tif ( ! originalIntersectsRanges( offset1, count1, offset2, count2, depth1, index1, depth2, index2 ) ) {\n\n\t\t\t\t\t\treturn iterateOverDoubleTriangles( offset1, count1, offset2, count2, depth1, index1, depth2, index2 );\n\n\t\t\t\t\t}\n\n\t\t\t\t\treturn true;\n\n\t\t\t\t};\n\n\t\t\t} else {\n\n\t\t\t\tintersectsRanges = iterateOverDoubleTriangles;\n\n\t\t\t}\n\n\t\t}\n\n\t\treturn bvhcast( this, otherBvh, matrixToLocal, intersectsRanges );\n\n\t}\n\n\n\t/* Derived Cast Functions */\n\tintersectsBox( box, boxToMesh ) {\n\n\t\tobb.set( box.min, box.max, boxToMesh );\n\t\tobb.needsUpdate = true;\n\n\t\treturn this.shapecast(\n\t\t\t{\n\t\t\t\tintersectsBounds: box => obb.intersectsBox( box ),\n\t\t\t\tintersectsTriangle: tri => obb.intersectsTriangle( tri )\n\t\t\t}\n\t\t);\n\n\t}\n\n\tintersectsSphere( sphere ) {\n\n\t\treturn this.shapecast(\n\t\t\t{\n\t\t\t\tintersectsBounds: box => sphere.intersectsBox( box ),\n\t\t\t\tintersectsTriangle: tri => tri.intersectsSphere( sphere )\n\t\t\t}\n\t\t);\n\n\t}\n\n\tclosestPointToGeometry( otherGeometry, geometryToBvh, target1 = { }, target2 = { }, minThreshold = 0, maxThreshold = Infinity ) {\n\n\t\tconst closestPointToGeometryFunc = this.indirect ? closestPointToGeometry_indirect : closestPointToGeometry;\n\t\treturn closestPointToGeometryFunc(\n\t\t\tthis,\n\t\t\totherGeometry,\n\t\t\tgeometryToBvh,\n\t\t\ttarget1,\n\t\t\ttarget2,\n\t\t\tminThreshold,\n\t\t\tmaxThreshold,\n\t\t);\n\n\t}\n\n\tclosestPointToPoint( point, target = { }, minThreshold = 0, maxThreshold = Infinity ) {\n\n\t\treturn closestPointToPoint(\n\t\t\tthis,\n\t\t\tpoint,\n\t\t\ttarget,\n\t\t\tminThreshold,\n\t\t\tmaxThreshold,\n\t\t);\n\n\t}\n\n\tgetBoundingBox( target ) {\n\n\t\ttarget.makeEmpty();\n\n\t\tconst roots = this._roots;\n\t\troots.forEach( buffer => {\n\n\t\t\tarrayToBox( 0, new Float32Array( buffer ), tempBox );\n\t\t\ttarget.union( tempBox );\n\n\t\t} );\n\n\t\treturn target;\n\n\t}\n\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,MAAM,aAAa,GAAG,IAAI,qKAAA,CAAA,cAAW;AAC3C,MAAM,UAAU,aAAa,GAAG,IAAI,kJAAA,CAAA,OAAI;AACjC,MAAM,kBAAkB;IAC9B,UAAU,mKAAA,CAAA,SAAM;IAChB,UAAU;IACV,aAAa;IACb,sBAAsB;IACtB,gBAAgB;IAChB,YAAY;IACZ,UAAU;IACV,SAAS;IACT,OAAO;AACR;AAEO,MAAM;IAEZ,OAAO,UAAW,GAAG,EAAE,UAAU,CAAC,CAAC,EAAG;QAErC,UAAU;YACT,cAAc;YACd,GAAG,OAAO;QACX;QAEA,MAAM,WAAW,IAAI,QAAQ;QAC7B,MAAM,WAAW,IAAI,MAAM;QAC3B,MAAM,iBAAiB,IAAI,eAAe;QAC1C,MAAM,iBAAiB,SAAS,QAAQ;QACxC,IAAI;QACJ,IAAK,QAAQ,YAAY,EAAG;YAE3B,SAAS;gBACR,OAAO,SAAS,GAAG,CAAE,CAAA,OAAQ,KAAK,KAAK;gBACvC,OAAO,iBAAiB,eAAe,KAAK,CAAC,KAAK,KAAK;gBACvD,gBAAgB,iBAAiB,eAAe,KAAK,KAAK;YAC3D;QAED,OAAO;YAEN,SAAS;gBACR,OAAO;gBACP,OAAO,iBAAiB,eAAe,KAAK,GAAG;gBAC/C,gBAAgB;YACjB;QAED;QAEA,OAAO;IAER;IAEA,OAAO,YAAa,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC,EAAG;QAElD,UAAU;YACT,UAAU;YACV,UAAU,QAAS,KAAK,cAAc;YACtC,GAAG,OAAO;QACX;QAEA,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG;QACzC,MAAM,MAAM,IAAI,QAAS,UAAU;YAAE,GAAG,OAAO;YAAE,CAAE,mKAAA,CAAA,kBAAe,CAAE,EAAE;QAAK;QAC3E,IAAI,MAAM,GAAG;QACb,IAAI,eAAe,GAAG,kBAAkB;QAExC,IAAK,QAAQ,QAAQ,EAAG;YAEvB,MAAM,iBAAiB,SAAS,QAAQ;YACxC,IAAK,mBAAmB,MAAO;gBAE9B,MAAM,WAAW,IAAI,kJAAA,CAAA,kBAAe,CAAE,KAAK,KAAK,EAAE,GAAG;gBACrD,SAAS,QAAQ,CAAE;YAEpB,OAAO,IAAK,eAAe,KAAK,KAAK,OAAQ;gBAE5C,eAAe,KAAK,CAAC,GAAG,CAAE;gBAC1B,eAAe,WAAW,GAAG;YAE9B;QAED;QAEA,OAAO;IAER;IAEA,IAAI,WAAW;QAEd,OAAO,CAAE,CAAE,IAAI,CAAC,eAAe;IAEhC;IAEA,YAAa,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAG;QAErC,IAAK,CAAE,SAAS,gBAAgB,EAAG;YAElC,MAAM,IAAI,MAAO;QAElB,OAAO,IAAK,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC,4BAA4B,EAAG;YAE3E,MAAM,IAAI,MAAO;QAElB;QAEA,kBAAkB;QAClB,UAAU,OAAO,MAAM,CAAE;YAExB,GAAG,eAAe;YAElB,uBAAuB;YAEvB,iEAAiE;YACjE,CAAE,mKAAA,CAAA,kBAAe,CAAE,EAAE;QAEtB,GAAG;QAEH,IAAK,QAAQ,oBAAoB,IAAI,CAAE,CAAA,GAAA,sKAAA,CAAA,+BAA4B,AAAD,KAAM;YAEvE,MAAM,IAAI,MAAO;QAElB;QAEA,4EAA4E;QAC5E,+CAA+C;QAC/C,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,eAAe,GAAG;QACvB,IAAK,CAAE,OAAO,CAAE,mKAAA,CAAA,kBAAe,CAAE,EAAG;YAEnC,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAG,IAAI,EAAE;YAEvB,IAAK,CAAE,SAAS,WAAW,IAAI,QAAQ,cAAc,EAAG;gBAEvD,SAAS,WAAW,GAAG,IAAI,CAAC,cAAc,CAAE,IAAI,kJAAA,CAAA,OAAI;YAErD;QAED;QAEA,IAAI,CAAC,oBAAoB,GAAG,QAAQ,QAAQ,GAAG,CAAA,IAAK,IAAI,CAAC,eAAe,CAAE,EAAG,GAAG,CAAA,IAAK;IAEtF;IAEA,MAAO,cAAc,IAAI,EAAG;QAE3B,MAAM,YAAY,IAAI,CAAC,QAAQ,GAAG,6LAAA,CAAA,iBAAc,GAAG,oLAAA,CAAA,QAAK;QACxD,OAAO,UAAW,IAAI,EAAE;IAEzB;IAEA,SAAU,QAAQ,EAAE,YAAY,CAAC,EAAG;QAEnC,MAAM,SAAS,IAAI,CAAC,MAAM,CAAE,UAAW;QACvC,MAAM,cAAc,IAAI,YAAa;QACrC,MAAM,cAAc,IAAI,YAAa;QACrC,UAAW;QAEX,SAAS,UAAW,WAAW,EAAE,QAAQ,CAAC;YAEzC,MAAM,cAAc,cAAc;YAClC,MAAM,SAAS,WAAW,CAAE,cAAc,GAAI,KAAK,mKAAA,CAAA,mBAAgB;YACnE,IAAK,QAAS;gBAEb,MAAM,SAAS,WAAW,CAAE,cAAc,EAAG;gBAC7C,MAAM,QAAQ,WAAW,CAAE,cAAc,GAAI;gBAC7C,SAAU,OAAO,QAAQ,IAAI,aAAc,QAAQ,cAAc,GAAG,IAAK,QAAQ;YAElF,OAAO;gBAEN,gCAAgC;gBAChC,MAAM,OAAO,cAAc,mKAAA,CAAA,iBAAc,GAAG;gBAC5C,MAAM,QAAQ,WAAW,CAAE,cAAc,EAAG;gBAC5C,MAAM,YAAY,WAAW,CAAE,cAAc,EAAG;gBAChD,MAAM,gBAAgB,SAAU,OAAO,QAAQ,IAAI,aAAc,QAAQ,cAAc,GAAG,IAAK;gBAE/F,IAAK,CAAE,eAAgB;oBAEtB,UAAW,MAAM,QAAQ;oBACzB,UAAW,OAAO,QAAQ;gBAE3B;YAED;QAED;IAED;IAEA,uBAAuB,GACvB,QAAS,GAAG,EAAE,iBAAiB,kJAAA,CAAA,YAAS,EAAE,OAAO,CAAC,EAAE,MAAM,QAAQ,EAAG;QAEpE,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,MAAM,WAAW,IAAI,CAAC,QAAQ;QAC9B,MAAM,aAAa,EAAE;QACrB,MAAM,aAAa,eAAe,UAAU;QAC5C,MAAM,kBAAkB,MAAM,OAAO,CAAE;QAEvC,MAAM,SAAS,SAAS,MAAM;QAC9B,MAAM,OAAO,aAAa,eAAe,IAAI,GAAG;QAChD,MAAM,cAAc,IAAI,CAAC,QAAQ,GAAG,+LAAA,CAAA,mBAAgB,GAAG,sLAAA,CAAA,UAAO;QAC9D,IAAM,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,IAAO;YAEhD,MAAM,eAAe,kBAAkB,cAAc,CAAE,MAAM,CAAE,EAAG,CAAC,aAAa,CAAE,CAAC,IAAI,GAAG;YAC1F,MAAM,aAAa,WAAW,MAAM;YAEpC,YAAa,IAAI,EAAE,GAAG,cAAc,KAAK,YAAY,MAAM;YAE3D,IAAK,iBAAkB;gBAEtB,MAAM,gBAAgB,MAAM,CAAE,EAAG,CAAC,aAAa;gBAC/C,IAAM,IAAI,IAAI,YAAY,KAAK,WAAW,MAAM,EAAE,IAAI,IAAI,IAAO;oBAEhE,UAAU,CAAE,EAAG,CAAC,IAAI,CAAC,aAAa,GAAG;gBAEtC;YAED;QAED;QAEA,OAAO;IAER;IAEA,aAAc,GAAG,EAAE,iBAAiB,kJAAA,CAAA,YAAS,EAAE,OAAO,CAAC,EAAE,MAAM,QAAQ,EAAG;QAEzE,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,MAAM,WAAW,IAAI,CAAC,QAAQ;QAC9B,MAAM,aAAa,eAAe,UAAU;QAC5C,MAAM,kBAAkB,MAAM,OAAO,CAAE;QAEvC,IAAI,gBAAgB;QAEpB,MAAM,SAAS,SAAS,MAAM;QAC9B,MAAM,OAAO,aAAa,eAAe,IAAI,GAAG;QAChD,MAAM,mBAAmB,IAAI,CAAC,QAAQ,GAAG,oMAAA,CAAA,wBAAqB,GAAG,2LAAA,CAAA,eAAY;QAC7E,IAAM,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,IAAO;YAEhD,MAAM,eAAe,kBAAkB,cAAc,CAAE,MAAM,CAAE,EAAG,CAAC,aAAa,CAAE,CAAC,IAAI,GAAG;YAC1F,MAAM,SAAS,iBAAkB,IAAI,EAAE,GAAG,cAAc,KAAK,MAAM;YACnE,IAAK,UAAU,QAAQ,CAAE,iBAAiB,QAAQ,OAAO,QAAQ,GAAG,cAAc,QAAQ,AAAC,GAAI;gBAE9F,gBAAgB;gBAChB,IAAK,iBAAkB;oBAEtB,OAAO,IAAI,CAAC,aAAa,GAAG,MAAM,CAAE,EAAG,CAAC,aAAa;gBAEtD;YAED;QAED;QAEA,OAAO;IAER;IAEA,mBAAoB,aAAa,EAAE,UAAU,EAAG;QAE/C,IAAI,SAAS;QACb,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,MAAM,yBAAyB,IAAI,CAAC,QAAQ,GAAG,0MAAA,CAAA,8BAA2B,GAAG,iMAAA,CAAA,qBAAkB;QAC/F,IAAM,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,IAAO;YAEhD,SAAS,uBAAwB,IAAI,EAAE,GAAG,eAAe;YAEzD,IAAK,QAAS;gBAEb;YAED;QAED;QAEA,OAAO;IAER;IAEA,UAAW,SAAS,EAAG;QAEtB,MAAM,WAAW,+KAAA,CAAA,uBAAoB,CAAC,YAAY;QAClD,MAAM,cAAc,IAAI,CAAC,QAAQ,GAAG,uMAAA,CAAA,gCAA6B,GAAG,8LAAA,CAAA,uBAAoB;QACxF,IAAI,EACH,mBAAmB,EACnB,gBAAgB,EAChB,eAAe,EACf,kBAAkB,EAClB,GAAG;QAEJ,oCAAoC;QACpC,IAAK,mBAAmB,oBAAqB;YAE5C,MAAM,0BAA0B;YAChC,kBAAkB,CAAE,QAAQ,OAAO,WAAW,OAAO;gBAEpD,IAAK,CAAE,wBAAyB,QAAQ,OAAO,WAAW,OAAO,YAAc;oBAE9E,OAAO,YAAa,QAAQ,OAAO,IAAI,EAAE,oBAAoB,WAAW,OAAO;gBAEhF;gBAEA,OAAO;YAER;QAED,OAAO,IAAK,CAAE,iBAAkB;YAE/B,IAAK,oBAAqB;gBAEzB,kBAAkB,CAAE,QAAQ,OAAO,WAAW;oBAE7C,OAAO,YAAa,QAAQ,OAAO,IAAI,EAAE,oBAAoB,WAAW,OAAO;gBAEhF;YAED,OAAO;gBAEN,kBAAkB,CAAE,QAAQ,OAAO;oBAElC,OAAO;gBAER;YAED;QAED;QAEA,gBAAgB;QAChB,IAAI,SAAS;QACb,IAAI,aAAa;QACjB,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,IAAM,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,IAAO;YAEhD,MAAM,OAAO,KAAK,CAAE,EAAG;YACvB,SAAS,CAAA,GAAA,2KAAA,CAAA,YAAS,AAAD,EAAG,IAAI,EAAE,GAAG,kBAAkB,iBAAiB,qBAAqB;YAErF,IAAK,QAAS;gBAEb;YAED;YAEA,cAAc,KAAK,UAAU;QAE9B;QAEA,+KAAA,CAAA,uBAAoB,CAAC,gBAAgB,CAAE;QAEvC,OAAO;IAER;IAEA,QAAS,QAAQ,EAAE,aAAa,EAAE,SAAS,EAAG;QAE7C,IAAI,EACH,gBAAgB,EAChB,mBAAmB,EACnB,GAAG;QAEJ,MAAM,YAAY,+KAAA,CAAA,uBAAoB,CAAC,YAAY;QACnD,MAAM,aAAa,IAAI,CAAC,QAAQ,CAAC,KAAK;QACtC,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ;QACvD,MAAM,kBAAkB,IAAI,CAAC,QAAQ,GACpC,CAAA;YAGC,MAAM,KAAK,IAAI,CAAC,oBAAoB,CAAE;YACtC,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,WAAW,KAAK,GAAG,YAAY;QAE7C,IACA,CAAA;YAEC,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,WAAW,KAAK,GAAG,YAAY;QAE7C;QAED,MAAM,YAAY,+KAAA,CAAA,uBAAoB,CAAC,YAAY;QACnD,MAAM,aAAa,SAAS,QAAQ,CAAC,KAAK;QAC1C,MAAM,gBAAgB,SAAS,QAAQ,CAAC,UAAU,CAAC,QAAQ;QAC3D,MAAM,kBAAkB,SAAS,QAAQ,GACxC,CAAA;YAEC,MAAM,MAAM,SAAS,oBAAoB,CAAE;YAC3C,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,WAAW,MAAM,GAAG,YAAY;QAE9C,IACA,CAAA;YAEC,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAG,WAAW,KAAK,GAAG,YAAY;QAE7C;QAED,uCAAuC;QACvC,IAAK,qBAAsB;YAE1B,MAAM,6BAA6B,CAAE,SAAS,QAAQ,SAAS,QAAQ,QAAQ,QAAQ,QAAQ;gBAE9F,IAAM,IAAI,KAAK,SAAS,KAAK,UAAU,QAAQ,KAAK,IAAI,KAAQ;oBAE/D,gBAAiB;oBAEjB,UAAU,CAAC,CAAC,YAAY,CAAE;oBAC1B,UAAU,CAAC,CAAC,YAAY,CAAE;oBAC1B,UAAU,CAAC,CAAC,YAAY,CAAE;oBAC1B,UAAU,WAAW,GAAG;oBAExB,IAAM,IAAI,KAAK,SAAS,KAAK,UAAU,QAAQ,KAAK,IAAI,KAAQ;wBAE/D,gBAAiB;wBAEjB,UAAU,WAAW,GAAG;wBAExB,IAAK,oBAAqB,WAAW,WAAW,IAAI,IAAI,QAAQ,QAAQ,QAAQ,SAAW;4BAE1F,OAAO;wBAER;oBAED;gBAED;gBAEA,OAAO;YAER;YAEA,IAAK,kBAAmB;gBAEvB,MAAM,2BAA2B;gBACjC,mBAAmB,SAAW,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;oBAE7F,IAAK,CAAE,yBAA0B,SAAS,QAAQ,SAAS,QAAQ,QAAQ,QAAQ,QAAQ,SAAW;wBAErG,OAAO,2BAA4B,SAAS,QAAQ,SAAS,QAAQ,QAAQ,QAAQ,QAAQ;oBAE9F;oBAEA,OAAO;gBAER;YAED,OAAO;gBAEN,mBAAmB;YAEpB;QAED;QAEA,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAO,AAAD,EAAG,IAAI,EAAE,UAAU,eAAe;IAEhD;IAGA,0BAA0B,GAC1B,cAAe,GAAG,EAAE,SAAS,EAAG;QAE/B,IAAI,GAAG,CAAE,IAAI,GAAG,EAAE,IAAI,GAAG,EAAE;QAC3B,IAAI,WAAW,GAAG;QAElB,OAAO,IAAI,CAAC,SAAS,CACpB;YACC,kBAAkB,CAAA,MAAO,IAAI,aAAa,CAAE;YAC5C,oBAAoB,CAAA,MAAO,IAAI,kBAAkB,CAAE;QACpD;IAGF;IAEA,iBAAkB,MAAM,EAAG;QAE1B,OAAO,IAAI,CAAC,SAAS,CACpB;YACC,kBAAkB,CAAA,MAAO,OAAO,aAAa,CAAE;YAC/C,oBAAoB,CAAA,MAAO,IAAI,gBAAgB,CAAE;QAClD;IAGF;IAEA,uBAAwB,aAAa,EAAE,aAAa,EAAE,UAAU,CAAE,CAAC,EAAE,UAAU,CAAE,CAAC,EAAE,eAAe,CAAC,EAAE,eAAe,QAAQ,EAAG;QAE/H,MAAM,6BAA6B,IAAI,CAAC,QAAQ,GAAG,8MAAA,CAAA,kCAA+B,GAAG,qMAAA,CAAA,yBAAsB;QAC3G,OAAO,2BACN,IAAI,EACJ,eACA,eACA,SACA,SACA,cACA;IAGF;IAEA,oBAAqB,KAAK,EAAE,SAAS,CAAE,CAAC,EAAE,eAAe,CAAC,EAAE,eAAe,QAAQ,EAAG;QAErF,OAAO,CAAA,GAAA,qLAAA,CAAA,sBAAmB,AAAD,EACxB,IAAI,EACJ,OACA,QACA,cACA;IAGF;IAEA,eAAgB,MAAM,EAAG;QAExB,OAAO,SAAS;QAEhB,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,MAAM,OAAO,CAAE,CAAA;YAEd,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAG,GAAG,IAAI,aAAc,SAAU;YAC3C,OAAO,KAAK,CAAE;QAEf;QAEA,OAAO;IAER;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4036, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/utils/ExtensionUtilities.js"], "sourcesContent": ["import { Ray, <PERSON>4, <PERSON>sh, Vector3, <PERSON>phere, BatchedMesh, REVISION } from 'three';\nimport { convertRaycastIntersect } from './GeometryRayIntersectUtilities.js';\nimport { MeshBVH } from '../core/MeshBVH.js';\n\nconst IS_REVISION_166 = parseInt( REVISION ) >= 166;\nconst ray = /* @__PURE__ */ new Ray();\nconst direction = /* @__PURE__ */ new Vector3();\nconst tmpInverseMatrix = /* @__PURE__ */ new Matrix4();\nconst origMeshRaycastFunc = Mesh.prototype.raycast;\nconst origBatchedRaycastFunc = BatchedMesh.prototype.raycast;\nconst _worldScale = /* @__PURE__ */ new Vector3();\nconst _mesh = /* @__PURE__ */ new Mesh();\nconst _batchIntersects = [];\n\nexport function acceleratedRaycast( raycaster, intersects ) {\n\n\tif ( this.isBatchedMesh ) {\n\n\t\tacceleratedBatchedMeshRaycast.call( this, raycaster, intersects );\n\n\t} else {\n\n\t\tacceleratedMeshRaycast.call( this, raycaster, intersects );\n\n\t}\n\n}\n\nfunction acceleratedBatchedMeshRaycast( raycaster, intersects ) {\n\n\tif ( this.boundsTrees ) {\n\n\t\t// TODO: remove use of geometry info, instance info when r170 is minimum version\n\t\tconst boundsTrees = this.boundsTrees;\n\t\tconst drawInfo = this._drawInfo || this._instanceInfo;\n\t\tconst drawRanges = this._drawRanges || this._geometryInfo;\n\t\tconst matrixWorld = this.matrixWorld;\n\n\t\t_mesh.material = this.material;\n\t\t_mesh.geometry = this.geometry;\n\n\t\tconst oldBoundsTree = _mesh.geometry.boundsTree;\n\t\tconst oldDrawRange = _mesh.geometry.drawRange;\n\n\t\tif ( _mesh.geometry.boundingSphere === null ) {\n\n\t\t\t_mesh.geometry.boundingSphere = new Sphere();\n\n\t\t}\n\n\t\t// TODO: provide new method to get instances count instead of 'drawInfo.length'\n\t\tfor ( let i = 0, l = drawInfo.length; i < l; i ++ ) {\n\n\t\t\tif ( ! this.getVisibleAt( i ) ) {\n\n\t\t\t\tcontinue;\n\n\t\t\t}\n\n\t\t\t// TODO: use getGeometryIndex\n\t\t\tconst geometryId = drawInfo[ i ].geometryIndex;\n\n\t\t\t_mesh.geometry.boundsTree = boundsTrees[ geometryId ];\n\n\t\t\tthis.getMatrixAt( i, _mesh.matrixWorld ).premultiply( matrixWorld );\n\n\t\t\tif ( ! _mesh.geometry.boundsTree ) {\n\n\t\t\t\tthis.getBoundingBoxAt( geometryId, _mesh.geometry.boundingBox );\n\t\t\t\tthis.getBoundingSphereAt( geometryId, _mesh.geometry.boundingSphere );\n\n\t\t\t\tconst drawRange = drawRanges[ geometryId ];\n\t\t\t\t_mesh.geometry.setDrawRange( drawRange.start, drawRange.count );\n\n\t\t\t}\n\n\t\t\t_mesh.raycast( raycaster, _batchIntersects );\n\n\t\t\tfor ( let j = 0, l = _batchIntersects.length; j < l; j ++ ) {\n\n\t\t\t\tconst intersect = _batchIntersects[ j ];\n\t\t\t\tintersect.object = this;\n\t\t\t\tintersect.batchId = i;\n\t\t\t\tintersects.push( intersect );\n\n\t\t\t}\n\n\t\t\t_batchIntersects.length = 0;\n\n\t\t}\n\n\t\t_mesh.geometry.boundsTree = oldBoundsTree;\n\t\t_mesh.geometry.drawRange = oldDrawRange;\n\t\t_mesh.material = null;\n\t\t_mesh.geometry = null;\n\n\t} else {\n\n\t\torigBatchedRaycastFunc.call( this, raycaster, intersects );\n\n\t}\n\n}\n\nfunction acceleratedMeshRaycast( raycaster, intersects ) {\n\n\tif ( this.geometry.boundsTree ) {\n\n\t\tif ( this.material === undefined ) return;\n\n\t\ttmpInverseMatrix.copy( this.matrixWorld ).invert();\n\t\tray.copy( raycaster.ray ).applyMatrix4( tmpInverseMatrix );\n\n\t\t_worldScale.setFromMatrixScale( this.matrixWorld );\n\t\tdirection.copy( ray.direction ).multiply( _worldScale );\n\n\t\tconst scaleFactor = direction.length();\n\t\tconst near = raycaster.near / scaleFactor;\n\t\tconst far = raycaster.far / scaleFactor;\n\n\t\tconst bvh = this.geometry.boundsTree;\n\t\tif ( raycaster.firstHitOnly === true ) {\n\n\t\t\tconst hit = convertRaycastIntersect( bvh.raycastFirst( ray, this.material, near, far ), this, raycaster );\n\t\t\tif ( hit ) {\n\n\t\t\t\tintersects.push( hit );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tconst hits = bvh.raycast( ray, this.material, near, far );\n\t\t\tfor ( let i = 0, l = hits.length; i < l; i ++ ) {\n\n\t\t\t\tconst hit = convertRaycastIntersect( hits[ i ], this, raycaster );\n\t\t\t\tif ( hit ) {\n\n\t\t\t\t\tintersects.push( hit );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t} else {\n\n\t\torigMeshRaycastFunc.call( this, raycaster, intersects );\n\n\t}\n\n}\n\nexport function computeBoundsTree( options = {} ) {\n\n\tthis.boundsTree = new MeshBVH( this, options );\n\treturn this.boundsTree;\n\n}\n\nexport function disposeBoundsTree() {\n\n\tthis.boundsTree = null;\n\n}\n\nexport function computeBatchedBoundsTree( index = - 1, options = {} ) {\n\n\tif ( ! IS_REVISION_166 ) {\n\n\t\tthrow new Error( 'BatchedMesh: Three r166+ is required to compute bounds trees.' );\n\n\t}\n\n\tif ( options.indirect ) {\n\n\t\tconsole.warn( '\"Indirect\" is set to false because it is not supported for BatchedMesh.' );\n\n\t}\n\n\toptions = {\n\t\t...options,\n\t\tindirect: false,\n\t\trange: null\n\t};\n\n\tconst drawRanges = this._drawRanges || this._geometryInfo;\n\tconst geometryCount = this._geometryCount;\n\tif ( ! this.boundsTrees ) {\n\n\t\tthis.boundsTrees = new Array( geometryCount ).fill( null );\n\n\t}\n\n\tconst boundsTrees = this.boundsTrees;\n\twhile ( boundsTrees.length < geometryCount ) {\n\n\t\tboundsTrees.push( null );\n\n\t}\n\n\tif ( index < 0 ) {\n\n\t\tfor ( let i = 0; i < geometryCount; i ++ ) {\n\n\t\t\toptions.range = drawRanges[ i ];\n\t\t\tboundsTrees[ i ] = new MeshBVH( this.geometry, options );\n\n\t\t}\n\n\t\treturn boundsTrees;\n\n\t} else {\n\n\t\tif ( index < drawRanges.length ) {\n\n\t\t\toptions.range = drawRanges[ index ];\n\t\t\tboundsTrees[ index ] = new MeshBVH( this.geometry, options );\n\n\t\t}\n\n\t\treturn boundsTrees[ index ] || null;\n\n\t}\n\n}\n\nexport function disposeBatchedBoundsTree( index = - 1 ) {\n\n\tif ( index < 0 ) {\n\n\t\tthis.boundsTrees.fill( null );\n\n\t} else {\n\n\t\tif ( index < this.boundsTree.length ) {\n\n\t\t\tthis.boundsTrees[ index ] = null;\n\n\t\t}\n\n\t}\n\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,kBAAkB,SAAU,kJAAA,CAAA,WAAQ,KAAM;AAChD,MAAM,MAAM,aAAa,GAAG,IAAI,kJAAA,CAAA,MAAG;AACnC,MAAM,YAAY,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAC7C,MAAM,mBAAmB,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AACpD,MAAM,sBAAsB,kJAAA,CAAA,OAAI,CAAC,SAAS,CAAC,OAAO;AAClD,MAAM,yBAAyB,kJAAA,CAAA,cAAW,CAAC,SAAS,CAAC,OAAO;AAC5D,MAAM,cAAc,aAAa,GAAG,IAAI,kJAAA,CAAA,UAAO;AAC/C,MAAM,QAAQ,aAAa,GAAG,IAAI,kJAAA,CAAA,OAAI;AACtC,MAAM,mBAAmB,EAAE;AAEpB,SAAS,mBAAoB,SAAS,EAAE,UAAU;IAExD,IAAK,IAAI,CAAC,aAAa,EAAG;QAEzB,8BAA8B,IAAI,CAAE,IAAI,EAAE,WAAW;IAEtD,OAAO;QAEN,uBAAuB,IAAI,CAAE,IAAI,EAAE,WAAW;IAE/C;AAED;AAEA,SAAS,8BAA+B,SAAS,EAAE,UAAU;IAE5D,IAAK,IAAI,CAAC,WAAW,EAAG;QAEvB,gFAAgF;QAChF,MAAM,cAAc,IAAI,CAAC,WAAW;QACpC,MAAM,WAAW,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa;QACrD,MAAM,aAAa,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa;QACzD,MAAM,cAAc,IAAI,CAAC,WAAW;QAEpC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;QAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;QAE9B,MAAM,gBAAgB,MAAM,QAAQ,CAAC,UAAU;QAC/C,MAAM,eAAe,MAAM,QAAQ,CAAC,SAAS;QAE7C,IAAK,MAAM,QAAQ,CAAC,cAAc,KAAK,MAAO;YAE7C,MAAM,QAAQ,CAAC,cAAc,GAAG,IAAI,kJAAA,CAAA,SAAM;QAE3C;QAEA,+EAA+E;QAC/E,IAAM,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAI,GAAG,IAAO;YAEnD,IAAK,CAAE,IAAI,CAAC,YAAY,CAAE,IAAM;gBAE/B;YAED;YAEA,6BAA6B;YAC7B,MAAM,aAAa,QAAQ,CAAE,EAAG,CAAC,aAAa;YAE9C,MAAM,QAAQ,CAAC,UAAU,GAAG,WAAW,CAAE,WAAY;YAErD,IAAI,CAAC,WAAW,CAAE,GAAG,MAAM,WAAW,EAAG,WAAW,CAAE;YAEtD,IAAK,CAAE,MAAM,QAAQ,CAAC,UAAU,EAAG;gBAElC,IAAI,CAAC,gBAAgB,CAAE,YAAY,MAAM,QAAQ,CAAC,WAAW;gBAC7D,IAAI,CAAC,mBAAmB,CAAE,YAAY,MAAM,QAAQ,CAAC,cAAc;gBAEnE,MAAM,YAAY,UAAU,CAAE,WAAY;gBAC1C,MAAM,QAAQ,CAAC,YAAY,CAAE,UAAU,KAAK,EAAE,UAAU,KAAK;YAE9D;YAEA,MAAM,OAAO,CAAE,WAAW;YAE1B,IAAM,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAI,GAAG,IAAO;gBAE3D,MAAM,YAAY,gBAAgB,CAAE,EAAG;gBACvC,UAAU,MAAM,GAAG,IAAI;gBACvB,UAAU,OAAO,GAAG;gBACpB,WAAW,IAAI,CAAE;YAElB;YAEA,iBAAiB,MAAM,GAAG;QAE3B;QAEA,MAAM,QAAQ,CAAC,UAAU,GAAG;QAC5B,MAAM,QAAQ,CAAC,SAAS,GAAG;QAC3B,MAAM,QAAQ,GAAG;QACjB,MAAM,QAAQ,GAAG;IAElB,OAAO;QAEN,uBAAuB,IAAI,CAAE,IAAI,EAAE,WAAW;IAE/C;AAED;AAEA,SAAS,uBAAwB,SAAS,EAAE,UAAU;IAErD,IAAK,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAG;QAE/B,IAAK,IAAI,CAAC,QAAQ,KAAK,WAAY;QAEnC,iBAAiB,IAAI,CAAE,IAAI,CAAC,WAAW,EAAG,MAAM;QAChD,IAAI,IAAI,CAAE,UAAU,GAAG,EAAG,YAAY,CAAE;QAExC,YAAY,kBAAkB,CAAE,IAAI,CAAC,WAAW;QAChD,UAAU,IAAI,CAAE,IAAI,SAAS,EAAG,QAAQ,CAAE;QAE1C,MAAM,cAAc,UAAU,MAAM;QACpC,MAAM,OAAO,UAAU,IAAI,GAAG;QAC9B,MAAM,MAAM,UAAU,GAAG,GAAG;QAE5B,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU;QACpC,IAAK,UAAU,YAAY,KAAK,MAAO;YAEtC,MAAM,MAAM,CAAA,GAAA,wLAAA,CAAA,0BAAuB,AAAD,EAAG,IAAI,YAAY,CAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,MAAM,MAAO,IAAI,EAAE;YAC9F,IAAK,KAAM;gBAEV,WAAW,IAAI,CAAE;YAElB;QAED,OAAO;YAEN,MAAM,OAAO,IAAI,OAAO,CAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,MAAM;YACpD,IAAM,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAI,GAAG,IAAO;gBAE/C,MAAM,MAAM,CAAA,GAAA,wLAAA,CAAA,0BAAuB,AAAD,EAAG,IAAI,CAAE,EAAG,EAAE,IAAI,EAAE;gBACtD,IAAK,KAAM;oBAEV,WAAW,IAAI,CAAE;gBAElB;YAED;QAED;IAED,OAAO;QAEN,oBAAoB,IAAI,CAAE,IAAI,EAAE,WAAW;IAE5C;AAED;AAEO,SAAS,kBAAmB,UAAU,CAAC,CAAC;IAE9C,IAAI,CAAC,UAAU,GAAG,IAAI,iKAAA,CAAA,UAAO,CAAE,IAAI,EAAE;IACrC,OAAO,IAAI,CAAC,UAAU;AAEvB;AAEO,SAAS;IAEf,IAAI,CAAC,UAAU,GAAG;AAEnB;AAEO,SAAS,yBAA0B,QAAQ,CAAE,CAAC,EAAE,UAAU,CAAC,CAAC;IAElE,IAAK,CAAE,iBAAkB;QAExB,MAAM,IAAI,MAAO;IAElB;IAEA,IAAK,QAAQ,QAAQ,EAAG;QAEvB,QAAQ,IAAI,CAAE;IAEf;IAEA,UAAU;QACT,GAAG,OAAO;QACV,UAAU;QACV,OAAO;IACR;IAEA,MAAM,aAAa,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa;IACzD,MAAM,gBAAgB,IAAI,CAAC,cAAc;IACzC,IAAK,CAAE,IAAI,CAAC,WAAW,EAAG;QAEzB,IAAI,CAAC,WAAW,GAAG,IAAI,MAAO,eAAgB,IAAI,CAAE;IAErD;IAEA,MAAM,cAAc,IAAI,CAAC,WAAW;IACpC,MAAQ,YAAY,MAAM,GAAG,cAAgB;QAE5C,YAAY,IAAI,CAAE;IAEnB;IAEA,IAAK,QAAQ,GAAI;QAEhB,IAAM,IAAI,IAAI,GAAG,IAAI,eAAe,IAAO;YAE1C,QAAQ,KAAK,GAAG,UAAU,CAAE,EAAG;YAC/B,WAAW,CAAE,EAAG,GAAG,IAAI,iKAAA,CAAA,UAAO,CAAE,IAAI,CAAC,QAAQ,EAAE;QAEhD;QAEA,OAAO;IAER,OAAO;QAEN,IAAK,QAAQ,WAAW,MAAM,EAAG;YAEhC,QAAQ,KAAK,GAAG,UAAU,CAAE,MAAO;YACnC,WAAW,CAAE,MAAO,GAAG,IAAI,iKAAA,CAAA,UAAO,CAAE,IAAI,CAAC,QAAQ,EAAE;QAEpD;QAEA,OAAO,WAAW,CAAE,MAAO,IAAI;IAEhC;AAED;AAEO,SAAS,yBAA0B,QAAQ,CAAE,CAAC;IAEpD,IAAK,QAAQ,GAAI;QAEhB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAE;IAExB,OAAO;QAEN,IAAK,QAAQ,IAAI,CAAC,UAAU,CAAC,MAAM,EAAG;YAErC,IAAI,CAAC,WAAW,CAAE,MAAO,GAAG;QAE7B;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/gpu/VertexAttributeTexture.js"], "sourcesContent": ["import {\n\tDataTexture,\n\tFloatType,\n\tIntType,\n\tUnsignedIntType,\n\tByteType,\n\tUnsignedByteType,\n\tShortType,\n\tUnsignedShortType,\n\n\tRedFormat,\n\tRGFormat,\n\tRGBAFormat,\n\n\tRedIntegerFormat,\n\tRGIntegerFormat,\n\tRGBAIntegerFormat,\n\n\tNearestFilter,\n} from 'three';\n\nfunction countToStringFormat( count ) {\n\n\tswitch ( count ) {\n\n\t\tcase 1: return 'R';\n\t\tcase 2: return 'RG';\n\t\tcase 3: return 'RGBA';\n\t\tcase 4: return 'RGBA';\n\n\t}\n\n\tthrow new Error();\n\n}\n\nfunction countToFormat( count ) {\n\n\tswitch ( count ) {\n\n\t\tcase 1: return RedFormat;\n\t\tcase 2: return RGFormat;\n\t\tcase 3: return RGBAFormat;\n\t\tcase 4: return RGBAFormat;\n\n\t}\n\n}\n\nfunction countToIntFormat( count ) {\n\n\tswitch ( count ) {\n\n\t\tcase 1: return RedIntegerFormat;\n\t\tcase 2: return RGIntegerFormat;\n\t\tcase 3: return RGBAIntegerFormat;\n\t\tcase 4: return RGBAIntegerFormat;\n\n\t}\n\n}\n\nexport class VertexAttributeTexture extends DataTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis.minFilter = NearestFilter;\n\t\tthis.magFilter = NearestFilter;\n\t\tthis.generateMipmaps = false;\n\t\tthis.overrideItemSize = null;\n\t\tthis._forcedType = null;\n\n\t}\n\n\tupdateFrom( attr ) {\n\n\t\tconst overrideItemSize = this.overrideItemSize;\n\t\tconst originalItemSize = attr.itemSize;\n\t\tconst originalCount = attr.count;\n\t\tif ( overrideItemSize !== null ) {\n\n\t\t\tif ( ( originalItemSize * originalCount ) % overrideItemSize !== 0.0 ) {\n\n\t\t\t\tthrow new Error( 'VertexAttributeTexture: overrideItemSize must divide evenly into buffer length.' );\n\n\t\t\t}\n\n\t\t\tattr.itemSize = overrideItemSize;\n\t\t\tattr.count = originalCount * originalItemSize / overrideItemSize;\n\n\t\t}\n\n\t\tconst itemSize = attr.itemSize;\n\t\tconst count = attr.count;\n\t\tconst normalized = attr.normalized;\n\t\tconst originalBufferCons = attr.array.constructor;\n\t\tconst byteCount = originalBufferCons.BYTES_PER_ELEMENT;\n\t\tlet targetType = this._forcedType;\n\t\tlet finalStride = itemSize;\n\n\t\t// derive the type of texture this should be in the shader\n\t\tif ( targetType === null ) {\n\n\t\t\tswitch ( originalBufferCons ) {\n\n\t\t\t\tcase Float32Array:\n\t\t\t\t\ttargetType = FloatType;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase Uint8Array:\n\t\t\t\tcase Uint16Array:\n\t\t\t\tcase Uint32Array:\n\t\t\t\t\ttargetType = UnsignedIntType;\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase Int8Array:\n\t\t\t\tcase Int16Array:\n\t\t\t\tcase Int32Array:\n\t\t\t\t\ttargetType = IntType;\n\t\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// get the target format to store the texture as\n\t\tlet type, format, normalizeValue, targetBufferCons;\n\t\tlet internalFormat = countToStringFormat( itemSize );\n\t\tswitch ( targetType ) {\n\n\t\t\tcase FloatType:\n\t\t\t\tnormalizeValue = 1.0;\n\t\t\t\tformat = countToFormat( itemSize );\n\n\t\t\t\tif ( normalized && byteCount === 1 ) {\n\n\t\t\t\t\ttargetBufferCons = originalBufferCons;\n\t\t\t\t\tinternalFormat += '8';\n\n\t\t\t\t\tif ( originalBufferCons === Uint8Array ) {\n\n\t\t\t\t\t\ttype = UnsignedByteType;\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\ttype = ByteType;\n\t\t\t\t\t\tinternalFormat += '_SNORM';\n\n\t\t\t\t\t}\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttargetBufferCons = Float32Array;\n\t\t\t\t\tinternalFormat += '32F';\n\t\t\t\t\ttype = FloatType;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t\tcase IntType:\n\t\t\t\tinternalFormat += byteCount * 8 + 'I';\n\t\t\t\tnormalizeValue = normalized ? Math.pow( 2, originalBufferCons.BYTES_PER_ELEMENT * 8 - 1 ) : 1.0;\n\t\t\t\tformat = countToIntFormat( itemSize );\n\n\t\t\t\tif ( byteCount === 1 ) {\n\n\t\t\t\t\ttargetBufferCons = Int8Array;\n\t\t\t\t\ttype = ByteType;\n\n\t\t\t\t} else if ( byteCount === 2 ) {\n\n\t\t\t\t\ttargetBufferCons = Int16Array;\n\t\t\t\t\ttype = ShortType;\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttargetBufferCons = Int32Array;\n\t\t\t\t\ttype = IntType;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t\tcase UnsignedIntType:\n\t\t\t\tinternalFormat += byteCount * 8 + 'UI';\n\t\t\t\tnormalizeValue = normalized ? Math.pow( 2, originalBufferCons.BYTES_PER_ELEMENT * 8 - 1 ) : 1.0;\n\t\t\t\tformat = countToIntFormat( itemSize );\n\n\t\t\t\tif ( byteCount === 1 ) {\n\n\t\t\t\t\ttargetBufferCons = Uint8Array;\n\t\t\t\t\ttype = UnsignedByteType;\n\n\t\t\t\t} else if ( byteCount === 2 ) {\n\n\t\t\t\t\ttargetBufferCons = Uint16Array;\n\t\t\t\t\ttype = UnsignedShortType;\n\n\t\t\t\t} else {\n\n\t\t\t\t\ttargetBufferCons = Uint32Array;\n\t\t\t\t\ttype = UnsignedIntType;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\n\t\t}\n\n\t\t// there will be a mismatch between format length and final length because\n\t\t// RGBFormat and RGBIntegerFormat was removed\n\t\tif ( finalStride === 3 && ( format === RGBAFormat || format === RGBAIntegerFormat ) ) {\n\n\t\t\tfinalStride = 4;\n\n\t\t}\n\n\t\t// copy the data over to the new texture array\n\t\tconst dimension = Math.ceil( Math.sqrt( count ) ) || 1;\n\t\tconst length = finalStride * dimension * dimension;\n\t\tconst dataArray = new targetBufferCons( length );\n\n\t\t// temporarily set the normalized state to false since we have custom normalization logic\n\t\tconst originalNormalized = attr.normalized;\n\t\tattr.normalized = false;\n\t\tfor ( let i = 0; i < count; i ++ ) {\n\n\t\t\tconst ii = finalStride * i;\n\t\t\tdataArray[ ii ] = attr.getX( i ) / normalizeValue;\n\n\t\t\tif ( itemSize >= 2 ) {\n\n\t\t\t\tdataArray[ ii + 1 ] = attr.getY( i ) / normalizeValue;\n\n\t\t\t}\n\n\t\t\tif ( itemSize >= 3 ) {\n\n\t\t\t\tdataArray[ ii + 2 ] = attr.getZ( i ) / normalizeValue;\n\n\t\t\t\tif ( finalStride === 4 ) {\n\n\t\t\t\t\tdataArray[ ii + 3 ] = 1.0;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( itemSize >= 4 ) {\n\n\t\t\t\tdataArray[ ii + 3 ] = attr.getW( i ) / normalizeValue;\n\n\t\t\t}\n\n\t\t}\n\n\t\tattr.normalized = originalNormalized;\n\n\t\tthis.internalFormat = internalFormat;\n\t\tthis.format = format;\n\t\tthis.type = type;\n\t\tthis.image.width = dimension;\n\t\tthis.image.height = dimension;\n\t\tthis.image.data = dataArray;\n\t\tthis.needsUpdate = true;\n\t\tthis.dispose();\n\n\t\tattr.itemSize = originalItemSize;\n\t\tattr.count = originalCount;\n\n\t}\n\n}\n\nexport class UIntVertexAttributeTexture extends VertexAttributeTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis._forcedType = UnsignedIntType;\n\n\t}\n\n}\n\nexport class IntVertexAttributeTexture extends VertexAttributeTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis._forcedType = IntType;\n\n\t}\n\n\n}\n\nexport class FloatVertexAttributeTexture extends VertexAttributeTexture {\n\n\tconstructor() {\n\n\t\tsuper();\n\t\tthis._forcedType = FloatType;\n\n\t}\n\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAqBA,SAAS,oBAAqB,KAAK;IAElC,OAAS;QAER,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;IAEhB;IAEA,MAAM,IAAI;AAEX;AAEA,SAAS,cAAe,KAAK;IAE5B,OAAS;QAER,KAAK;YAAG,OAAO,kJAAA,CAAA,YAAS;QACxB,KAAK;YAAG,OAAO,kJAAA,CAAA,WAAQ;QACvB,KAAK;YAAG,OAAO,kJAAA,CAAA,aAAU;QACzB,KAAK;YAAG,OAAO,kJAAA,CAAA,aAAU;IAE1B;AAED;AAEA,SAAS,iBAAkB,KAAK;IAE/B,OAAS;QAER,KAAK;YAAG,OAAO,kJAAA,CAAA,mBAAgB;QAC/B,KAAK;YAAG,OAAO,kJAAA,CAAA,kBAAe;QAC9B,KAAK;YAAG,OAAO,kJAAA,CAAA,oBAAiB;QAChC,KAAK;YAAG,OAAO,kJAAA,CAAA,oBAAiB;IAEjC;AAED;AAEO,MAAM,+BAA+B,kJAAA,CAAA,cAAW;IAEtD,aAAc;QAEb,KAAK;QACL,IAAI,CAAC,SAAS,GAAG,kJAAA,CAAA,gBAAa;QAC9B,IAAI,CAAC,SAAS,GAAG,kJAAA,CAAA,gBAAa;QAC9B,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,WAAW,GAAG;IAEpB;IAEA,WAAY,IAAI,EAAG;QAElB,MAAM,mBAAmB,IAAI,CAAC,gBAAgB;QAC9C,MAAM,mBAAmB,KAAK,QAAQ;QACtC,MAAM,gBAAgB,KAAK,KAAK;QAChC,IAAK,qBAAqB,MAAO;YAEhC,IAAK,AAAE,mBAAmB,gBAAkB,qBAAqB,KAAM;gBAEtE,MAAM,IAAI,MAAO;YAElB;YAEA,KAAK,QAAQ,GAAG;YAChB,KAAK,KAAK,GAAG,gBAAgB,mBAAmB;QAEjD;QAEA,MAAM,WAAW,KAAK,QAAQ;QAC9B,MAAM,QAAQ,KAAK,KAAK;QACxB,MAAM,aAAa,KAAK,UAAU;QAClC,MAAM,qBAAqB,KAAK,KAAK,CAAC,WAAW;QACjD,MAAM,YAAY,mBAAmB,iBAAiB;QACtD,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,IAAI,cAAc;QAElB,0DAA0D;QAC1D,IAAK,eAAe,MAAO;YAE1B,OAAS;gBAER,KAAK;oBACJ,aAAa,kJAAA,CAAA,YAAS;oBACtB;gBAED,KAAK;gBACL,KAAK;gBACL,KAAK;oBACJ,aAAa,kJAAA,CAAA,kBAAe;oBAC5B;gBAED,KAAK;gBACL,KAAK;gBACL,KAAK;oBACJ,aAAa,kJAAA,CAAA,UAAO;oBACpB;YAEF;QAED;QAEA,gDAAgD;QAChD,IAAI,MAAM,QAAQ,gBAAgB;QAClC,IAAI,iBAAiB,oBAAqB;QAC1C,OAAS;YAER,KAAK,kJAAA,CAAA,YAAS;gBACb,iBAAiB;gBACjB,SAAS,cAAe;gBAExB,IAAK,cAAc,cAAc,GAAI;oBAEpC,mBAAmB;oBACnB,kBAAkB;oBAElB,IAAK,uBAAuB,YAAa;wBAExC,OAAO,kJAAA,CAAA,mBAAgB;oBAExB,OAAO;wBAEN,OAAO,kJAAA,CAAA,WAAQ;wBACf,kBAAkB;oBAEnB;gBAED,OAAO;oBAEN,mBAAmB;oBACnB,kBAAkB;oBAClB,OAAO,kJAAA,CAAA,YAAS;gBAEjB;gBAEA;YAED,KAAK,kJAAA,CAAA,UAAO;gBACX,kBAAkB,YAAY,IAAI;gBAClC,iBAAiB,aAAa,KAAK,GAAG,CAAE,GAAG,mBAAmB,iBAAiB,GAAG,IAAI,KAAM;gBAC5F,SAAS,iBAAkB;gBAE3B,IAAK,cAAc,GAAI;oBAEtB,mBAAmB;oBACnB,OAAO,kJAAA,CAAA,WAAQ;gBAEhB,OAAO,IAAK,cAAc,GAAI;oBAE7B,mBAAmB;oBACnB,OAAO,kJAAA,CAAA,YAAS;gBAEjB,OAAO;oBAEN,mBAAmB;oBACnB,OAAO,kJAAA,CAAA,UAAO;gBAEf;gBAEA;YAED,KAAK,kJAAA,CAAA,kBAAe;gBACnB,kBAAkB,YAAY,IAAI;gBAClC,iBAAiB,aAAa,KAAK,GAAG,CAAE,GAAG,mBAAmB,iBAAiB,GAAG,IAAI,KAAM;gBAC5F,SAAS,iBAAkB;gBAE3B,IAAK,cAAc,GAAI;oBAEtB,mBAAmB;oBACnB,OAAO,kJAAA,CAAA,mBAAgB;gBAExB,OAAO,IAAK,cAAc,GAAI;oBAE7B,mBAAmB;oBACnB,OAAO,kJAAA,CAAA,oBAAiB;gBAEzB,OAAO;oBAEN,mBAAmB;oBACnB,OAAO,kJAAA,CAAA,kBAAe;gBAEvB;gBAEA;QAEF;QAEA,0EAA0E;QAC1E,6CAA6C;QAC7C,IAAK,gBAAgB,KAAK,CAAE,WAAW,kJAAA,CAAA,aAAU,IAAI,WAAW,kJAAA,CAAA,oBAAiB,AAAC,GAAI;YAErF,cAAc;QAEf;QAEA,8CAA8C;QAC9C,MAAM,YAAY,KAAK,IAAI,CAAE,KAAK,IAAI,CAAE,WAAa;QACrD,MAAM,SAAS,cAAc,YAAY;QACzC,MAAM,YAAY,IAAI,iBAAkB;QAExC,yFAAyF;QACzF,MAAM,qBAAqB,KAAK,UAAU;QAC1C,KAAK,UAAU,GAAG;QAClB,IAAM,IAAI,IAAI,GAAG,IAAI,OAAO,IAAO;YAElC,MAAM,KAAK,cAAc;YACzB,SAAS,CAAE,GAAI,GAAG,KAAK,IAAI,CAAE,KAAM;YAEnC,IAAK,YAAY,GAAI;gBAEpB,SAAS,CAAE,KAAK,EAAG,GAAG,KAAK,IAAI,CAAE,KAAM;YAExC;YAEA,IAAK,YAAY,GAAI;gBAEpB,SAAS,CAAE,KAAK,EAAG,GAAG,KAAK,IAAI,CAAE,KAAM;gBAEvC,IAAK,gBAAgB,GAAI;oBAExB,SAAS,CAAE,KAAK,EAAG,GAAG;gBAEvB;YAED;YAEA,IAAK,YAAY,GAAI;gBAEpB,SAAS,CAAE,KAAK,EAAG,GAAG,KAAK,IAAI,CAAE,KAAM;YAExC;QAED;QAEA,KAAK,UAAU,GAAG;QAElB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;QACnB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QACpB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG;QAClB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,OAAO;QAEZ,KAAK,QAAQ,GAAG;QAChB,KAAK,KAAK,GAAG;IAEd;AAED;AAEO,MAAM,mCAAmC;IAE/C,aAAc;QAEb,KAAK;QACL,IAAI,CAAC,WAAW,GAAG,kJAAA,CAAA,kBAAe;IAEnC;AAED;AAEO,MAAM,kCAAkC;IAE9C,aAAc;QAEb,KAAK;QACL,IAAI,CAAC,WAAW,GAAG,kJAAA,CAAA,UAAO;IAE3B;AAGD;AAEO,MAAM,oCAAoC;IAEhD,aAAc;QAEb,KAAK;QACL,IAAI,CAAC,WAAW,GAAG,kJAAA,CAAA,YAAS;IAE7B;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/gpu/MeshBVHUniformStruct.js"], "sourcesContent": ["import {\n\tDataTexture,\n\tFloatType,\n\tUnsignedIntType,\n\tRGBAFormat,\n\tRGIntegerFormat,\n\tNearestFilter,\n\tBufferAttribute,\n} from 'three';\nimport {\n\tFloatVertexAttributeTexture,\n\tUIntVertexAttributeTexture,\n} from './VertexAttributeTexture.js';\nimport { BYTES_PER_NODE } from '../core/Constants.js';\nimport {\n\tBOUNDING_DATA_INDEX,\n\tCOUNT,\n\tIS_LEAF,\n\tRIGHT_NODE,\n\tOFFSET,\n\tSPLIT_AXIS,\n} from '../core/utils/nodeBufferUtils.js';\nimport { getIndexArray, getVertexCount } from '../core/build/geometryUtils.js';\n\nexport class MeshBVHUniformStruct {\n\n\tconstructor() {\n\n\t\tthis.index = new UIntVertexAttributeTexture();\n\t\tthis.position = new FloatVertexAttributeTexture();\n\t\tthis.bvhBounds = new DataTexture();\n\t\tthis.bvhContents = new DataTexture();\n\t\tthis._cachedIndexAttr = null;\n\n\t\tthis.index.overrideItemSize = 3;\n\n\t}\n\n\tupdateFrom( bvh ) {\n\n\t\tconst { geometry } = bvh;\n\t\tbvhToTextures( bvh, this.bvhBounds, this.bvhContents );\n\n\t\tthis.position.updateFrom( geometry.attributes.position );\n\n\t\t// dereference a new index attribute if we're using indirect storage\n\t\tif ( bvh.indirect ) {\n\n\t\t\tconst indirectBuffer = bvh._indirectBuffer;\n\t\t\tif (\n\t\t\t\tthis._cachedIndexAttr === null ||\n\t\t\t\tthis._cachedIndexAttr.count !== indirectBuffer.length\n\t\t\t) {\n\n\t\t\t\tif ( geometry.index ) {\n\n\t\t\t\t\tthis._cachedIndexAttr = geometry.index.clone();\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconst array = getIndexArray( getVertexCount( geometry ) );\n\t\t\t\t\tthis._cachedIndexAttr = new BufferAttribute( array, 1, false );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tdereferenceIndex( geometry, indirectBuffer, this._cachedIndexAttr );\n\t\t\tthis.index.updateFrom( this._cachedIndexAttr );\n\n\t\t} else {\n\n\t\t\tthis.index.updateFrom( geometry.index );\n\n\t\t}\n\n\t}\n\n\tdispose() {\n\n\t\tconst { index, position, bvhBounds, bvhContents } = this;\n\n\t\tif ( index ) index.dispose();\n\t\tif ( position ) position.dispose();\n\t\tif ( bvhBounds ) bvhBounds.dispose();\n\t\tif ( bvhContents ) bvhContents.dispose();\n\n\t}\n\n}\n\nfunction dereferenceIndex( geometry, indirectBuffer, target ) {\n\n\tconst unpacked = target.array;\n\tconst indexArray = geometry.index ? geometry.index.array : null;\n\tfor ( let i = 0, l = indirectBuffer.length; i < l; i ++ ) {\n\n\t\tconst i3 = 3 * i;\n\t\tconst v3 = 3 * indirectBuffer[ i ];\n\t\tfor ( let c = 0; c < 3; c ++ ) {\n\n\t\t\tunpacked[ i3 + c ] = indexArray ? indexArray[ v3 + c ] : v3 + c;\n\n\t\t}\n\n\t}\n\n}\n\nfunction bvhToTextures( bvh, boundsTexture, contentsTexture ) {\n\n\tconst roots = bvh._roots;\n\n\tif ( roots.length !== 1 ) {\n\n\t\tthrow new Error( 'MeshBVHUniformStruct: Multi-root BVHs not supported.' );\n\n\t}\n\n\tconst root = roots[ 0 ];\n\tconst uint16Array = new Uint16Array( root );\n\tconst uint32Array = new Uint32Array( root );\n\tconst float32Array = new Float32Array( root );\n\n\t// Both bounds need two elements per node so compute the height so it's twice as long as\n\t// the width so we can expand the row by two and still have a square texture\n\tconst nodeCount = root.byteLength / BYTES_PER_NODE;\n\tconst boundsDimension = 2 * Math.ceil( Math.sqrt( nodeCount / 2 ) );\n\tconst boundsArray = new Float32Array( 4 * boundsDimension * boundsDimension );\n\n\tconst contentsDimension = Math.ceil( Math.sqrt( nodeCount ) );\n\tconst contentsArray = new Uint32Array( 2 * contentsDimension * contentsDimension );\n\n\tfor ( let i = 0; i < nodeCount; i ++ ) {\n\n\t\tconst nodeIndex32 = i * BYTES_PER_NODE / 4;\n\t\tconst nodeIndex16 = nodeIndex32 * 2;\n\t\tconst boundsIndex = BOUNDING_DATA_INDEX( nodeIndex32 );\n\t\tfor ( let b = 0; b < 3; b ++ ) {\n\n\t\t\tboundsArray[ 8 * i + 0 + b ] = float32Array[ boundsIndex + 0 + b ];\n\t\t\tboundsArray[ 8 * i + 4 + b ] = float32Array[ boundsIndex + 3 + b ];\n\n\t\t}\n\n\t\tif ( IS_LEAF( nodeIndex16, uint16Array ) ) {\n\n\t\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\t\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\n\t\t\tconst mergedLeafCount = 0xffff0000 | count;\n\t\t\tcontentsArray[ i * 2 + 0 ] = mergedLeafCount;\n\t\t\tcontentsArray[ i * 2 + 1 ] = offset;\n\n\t\t} else {\n\n\t\t\tconst rightIndex = 4 * RIGHT_NODE( nodeIndex32, uint32Array ) / BYTES_PER_NODE;\n\t\t\tconst splitAxis = SPLIT_AXIS( nodeIndex32, uint32Array );\n\n\t\t\tcontentsArray[ i * 2 + 0 ] = splitAxis;\n\t\t\tcontentsArray[ i * 2 + 1 ] = rightIndex;\n\n\t\t}\n\n\t}\n\n\tboundsTexture.image.data = boundsArray;\n\tboundsTexture.image.width = boundsDimension;\n\tboundsTexture.image.height = boundsDimension;\n\tboundsTexture.format = RGBAFormat;\n\tboundsTexture.type = FloatType;\n\tboundsTexture.internalFormat = 'RGBA32F';\n\tboundsTexture.minFilter = NearestFilter;\n\tboundsTexture.magFilter = NearestFilter;\n\tboundsTexture.generateMipmaps = false;\n\tboundsTexture.needsUpdate = true;\n\tboundsTexture.dispose();\n\n\tcontentsTexture.image.data = contentsArray;\n\tcontentsTexture.image.width = contentsDimension;\n\tcontentsTexture.image.height = contentsDimension;\n\tcontentsTexture.format = RGIntegerFormat;\n\tcontentsTexture.type = UnsignedIntType;\n\tcontentsTexture.internalFormat = 'RG32UI';\n\tcontentsTexture.minFilter = NearestFilter;\n\tcontentsTexture.magFilter = NearestFilter;\n\tcontentsTexture.generateMipmaps = false;\n\tcontentsTexture.needsUpdate = true;\n\tcontentsTexture.dispose();\n\n}\n"], "names": [], "mappings": ";;;AAAA;AASA;AAIA;AACA;AAQA;;;;;;AAEO,MAAM;IAEZ,aAAc;QAEb,IAAI,CAAC,KAAK,GAAG,IAAI,+KAAA,CAAA,6BAA0B;QAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,+KAAA,CAAA,8BAA2B;QAC/C,IAAI,CAAC,SAAS,GAAG,IAAI,kJAAA,CAAA,cAAW;QAChC,IAAI,CAAC,WAAW,GAAG,IAAI,kJAAA,CAAA,cAAW;QAClC,IAAI,CAAC,gBAAgB,GAAG;QAExB,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG;IAE/B;IAEA,WAAY,GAAG,EAAG;QAEjB,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,cAAe,KAAK,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW;QAEpD,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAE,SAAS,UAAU,CAAC,QAAQ;QAEtD,oEAAoE;QACpE,IAAK,IAAI,QAAQ,EAAG;YAEnB,MAAM,iBAAiB,IAAI,eAAe;YAC1C,IACC,IAAI,CAAC,gBAAgB,KAAK,QAC1B,IAAI,CAAC,gBAAgB,CAAC,KAAK,KAAK,eAAe,MAAM,EACpD;gBAED,IAAK,SAAS,KAAK,EAAG;oBAErB,IAAI,CAAC,gBAAgB,GAAG,SAAS,KAAK,CAAC,KAAK;gBAE7C,OAAO;oBAEN,MAAM,QAAQ,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAG,CAAA,GAAA,gLAAA,CAAA,iBAAc,AAAD,EAAG;oBAC7C,IAAI,CAAC,gBAAgB,GAAG,IAAI,kJAAA,CAAA,kBAAe,CAAE,OAAO,GAAG;gBAExD;YAED;YAEA,iBAAkB,UAAU,gBAAgB,IAAI,CAAC,gBAAgB;YACjE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAE,IAAI,CAAC,gBAAgB;QAE7C,OAAO;YAEN,IAAI,CAAC,KAAK,CAAC,UAAU,CAAE,SAAS,KAAK;QAEtC;IAED;IAEA,UAAU;QAET,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,IAAI;QAExD,IAAK,OAAQ,MAAM,OAAO;QAC1B,IAAK,UAAW,SAAS,OAAO;QAChC,IAAK,WAAY,UAAU,OAAO;QAClC,IAAK,aAAc,YAAY,OAAO;IAEvC;AAED;AAEA,SAAS,iBAAkB,QAAQ,EAAE,cAAc,EAAE,MAAM;IAE1D,MAAM,WAAW,OAAO,KAAK;IAC7B,MAAM,aAAa,SAAS,KAAK,GAAG,SAAS,KAAK,CAAC,KAAK,GAAG;IAC3D,IAAM,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAI,GAAG,IAAO;QAEzD,MAAM,KAAK,IAAI;QACf,MAAM,KAAK,IAAI,cAAc,CAAE,EAAG;QAClC,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;YAE9B,QAAQ,CAAE,KAAK,EAAG,GAAG,aAAa,UAAU,CAAE,KAAK,EAAG,GAAG,KAAK;QAE/D;IAED;AAED;AAEA,SAAS,cAAe,GAAG,EAAE,aAAa,EAAE,eAAe;IAE1D,MAAM,QAAQ,IAAI,MAAM;IAExB,IAAK,MAAM,MAAM,KAAK,GAAI;QAEzB,MAAM,IAAI,MAAO;IAElB;IAEA,MAAM,OAAO,KAAK,CAAE,EAAG;IACvB,MAAM,cAAc,IAAI,YAAa;IACrC,MAAM,cAAc,IAAI,YAAa;IACrC,MAAM,eAAe,IAAI,aAAc;IAEvC,wFAAwF;IACxF,4EAA4E;IAC5E,MAAM,YAAY,KAAK,UAAU,GAAG,mKAAA,CAAA,iBAAc;IAClD,MAAM,kBAAkB,IAAI,KAAK,IAAI,CAAE,KAAK,IAAI,CAAE,YAAY;IAC9D,MAAM,cAAc,IAAI,aAAc,IAAI,kBAAkB;IAE5D,MAAM,oBAAoB,KAAK,IAAI,CAAE,KAAK,IAAI,CAAE;IAChD,MAAM,gBAAgB,IAAI,YAAa,IAAI,oBAAoB;IAE/D,IAAM,IAAI,IAAI,GAAG,IAAI,WAAW,IAAO;QAEtC,MAAM,cAAc,IAAI,mKAAA,CAAA,iBAAc,GAAG;QACzC,MAAM,cAAc,cAAc;QAClC,MAAM,cAAc,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAG;QACzC,IAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAO;YAE9B,WAAW,CAAE,IAAI,IAAI,IAAI,EAAG,GAAG,YAAY,CAAE,cAAc,IAAI,EAAG;YAClE,WAAW,CAAE,IAAI,IAAI,IAAI,EAAG,GAAG,YAAY,CAAE,cAAc,IAAI,EAAG;QAEnE;QAEA,IAAK,CAAA,GAAA,kLAAA,CAAA,UAAO,AAAD,EAAG,aAAa,cAAgB;YAE1C,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,QAAK,AAAD,EAAG,aAAa;YAClC,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAG,aAAa;YAEpC,MAAM,kBAAkB,aAAa;YACrC,aAAa,CAAE,IAAI,IAAI,EAAG,GAAG;YAC7B,aAAa,CAAE,IAAI,IAAI,EAAG,GAAG;QAE9B,OAAO;YAEN,MAAM,aAAa,IAAI,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAG,aAAa,eAAgB,mKAAA,CAAA,iBAAc;YAC9E,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAG,aAAa;YAE3C,aAAa,CAAE,IAAI,IAAI,EAAG,GAAG;YAC7B,aAAa,CAAE,IAAI,IAAI,EAAG,GAAG;QAE9B;IAED;IAEA,cAAc,KAAK,CAAC,IAAI,GAAG;IAC3B,cAAc,KAAK,CAAC,KAAK,GAAG;IAC5B,cAAc,KAAK,CAAC,MAAM,GAAG;IAC7B,cAAc,MAAM,GAAG,kJAAA,CAAA,aAAU;IACjC,cAAc,IAAI,GAAG,kJAAA,CAAA,YAAS;IAC9B,cAAc,cAAc,GAAG;IAC/B,cAAc,SAAS,GAAG,kJAAA,CAAA,gBAAa;IACvC,cAAc,SAAS,GAAG,kJAAA,CAAA,gBAAa;IACvC,cAAc,eAAe,GAAG;IAChC,cAAc,WAAW,GAAG;IAC5B,cAAc,OAAO;IAErB,gBAAgB,KAAK,CAAC,IAAI,GAAG;IAC7B,gBAAgB,KAAK,CAAC,KAAK,GAAG;IAC9B,gBAAgB,KAAK,CAAC,MAAM,GAAG;IAC/B,gBAAgB,MAAM,GAAG,kJAAA,CAAA,kBAAe;IACxC,gBAAgB,IAAI,GAAG,kJAAA,CAAA,kBAAe;IACtC,gBAAgB,cAAc,GAAG;IACjC,gBAAgB,SAAS,GAAG,kJAAA,CAAA,gBAAa;IACzC,gBAAgB,SAAS,GAAG,kJAAA,CAAA,gBAAa;IACzC,gBAAgB,eAAe,GAAG;IAClC,gBAAgB,WAAW,GAAG;IAC9B,gBAAgB,OAAO;AAExB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/gpu/glsl/bvh_struct_definitions.glsl.js"], "sourcesContent": ["// Note that a struct cannot be used for the hit record including faceIndices, faceNormal, barycoord,\n// side, and dist because on some mobile GPUS (such as Adreno) numbers are afforded less precision specifically\n// when in a struct leading to inaccurate hit results. See KhronosGroup/WebGL#3351 for more details.\nexport const bvh_struct_definitions = /* glsl */`\nstruct BVH {\n\n\tusampler2D index;\n\tsampler2D position;\n\n\tsampler2D bvhBounds;\n\tusampler2D bvhContents;\n\n};\n`;\n"], "names": [], "mappings": "AAAA,qGAAqG;AACrG,+GAA+G;AAC/G,oGAAoG;;;;AAC7F,MAAM,yBAAyB,QAAQ,GAAE,CAAC;;;;;;;;;;AAUjD,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4556, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/gpu/glsl/bvh_distance_functions.glsl.js"], "sourcesContent": ["// Distance to Point\nexport const bvh_distance_functions = /* glsl */`\n\nfloat dot2( vec3 v ) {\n\n\treturn dot( v, v );\n\n}\n\n// https://www.shadertoy.com/view/ttfGWl\nvec3 closestPointToTriangle( vec3 p, vec3 v0, vec3 v1, vec3 v2, out vec3 barycoord ) {\n\n    vec3 v10 = v1 - v0;\n    vec3 v21 = v2 - v1;\n    vec3 v02 = v0 - v2;\n\n\tvec3 p0 = p - v0;\n\tvec3 p1 = p - v1;\n\tvec3 p2 = p - v2;\n\n    vec3 nor = cross( v10, v02 );\n\n    // method 2, in barycentric space\n    vec3  q = cross( nor, p0 );\n    float d = 1.0 / dot2( nor );\n    float u = d * dot( q, v02 );\n    float v = d * dot( q, v10 );\n    float w = 1.0 - u - v;\n\n\tif( u < 0.0 ) {\n\n\t\tw = clamp( dot( p2, v02 ) / dot2( v02 ), 0.0, 1.0 );\n\t\tu = 0.0;\n\t\tv = 1.0 - w;\n\n\t} else if( v < 0.0 ) {\n\n\t\tu = clamp( dot( p0, v10 ) / dot2( v10 ), 0.0, 1.0 );\n\t\tv = 0.0;\n\t\tw = 1.0 - u;\n\n\t} else if( w < 0.0 ) {\n\n\t\tv = clamp( dot( p1, v21 ) / dot2( v21 ), 0.0, 1.0 );\n\t\tw = 0.0;\n\t\tu = 1.0-v;\n\n\t}\n\n\tbarycoord = vec3( u, v, w );\n    return u * v1 + v * v2 + w * v0;\n\n}\n\nfloat distanceToTriangles(\n\t// geometry info and triangle range\n\tsampler2D positionAttr, usampler2D indexAttr, uint offset, uint count,\n\n\t// point and cut off range\n\tvec3 point, float closestDistanceSquared,\n\n\t// outputs\n\tinout uvec4 faceIndices, inout vec3 faceNormal, inout vec3 barycoord, inout float side, inout vec3 outPoint\n) {\n\n\tbool found = false;\n\tvec3 localBarycoord;\n\tfor ( uint i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\tuvec3 indices = uTexelFetch1D( indexAttr, i ).xyz;\n\t\tvec3 a = texelFetch1D( positionAttr, indices.x ).rgb;\n\t\tvec3 b = texelFetch1D( positionAttr, indices.y ).rgb;\n\t\tvec3 c = texelFetch1D( positionAttr, indices.z ).rgb;\n\n\t\t// get the closest point and barycoord\n\t\tvec3 closestPoint = closestPointToTriangle( point, a, b, c, localBarycoord );\n\t\tvec3 delta = point - closestPoint;\n\t\tfloat sqDist = dot2( delta );\n\t\tif ( sqDist < closestDistanceSquared ) {\n\n\t\t\t// set the output results\n\t\t\tclosestDistanceSquared = sqDist;\n\t\t\tfaceIndices = uvec4( indices.xyz, i );\n\t\t\tfaceNormal = normalize( cross( a - b, b - c ) );\n\t\t\tbarycoord = localBarycoord;\n\t\t\toutPoint = closestPoint;\n\t\t\tside = sign( dot( faceNormal, delta ) );\n\n\t\t}\n\n\t}\n\n\treturn closestDistanceSquared;\n\n}\n\nfloat distanceSqToBounds( vec3 point, vec3 boundsMin, vec3 boundsMax ) {\n\n\tvec3 clampedPoint = clamp( point, boundsMin, boundsMax );\n\tvec3 delta = point - clampedPoint;\n\treturn dot( delta, delta );\n\n}\n\nfloat distanceSqToBVHNodeBoundsPoint( vec3 point, sampler2D bvhBounds, uint currNodeIndex ) {\n\n\tuint cni2 = currNodeIndex * 2u;\n\tvec3 boundsMin = texelFetch1D( bvhBounds, cni2 ).xyz;\n\tvec3 boundsMax = texelFetch1D( bvhBounds, cni2 + 1u ).xyz;\n\treturn distanceSqToBounds( point, boundsMin, boundsMax );\n\n}\n\n// use a macro to hide the fact that we need to expand the struct into separate fields\n#define\\\n\tbvhClosestPointToPoint(\\\n\t\tbvh,\\\n\t\tpoint, faceIndices, faceNormal, barycoord, side, outPoint\\\n\t)\\\n\t_bvhClosestPointToPoint(\\\n\t\tbvh.position, bvh.index, bvh.bvhBounds, bvh.bvhContents,\\\n\t\tpoint, faceIndices, faceNormal, barycoord, side, outPoint\\\n\t)\n\nfloat _bvhClosestPointToPoint(\n\t// bvh info\n\tsampler2D bvh_position, usampler2D bvh_index, sampler2D bvh_bvhBounds, usampler2D bvh_bvhContents,\n\n\t// point to check\n\tvec3 point,\n\n\t// output variables\n\tinout uvec4 faceIndices, inout vec3 faceNormal, inout vec3 barycoord,\n\tinout float side, inout vec3 outPoint\n ) {\n\n\t// stack needs to be twice as long as the deepest tree we expect because\n\t// we push both the left and right child onto the stack every traversal\n\tint ptr = 0;\n\tuint stack[ BVH_STACK_DEPTH ];\n\tstack[ 0 ] = 0u;\n\n\tfloat closestDistanceSquared = pow( 100000.0, 2.0 );\n\tbool found = false;\n\twhile ( ptr > - 1 && ptr < BVH_STACK_DEPTH ) {\n\n\t\tuint currNodeIndex = stack[ ptr ];\n\t\tptr --;\n\n\t\t// check if we intersect the current bounds\n\t\tfloat boundsHitDistance = distanceSqToBVHNodeBoundsPoint( point, bvh_bvhBounds, currNodeIndex );\n\t\tif ( boundsHitDistance > closestDistanceSquared ) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\tuvec2 boundsInfo = uTexelFetch1D( bvh_bvhContents, currNodeIndex ).xy;\n\t\tbool isLeaf = bool( boundsInfo.x & 0xffff0000u );\n\t\tif ( isLeaf ) {\n\n\t\t\tuint count = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint offset = boundsInfo.y;\n\t\t\tclosestDistanceSquared = distanceToTriangles(\n\t\t\t\tbvh_position, bvh_index, offset, count, point, closestDistanceSquared,\n\n\t\t\t\t// outputs\n\t\t\t\tfaceIndices, faceNormal, barycoord, side, outPoint\n\t\t\t);\n\n\t\t} else {\n\n\t\t\tuint leftIndex = currNodeIndex + 1u;\n\t\t\tuint splitAxis = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint rightIndex = boundsInfo.y;\n\t\t\tbool leftToRight = distanceSqToBVHNodeBoundsPoint( point, bvh_bvhBounds, leftIndex ) < distanceSqToBVHNodeBoundsPoint( point, bvh_bvhBounds, rightIndex );//rayDirection[ splitAxis ] >= 0.0;\n\t\t\tuint c1 = leftToRight ? leftIndex : rightIndex;\n\t\t\tuint c2 = leftToRight ? rightIndex : leftIndex;\n\n\t\t\t// set c2 in the stack so we traverse it later. We need to keep track of a pointer in\n\t\t\t// the stack while we traverse. The second pointer added is the one that will be\n\t\t\t// traversed first\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c2;\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c1;\n\n\t\t}\n\n\t}\n\n\treturn sqrt( closestDistanceSquared );\n\n}\n`;\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;AACb,MAAM,yBAAyB,QAAQ,GAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiMjD,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4760, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/gpu/glsl/common_functions.glsl.js"], "sourcesContent": ["export const common_functions = /* glsl */`\n\n// A stack of uint32 indices can can store the indices for\n// a perfectly balanced tree with a depth up to 31. Lower stack\n// depth gets higher performance.\n//\n// However not all trees are balanced. Best value to set this to\n// is the trees max depth.\n#ifndef BVH_STACK_DEPTH\n#define BVH_STACK_DEPTH 60\n#endif\n\n#ifndef INFINITY\n#define INFINITY 1e20\n#endif\n\n// Utilities\nuvec4 uTexelFetch1D( usampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nivec4 iTexelFetch1D( isampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nvec4 texelFetch1D( sampler2D tex, uint index ) {\n\n\tuint width = uint( textureSize( tex, 0 ).x );\n\tuvec2 uv;\n\tuv.x = index % width;\n\tuv.y = index / width;\n\n\treturn texelFetch( tex, ivec2( uv ), 0 );\n\n}\n\nvec4 textureSampleBarycoord( sampler2D tex, vec3 barycoord, uvec3 faceIndices ) {\n\n\treturn\n\t\tbarycoord.x * texelFetch1D( tex, faceIndices.x ) +\n\t\tbarycoord.y * texelFetch1D( tex, faceIndices.y ) +\n\t\tbarycoord.z * texelFetch1D( tex, faceIndices.z );\n\n}\n\nvoid ndcToCameraRay(\n\tvec2 coord, mat4 cameraWorld, mat4 invProjectionMatrix,\n\tout vec3 rayOrigin, out vec3 rayDirection\n) {\n\n\t// get camera look direction and near plane for camera clipping\n\tvec4 lookDirection = cameraWorld * vec4( 0.0, 0.0, - 1.0, 0.0 );\n\tvec4 nearVector = invProjectionMatrix * vec4( 0.0, 0.0, - 1.0, 1.0 );\n\tfloat near = abs( nearVector.z / nearVector.w );\n\n\t// get the camera direction and position from camera matrices\n\tvec4 origin = cameraWorld * vec4( 0.0, 0.0, 0.0, 1.0 );\n\tvec4 direction = invProjectionMatrix * vec4( coord, 0.5, 1.0 );\n\tdirection /= direction.w;\n\tdirection = cameraWorld * direction - origin;\n\n\t// slide the origin along the ray until it sits at the near clip plane position\n\torigin.xyz += direction.xyz * near / dot( direction, lookDirection );\n\n\trayOrigin = origin.xyz;\n\trayDirection = direction.xyz;\n\n}\n`;\n"], "names": [], "mappings": ";;;AAAO,MAAM,mBAAmB,QAAQ,GAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkF3C,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4852, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/gpu/glsl/bvh_ray_functions.glsl.js"], "sourcesContent": ["export const bvh_ray_functions = /* glsl */`\n\n#ifndef TRI_INTERSECT_EPSILON\n#define TRI_INTERSECT_EPSILON 1e-5\n#endif\n\n// Raycasting\nbool intersectsBounds( vec3 rayOrigin, vec3 rayDirection, vec3 boundsMin, vec3 boundsMax, out float dist ) {\n\n\t// https://www.reddit.com/r/opengl/comments/8ntzz5/fast_glsl_ray_box_intersection/\n\t// https://tavianator.com/2011/ray_box.html\n\tvec3 invDir = 1.0 / rayDirection;\n\n\t// find intersection distances for each plane\n\tvec3 tMinPlane = invDir * ( boundsMin - rayOrigin );\n\tvec3 tMaxPlane = invDir * ( boundsMax - rayOrigin );\n\n\t// get the min and max distances from each intersection\n\tvec3 tMinHit = min( tMaxPlane, tMinPlane );\n\tvec3 tMaxHit = max( tMaxPlane, tMinPlane );\n\n\t// get the furthest hit distance\n\tvec2 t = max( tMinHit.xx, tMinHit.yz );\n\tfloat t0 = max( t.x, t.y );\n\n\t// get the minimum hit distance\n\tt = min( tMaxHit.xx, tMaxHit.yz );\n\tfloat t1 = min( t.x, t.y );\n\n\t// set distance to 0.0 if the ray starts inside the box\n\tdist = max( t0, 0.0 );\n\n\treturn t1 >= dist;\n\n}\n\nbool intersectsTriangle(\n\tvec3 rayOrigin, vec3 rayDirection, vec3 a, vec3 b, vec3 c,\n\tout vec3 barycoord, out vec3 norm, out float dist, out float side\n) {\n\n\t// https://stackoverflow.com/questions/42740765/intersection-between-line-and-triangle-in-3d\n\tvec3 edge1 = b - a;\n\tvec3 edge2 = c - a;\n\tnorm = cross( edge1, edge2 );\n\n\tfloat det = - dot( rayDirection, norm );\n\tfloat invdet = 1.0 / det;\n\n\tvec3 AO = rayOrigin - a;\n\tvec3 DAO = cross( AO, rayDirection );\n\n\tvec4 uvt;\n\tuvt.x = dot( edge2, DAO ) * invdet;\n\tuvt.y = - dot( edge1, DAO ) * invdet;\n\tuvt.z = dot( AO, norm ) * invdet;\n\tuvt.w = 1.0 - uvt.x - uvt.y;\n\n\t// set the hit information\n\tbarycoord = uvt.wxy; // arranged in A, B, C order\n\tdist = uvt.z;\n\tside = sign( det );\n\tnorm = side * normalize( norm );\n\n\t// add an epsilon to avoid misses between triangles\n\tuvt += vec4( TRI_INTERSECT_EPSILON );\n\n\treturn all( greaterThanEqual( uvt, vec4( 0.0 ) ) );\n\n}\n\nbool intersectTriangles(\n\t// geometry info and triangle range\n\tsampler2D positionAttr, usampler2D indexAttr, uint offset, uint count,\n\n\t// ray\n\tvec3 rayOrigin, vec3 rayDirection,\n\n\t// outputs\n\tinout float minDistance, inout uvec4 faceIndices, inout vec3 faceNormal, inout vec3 barycoord,\n\tinout float side, inout float dist\n) {\n\n\tbool found = false;\n\tvec3 localBarycoord, localNormal;\n\tfloat localDist, localSide;\n\tfor ( uint i = offset, l = offset + count; i < l; i ++ ) {\n\n\t\tuvec3 indices = uTexelFetch1D( indexAttr, i ).xyz;\n\t\tvec3 a = texelFetch1D( positionAttr, indices.x ).rgb;\n\t\tvec3 b = texelFetch1D( positionAttr, indices.y ).rgb;\n\t\tvec3 c = texelFetch1D( positionAttr, indices.z ).rgb;\n\n\t\tif (\n\t\t\tintersectsTriangle( rayOrigin, rayDirection, a, b, c, localBarycoord, localNormal, localDist, localSide )\n\t\t\t&& localDist < minDistance\n\t\t) {\n\n\t\t\tfound = true;\n\t\t\tminDistance = localDist;\n\n\t\t\tfaceIndices = uvec4( indices.xyz, i );\n\t\t\tfaceNormal = localNormal;\n\n\t\t\tside = localSide;\n\t\t\tbarycoord = localBarycoord;\n\t\t\tdist = localDist;\n\n\t\t}\n\n\t}\n\n\treturn found;\n\n}\n\nbool intersectsBVHNodeBounds( vec3 rayOrigin, vec3 rayDirection, sampler2D bvhBounds, uint currNodeIndex, out float dist ) {\n\n\tuint cni2 = currNodeIndex * 2u;\n\tvec3 boundsMin = texelFetch1D( bvhBounds, cni2 ).xyz;\n\tvec3 boundsMax = texelFetch1D( bvhBounds, cni2 + 1u ).xyz;\n\treturn intersectsBounds( rayOrigin, rayDirection, boundsMin, boundsMax, dist );\n\n}\n\n// use a macro to hide the fact that we need to expand the struct into separate fields\n#define\\\n\tbvhIntersectFirstHit(\\\n\t\tbvh,\\\n\t\trayOrigin, rayDirection, faceIndices, faceNormal, barycoord, side, dist\\\n\t)\\\n\t_bvhIntersectFirstHit(\\\n\t\tbvh.position, bvh.index, bvh.bvhBounds, bvh.bvhContents,\\\n\t\trayOrigin, rayDirection, faceIndices, faceNormal, barycoord, side, dist\\\n\t)\n\nbool _bvhIntersectFirstHit(\n\t// bvh info\n\tsampler2D bvh_position, usampler2D bvh_index, sampler2D bvh_bvhBounds, usampler2D bvh_bvhContents,\n\n\t// ray\n\tvec3 rayOrigin, vec3 rayDirection,\n\n\t// output variables split into separate variables due to output precision\n\tinout uvec4 faceIndices, inout vec3 faceNormal, inout vec3 barycoord,\n\tinout float side, inout float dist\n) {\n\n\t// stack needs to be twice as long as the deepest tree we expect because\n\t// we push both the left and right child onto the stack every traversal\n\tint ptr = 0;\n\tuint stack[ BVH_STACK_DEPTH ];\n\tstack[ 0 ] = 0u;\n\n\tfloat triangleDistance = INFINITY;\n\tbool found = false;\n\twhile ( ptr > - 1 && ptr < BVH_STACK_DEPTH ) {\n\n\t\tuint currNodeIndex = stack[ ptr ];\n\t\tptr --;\n\n\t\t// check if we intersect the current bounds\n\t\tfloat boundsHitDistance;\n\t\tif (\n\t\t\t! intersectsBVHNodeBounds( rayOrigin, rayDirection, bvh_bvhBounds, currNodeIndex, boundsHitDistance )\n\t\t\t|| boundsHitDistance > triangleDistance\n\t\t) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\tuvec2 boundsInfo = uTexelFetch1D( bvh_bvhContents, currNodeIndex ).xy;\n\t\tbool isLeaf = bool( boundsInfo.x & 0xffff0000u );\n\n\t\tif ( isLeaf ) {\n\n\t\t\tuint count = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint offset = boundsInfo.y;\n\n\t\t\tfound = intersectTriangles(\n\t\t\t\tbvh_position, bvh_index, offset, count,\n\t\t\t\trayOrigin, rayDirection, triangleDistance,\n\t\t\t\tfaceIndices, faceNormal, barycoord, side, dist\n\t\t\t) || found;\n\n\t\t} else {\n\n\t\t\tuint leftIndex = currNodeIndex + 1u;\n\t\t\tuint splitAxis = boundsInfo.x & 0x0000ffffu;\n\t\t\tuint rightIndex = boundsInfo.y;\n\n\t\t\tbool leftToRight = rayDirection[ splitAxis ] >= 0.0;\n\t\t\tuint c1 = leftToRight ? leftIndex : rightIndex;\n\t\t\tuint c2 = leftToRight ? rightIndex : leftIndex;\n\n\t\t\t// set c2 in the stack so we traverse it later. We need to keep track of a pointer in\n\t\t\t// the stack while we traverse. The second pointer added is the one that will be\n\t\t\t// traversed first\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c2;\n\n\t\t\tptr ++;\n\t\t\tstack[ ptr ] = c1;\n\n\t\t}\n\n\t}\n\n\treturn found;\n\n}\n`;\n"], "names": [], "mappings": ";;;AAAO,MAAM,oBAAoB,QAAQ,GAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoN5C,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5074, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Dev/how%20r%20u/how-r-u-website/node_modules/three-mesh-bvh/src/index.js"], "sourcesContent": ["export { MeshBVH } from './core/MeshBVH.js';\nexport { MeshBVHHelper } from './objects/MeshBVHHelper.js';\nexport { CENTER, AVERAGE, SAH, NOT_INTERSECTED, INTERSECTED, CONTAINED } from './core/Constants.js';\nexport { getBVHExtremes, estimateMemoryInBytes, getJSONStructure, validateBounds } from './debug/Debug.js';\nexport * from './utils/ExtensionUtilities.js';\nexport { getTriangleHitPointInfo } from './utils/TriangleUtilities.js';\nexport * from './math/ExtendedTriangle.js';\nexport * from './math/OrientedBox.js';\nexport * from './gpu/MeshBVHUniformStruct.js';\nexport * from './gpu/VertexAttributeTexture.js';\nexport * from './utils/StaticGeometryGenerator.js';\nexport * as BVHShaderGLSL from './gpu/BVHShaderGLSL.js';\n\n// backwards compatibility\nimport * as BVHShaderGLSL from './gpu/BVHShaderGLSL.js';\nexport const shaderStructs = BVHShaderGLSL.bvh_struct_definitions;\nexport const shaderDistanceFunction = BVHShaderGLSL.bvh_distance_functions;\nexport const shaderIntersectFunction = `\n\t${ BVHShaderGLSL.common_functions }\n\t${ BVHShaderGLSL.bvh_ray_functions }\n`;\n"], "names": [], "mappings": ";;;;;AAaA,0BAA0B;AAC1B;AAAA;AAAA;AAAA;;;;;;;;;;;;;;AACO,MAAM,gBAAgB,+LAAA,CAAA,yBAAoC;AAC1D,MAAM,yBAAyB,+LAAA,CAAA,yBAAoC;AACnE,MAAM,0BAA0B,CAAC;CACvC,EAAG,yLAAA,CAAA,mBAA8B,CAAE;CACnC,EAAG,0LAAA,CAAA,oBAA+B,CAAE;AACrC,CAAC", "ignoreList": [0], "debugId": null}}]}